{"name": "trend-taker-nextjs", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/node": "^20.0.0", "@types/papaparse": "^5.3.14", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "exceljs": "^4.4.0", "lucide-react": "^0.292.0", "next": "^14.2.32", "papaparse": "^5.4.1", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "14.0.0"}}