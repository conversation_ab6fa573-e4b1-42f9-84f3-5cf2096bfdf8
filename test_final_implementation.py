#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finální test implementace - BUY_LIMIT_NOT_FILLED a SL_Calc
"""

import sys
import os
sys.path.append('.')

# Import TrendTaker třídy
sys.path.append('python-webgui')
exec(open('python-webgui/Trend Taker Web GUI.py').read())

def test_final_implementation():
    """Test finální implementace"""
    
    print("🧪 FINÁLNÍ TEST - BUY_LIMIT_NOT_FILLED & SL_CALC")
    print("=" * 80)
    
    # Vytvoření TrendTaker instance
    tt = TrendTaker()
    
    # Parametry testu
    test_params = {
        'filename': 'XAUUSD_GMT+2_US-DST_D1.csv',
        'start_date': '2025-08-19',
        'end_date': '2025-09-18',
        'variant': 'Tolerant',
        'rrr': 2.0,
        'exit_policy': 'cons',
        'start_equity': 10000,
        'risk_pct': 0.02,
        'overnight_mode': 'LONG-FLAT-LIMIT',
        'buy_limit_offset': 5,
        'min_body_pct': 5.0
    }
    
    print(f"📊 TESTOVACÍ PARAMETRY:")
    for key, value in test_params.items():
        print(f"   {key}: {value}")
    print()
    
    try:
        # Spuštění testu
        print("🚀 Spouštím test...")
        result = tt.run_test(
            test_params['filename'],
            test_params['start_date'],
            test_params['end_date'],
            test_params['variant'],
            test_params['rrr'],
            test_params['exit_policy'],
            test_params['start_equity'],
            test_params['risk_pct'],
            test_params['overnight_mode'],
            test_params['buy_limit_offset'],
            test_params['min_body_pct']
        )
        
        print("✅ Test dokončen!")
        print()
        
        # Analýza výsledků
        print("📋 ANALÝZA VÝSLEDKŮ:")
        print("=" * 80)
        
        if 'trades' in result:
            trades = result['trades']
            print(f"📊 Celkem obchodů: {len(trades)}")
            
            for i, trade in enumerate(trades, 1):
                print(f"\n🔹 OBCHOD {i}:")
                print(f"   Sequence_ID: {trade.get('Sequence_ID', 'N/A')}")
                print(f"   Direction: {trade.get('Direction', 'N/A')}")
                print(f"   Období: {trade.get('Start_Date', 'N/A')} - {trade.get('End_Date', 'N/A')}")
                print(f"   Entry: {trade.get('Entry_Price', 0):.2f}")
                print(f"   Exit: {trade.get('Exit_Price', 0):.2f}")
                print(f"   SL: {trade.get('Stop_Loss', 0):.2f}")
                print(f"   Exit_Reason: {trade.get('Exit_Reason', 'N/A')}")
                print(f"   SL_Calc: {trade.get('SL_Calc', 'N/A')}")
                print(f"   P&L: ${trade.get('Net_PnL', 0):.2f}")
                print(f"   Segments: {trade.get('Segments_Count', 'N/A')}")
                
                # Kontrola BUY_LIMIT_NOT_FILLED
                exit_reason = trade.get('Exit_Reason', '')
                if 'BUY_LIMIT_NOT_FILLED' in exit_reason:
                    print(f"   ✅ BUY_LIMIT_NOT_FILLED nalezen v Exit_Reason")
                elif 'BUY_LIMIT_FILLED' in exit_reason:
                    print(f"   ✅ BUY_LIMIT_FILLED nalezen v Exit_Reason")
                else:
                    print(f"   ⚠️  Žádná BUY_LIMIT informace v Exit_Reason")
                
                # Kontrola SL_Calc
                sl_calc = trade.get('SL_Calc', '')
                if sl_calc and sl_calc != 'N/A':
                    print(f"   ✅ SL_Calc implementován: {sl_calc[:50]}...")
                else:
                    print(f"   ❌ SL_Calc chybí nebo je prázdný")
                
                # Kontrola segmentů
                if 'Segments_Detail' in trade:
                    segments = trade['Segments_Detail']
                    print(f"   📋 Detailní segmenty: {len(segments)}")
                    
                    for j, segment in enumerate(segments[:3], 1):  # Jen první 3
                        print(f"      {j}. {segment.get('date', 'N/A')}: "
                              f"{segment.get('entry', 0):.2f} → {segment.get('exit', 0):.2f} "
                              f"({segment.get('pnl_bod', 0):+.2f} bodů, {segment.get('reason', 'N/A')})")
                        
                        # Kontrola SL_Calc v segmentech
                        seg_sl_calc = segment.get('sl_calc', '')
                        if seg_sl_calc:
                            print(f"         SL_Calc: {seg_sl_calc}")
                    
                    if len(segments) > 3:
                        print(f"      ... a dalších {len(segments) - 3} segmentů")
        
        # Kontrola Excel souboru
        excel_file = f"TrendTaker_WebGUI_{test_params['variant']}_{test_params['start_date']}_{test_params['end_date']}.xlsx"
        
        if os.path.exists(excel_file):
            print(f"\n📊 EXCEL SOUBOR:")
            print(f"✅ Soubor vytvořen: {excel_file}")
            
            # Načtení a kontrola Excel souboru
            try:
                import pandas as pd
                excel_data = pd.read_excel(excel_file, sheet_name=None)
                
                print(f"📋 Sheety: {list(excel_data.keys())}")
                
                # Kontrola obchodů
                if 'Obchody' in excel_data:
                    trades_df = excel_data['Obchody']
                    print(f"💹 Obchody: {len(trades_df)} řádků")
                    
                    if len(trades_df) > 0:
                        print(f"📊 Sloupce: {list(trades_df.columns)}")
                        
                        # Kontrola SL_Calc sloupce
                        if 'SL_Calc' in trades_df.columns:
                            print(f"✅ SL_Calc sloupec nalezen")
                            first_sl_calc = trades_df.iloc[0]['SL_Calc']
                            print(f"   První SL_Calc: {first_sl_calc}")
                        else:
                            print(f"❌ SL_Calc sloupec chybí")
                        
                        # Kontrola Exit_Reason
                        if 'Exit_Reason' in trades_df.columns:
                            first_exit_reason = trades_df.iloc[0]['Exit_Reason']
                            print(f"✅ Exit_Reason: {first_exit_reason}")
                            
                            if 'BUY_LIMIT' in first_exit_reason:
                                print(f"✅ BUY_LIMIT informace nalezena")
                            else:
                                print(f"⚠️  BUY_LIMIT informace nenalezena")
                        else:
                            print(f"❌ Exit_Reason sloupec chybí")
                
                # Kontrola detailních segmentů
                if 'Detailní_Segmenty' in excel_data:
                    segments_df = excel_data['Detailní_Segmenty']
                    print(f"🔍 Detailní segmenty: {len(segments_df)} řádků")
                    
                    if len(segments_df) > 0:
                        print(f"📊 Sloupce segmentů: {list(segments_df.columns)}")
                        
                        # Kontrola SL_Calc v segmentech
                        if 'SL_Calc' in segments_df.columns:
                            print(f"✅ SL_Calc v segmentech nalezen")
                        else:
                            print(f"❌ SL_Calc v segmentech chybí")
                
            except Exception as e:
                print(f"❌ Chyba při čtení Excel: {e}")
        else:
            print(f"❌ Excel soubor nenalezen: {excel_file}")
        
        # Finální hodnocení
        print(f"\n🎯 FINÁLNÍ HODNOCENÍ:")
        print("=" * 80)
        
        success_checks = []
        
        # Check 1: Obchody existují
        if 'trades' in result and len(result['trades']) > 0:
            success_checks.append("✅ Obchody vytvořeny")
        else:
            success_checks.append("❌ Žádné obchody")
        
        # Check 2: BUY_LIMIT_NOT_FILLED v Exit_Reason
        buy_limit_found = False
        if 'trades' in result:
            for trade in result['trades']:
                if 'BUY_LIMIT' in trade.get('Exit_Reason', ''):
                    buy_limit_found = True
                    break
        
        if buy_limit_found:
            success_checks.append("✅ BUY_LIMIT informace v Exit_Reason")
        else:
            success_checks.append("❌ BUY_LIMIT informace chybí")
        
        # Check 3: SL_Calc implementován
        sl_calc_found = False
        if 'trades' in result:
            for trade in result['trades']:
                if trade.get('SL_Calc') and trade.get('SL_Calc') != 'N/A':
                    sl_calc_found = True
                    break
        
        if sl_calc_found:
            success_checks.append("✅ SL_Calc implementován")
        else:
            success_checks.append("❌ SL_Calc chybí")
        
        # Check 4: Excel soubor vytvořen
        if os.path.exists(excel_file):
            success_checks.append("✅ Excel soubor vytvořen")
        else:
            success_checks.append("❌ Excel soubor chybí")
        
        # Výsledek
        success_count = len([check for check in success_checks if check.startswith("✅")])
        total_checks = len(success_checks)
        
        print(f"📊 VÝSLEDKY:")
        for check in success_checks:
            print(f"   {check}")
        
        print(f"\n📈 CELKOVÉ SKÓRE: {success_count}/{total_checks} ({success_count/total_checks*100:.0f}%)")
        
        if success_count == total_checks:
            print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
            print("✅ Implementace je připravena pro WebGUI")
            return True
        elif success_count >= total_checks * 0.75:
            print("✅ VĚTŠINA TESTŮ ÚSPĚŠNÁ")
            print("⚠️  Drobné problémy k dořešení")
            return False
        else:
            print("❌ VÝZNAMNÉ PROBLÉMY")
            print("🔧 Nutné další opravy")
            return False
    
    except Exception as e:
        print(f"❌ Chyba při testu: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_implementation()
    if success:
        print(f"\n🚀 PŘIPRAVENO PRO WEBGUI!")
    else:
        print(f"\n🔧 NUTNÉ DALŠÍ OPRAVY")
