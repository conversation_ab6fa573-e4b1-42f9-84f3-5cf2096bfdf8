#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test reálného SL hit scé<PERSON><PERSON><PERSON>
Simuluje situa<PERSON>, kdy SL skutečně hit na 26.08.2025
"""

import pandas as pd
import numpy as np

def test_real_sl_hit():
    """Test reálného SL hit scénáře"""
    
    print("🧪 TEST REÁLNÉHO SL HIT - 26.08.2025")
    print("=" * 80)
    
    # Simulace dat, kde SL skutečně hit na 26.08
    print("📊 SIMULACE REÁLNÉHO SL HIT SCÉNÁŘE:")
    print("=" * 80)
    
    # Data s SL hit na 26.08
    test_data = [
        {'Date': '2025-08-23', 'Open': 2556.58, 'High': 2569.35, 'Low': 2547.21, 'Close': 2567.37, 'Range': 22.14},
        {'Date': '2025-08-24', 'Open': 2568.40, 'High': 2583.77, 'Low': 2555.90, 'Close': 2573.46, 'Range': 27.87},
        {'Date': '2025-08-25', 'Open': 2578.14, 'High': 2603.45, 'Low': 2565.57, 'Close': 2591.15, 'Range': 37.88},
        # KRITICKÝ DEN - Low dosáhne SL
        {'Date': '2025-08-26', 'Open': 2593.98, 'High': 2618.17, 'Low': 2568.00, 'Close': 2580.00, 'Range': 50.17},  # Low snížen!
        {'Date': '2025-08-27', 'Open': 2608.61, 'High': 2629.43, 'Low': 2599.12, 'Close': 2625.16, 'Range': 30.31}
    ]
    
    df = pd.DataFrame(test_data)
    df['Date'] = pd.to_datetime(df['Date'])
    
    # Parametry
    entry = 2556.58
    initial_stop = 2528.47  # entry - ATR10 (28.11)
    tp = 2725.26
    buy_limit_offset = 5
    
    O = df['Open'].values
    H = df['High'].values
    L = df['Low'].values
    C = df['Close'].values
    
    print("📋 TESTOVACÍ DATA (s upraveným 26.08):")
    for _, row in df.iterrows():
        print(f"{row['Date'].strftime('%Y-%m-%d')}: O={row['Open']:.2f} H={row['High']:.2f} L={row['Low']:.2f} C={row['Close']:.2f}")
        if row['Date'].strftime('%Y-%m-%d') == '2025-08-26':
            print(f"   ⚠️  KRITICKÝ DEN: Low snížen na {row['Low']:.2f} pro test SL hit")
    print()
    
    print("🔧 SIMULACE SL HIT SCÉNÁŘE:")
    print("=" * 80)
    
    # Simulace s SL hit
    current_day = 0
    current_entry = entry
    current_stop = initial_stop
    position_active = True
    segments = []
    
    while current_day < len(df) and position_active:
        hi, lo, cl = H[current_day], L[current_day], C[current_day]
        date_str = df.iloc[current_day]['Date'].strftime('%Y-%m-%d')
        
        print(f"📅 DEN {current_day+1} ({date_str}):")
        print(f"   Current Entry: {current_entry:.2f}")
        print(f"   Current SL: {current_stop:.2f}")
        print(f"   OHLC: {O[current_day]:.2f}/{hi:.2f}/{lo:.2f}/{cl:.2f}")
        
        # Kontrola TP/SL během dne
        hit_tp = hi >= tp
        hit_sl = lo <= current_stop
        
        print(f"   TP check: {hi:.2f} >= {tp:.2f} = {hit_tp}")
        print(f"   SL check: {lo:.2f} <= {current_stop:.2f} = {hit_sl}")
        
        if hit_tp and hit_sl:
            exit_px = tp  # Optimistic
            reason = "TP (same-day)"
            position_active = False
            print(f"   ✅ TP a SL hit - exit na TP: {exit_px:.2f}")
        elif hit_tp:
            exit_px = tp
            reason = "TP"
            position_active = False
            print(f"   ✅ TP hit: {exit_px:.2f}")
        elif hit_sl:
            exit_px = current_stop
            reason = "SL"
            position_active = False
            print(f"   ❌ SL HIT! Exit na SL: {exit_px:.2f}")
            print(f"   🚨 POZICE UKONČENA NA SL!")
        else:
            # Pozice přežila den - uzavřeme na Close
            exit_px = cl
            if current_day == len(df) - 1:
                reason = "SEQ_END"
                position_active = False
                print(f"   📅 Konec sekvence - exit na Close: {exit_px:.2f}")
            else:
                reason = "EOD_CLOSE"
                print(f"   🌅 EOD Close: {exit_px:.2f}")
        
        # Výpočet P&L pro tento segment
        segment_pnl_bod = exit_px - current_entry
        
        print(f"   💰 Segment P&L: {segment_pnl_bod:.2f} bodů")
        
        segments.append({
            "day": current_day + 1,
            "date": date_str,
            "entry": current_entry,
            "exit": exit_px,
            "stop": current_stop,
            "pnl_bod": segment_pnl_bod,
            "reason": reason
        })
        
        if position_active and current_day < len(df) - 1:
            # Pokračujeme další den s Buy Limit
            next_day = current_day + 1
            if next_day < len(df):
                next_open = O[next_day]
                buy_limit_price = next_open - buy_limit_offset
                next_low = L[next_day]
                next_date = df.iloc[next_day]['Date'].strftime('%Y-%m-%d')
                
                print(f"   📋 Další den ({next_date}) Buy Limit setup:")
                print(f"      Next Open: {next_open:.2f}")
                print(f"      Buy Limit: {buy_limit_price:.2f} (Open - {buy_limit_offset})")
                print(f"      Next Low: {next_low:.2f}")
                
                if next_low <= buy_limit_price:
                    # Buy Limit se naplnil
                    current_entry = buy_limit_price
                    current_day = next_day
                    
                    # SL aktualizace
                    if current_day > 0:
                        prev_range = H[current_day-1] - L[current_day-1]
                        new_sl = current_entry - (prev_range * 0.5)
                        old_stop = current_stop
                        current_stop = max(current_stop, new_sl)  # Nová logika
                        
                        print(f"      🔄 SL AKTUALIZACE:")
                        print(f"         Předposlední range: {prev_range:.2f}")
                        print(f"         Nový SL výpočet: {new_sl:.2f}")
                        print(f"         Starý SL: {old_stop:.2f}")
                        print(f"         Finální SL: max({old_stop:.2f}, {new_sl:.2f}) = {current_stop:.2f}")
                        
                        if current_stop == old_stop:
                            print(f"         ✅ SL zůstal stejný")
                        else:
                            print(f"         ⬆️  SL se zvýšil o {current_stop - old_stop:.2f} bodů")
                    
                    print(f"      ✅ Buy Limit naplněn na {buy_limit_price:.2f}")
                else:
                    # Buy Limit se nenaplnil
                    position_active = False
                    print(f"      ❌ Buy Limit nenaplněn (Low {next_low:.2f} > Limit {buy_limit_price:.2f})")
            else:
                position_active = False
        else:
            position_active = False
        
        print()
        
        if not position_active:
            break
    
    # Analýza výsledků
    print("🔍 ANALÝZA SL HIT PROBLÉMU:")
    print("=" * 80)
    
    print("📋 DETAILNÍ SEGMENTY:")
    total_pnl = 0
    sl_hit_segment = None
    
    for i, seg in enumerate(segments, 1):
        total_pnl += seg['pnl_bod']
        print(f"Segment {i}: {seg['date']}")
        print(f"  Entry: {seg['entry']:.2f}, Exit: {seg['exit']:.2f}")
        print(f"  SL used: {seg['stop']:.2f}")
        print(f"  P&L: {seg['pnl_bod']:.2f} bodů")
        print(f"  Reason: {seg['reason']}")
        
        if seg['reason'] == 'SL':
            sl_hit_segment = seg
            print(f"  🚨 SL HIT SEGMENT!")
        print()
    
    print(f"📊 CELKOVÉ VÝSLEDKY:")
    print(f"Celkem segmentů: {len(segments)}")
    print(f"Celkový P&L: {total_pnl:.2f} bodů")
    print(f"Poslední exit: {segments[-1]['date']} na {segments[-1]['reason']}")
    
    if sl_hit_segment:
        print(f"\n🚨 SL HIT ANALÝZA:")
        print("=" * 80)
        print(f"❌ SL hit na: {sl_hit_segment['date']}")
        print(f"   Entry: {sl_hit_segment['entry']:.2f}")
        print(f"   SL: {sl_hit_segment['stop']:.2f}")
        print(f"   Exit: {sl_hit_segment['exit']:.2f}")
        print(f"   P&L: {sl_hit_segment['pnl_bod']:.2f} bodů")
        
        # Najdeme Low tohoto dne
        sl_day_data = df[df['Date'].dt.strftime('%Y-%m-%d') == sl_hit_segment['date']].iloc[0]
        print(f"   Low dne: {sl_day_data['Low']:.2f}")
        print(f"   SL vs Low: {sl_hit_segment['stop']:.2f} vs {sl_day_data['Low']:.2f}")
        print(f"   Rozdíl: {sl_day_data['Low'] - sl_hit_segment['stop']:.2f} bodů")
        
        if sl_day_data['Low'] <= sl_hit_segment['stop']:
            print(f"   ✅ SL hit je správný (Low <= SL)")
        else:
            print(f"   ❌ SL hit je chybný (Low > SL)")
    
    print(f"\n🎯 ZÁVĚR:")
    print("=" * 80)
    print("1. 🔍 SL hit na 26.08.2025 je způsoben tím, že Low dosáhl SL hodnoty")
    print("2. 📈 SL se zvyšuje kvůli Buy Limit aktualizaci (50% předposlední svíčky)")
    print("3. 🚨 Pokud je to problém, můžeme upravit SL aktualizační logiku")
    print("4. 📊 Excel nyní bude obsahovat detailní segmenty")
    
    print(f"\n💡 MOŽNÁ ŘEŠENÍ:")
    print("=" * 80)
    print("A) 🔒 Nezvyšovat SL - použít min(starý_SL, nový_SL)")
    print("B) 🎯 Použít jiný algoritmus pro SL aktualizaci")
    print("C) ⚙️  Přidat parametr pro kontrolu SL aktualizace")
    print("D) 📋 Ponechat současnou logiku (je správná pro trailing stop)")

if __name__ == "__main__":
    test_real_sl_hit()
