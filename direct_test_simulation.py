#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Přímý test simulující WebGUI funkcionalitu
Testuje BUY_LIMIT_NOT_FILLED a SL_Calc
"""

import sys
import os
import json
from datetime import datetime

# Přidání cesty k TrendTaker modulu
sys.path.append('python-webgui')

def simulate_webgui_test():
    """Simuluje test z WebGUI"""
    
    print("🚀 SIMULACE WEBGUI TESTU")
    print("=" * 80)
    
    # Parametry testu (stejné jako ve WebGUI)
    test_params = {
        'filename': 'XAUUSD_GMT+2_US-DST_D1.csv',
        'start_date': '2025-08-19',
        'end_date': '2025-09-18',
        'variant': 'Tolerant',
        'rrr': 2.0,
        'exit_policy': 'cons',
        'start_equity': 10000,
        'risk_pct': 0.02,
        'overnight_mode': 'LONG-FLAT-LIMIT',
        'buy_limit_offset': 5,
        'min_body_pct': 5.0
    }
    
    print("📊 TESTOVACÍ PARAMETRY:")
    for key, value in test_params.items():
        print(f"   {key}: {value}")
    print()
    
    try:
        # Import TrendTaker třídy
        print("📥 Načítání TrendTaker modulu...")
        
        # Načtení kódu přímo
        with open('python-webgui/Trend Taker Web GUI.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Vytvoření lokálního namespace
        local_namespace = {}
        exec(code, local_namespace)
        
        # Získání TrendTakerWebHandler třídy
        TrendTakerWebHandler = local_namespace['TrendTakerWebHandler']

        print("✅ TrendTakerWebHandler modul načten")

        # Vytvoření jednoduchého objektu s potřebnými konstantami
        class SimpleHandler:
            def __init__(self):
                self.USD_PER_BOD_PER_LOT = 100.0
                self.SPREAD_BOD = 0.15
                self.SWAP_LONG_BOD = -0.55237
                self.SWAP_SHORT_BOD = 0.29425
                self.WICK_RATIO_MAX = 0.18
                self.MIN_BODY_RATIO = 0.60
                self.ATR_PERIOD = 10

        tt = SimpleHandler()

        # Přiřazení metody run_trend_taker_test
        tt.run_trend_taker_test = TrendTakerWebHandler.run_trend_taker_test.__get__(tt)

        print("🚀 Spouštím test...")

        # Příprava parametrů pro run_trend_taker_test
        params = {
            'dataFile': test_params['filename'],
            'dataRange': 'CUSTOM',
            'startDate': test_params['start_date'],
            'endDate': test_params['end_date'],
            'variant': test_params['variant'],
            'rrr': test_params['rrr'],
            'exitPolicy': test_params['exit_policy'],
            'startEquity': test_params['start_equity'],
            'riskPct': test_params['risk_pct'],
            'overnightMode': test_params['overnight_mode'],
            'buyLimitOffset': test_params['buy_limit_offset'],
            'minBodyPct': test_params['min_body_pct']
        }

        # Spuštění testu
        result_messages, excel_file = tt.run_trend_taker_test(params)

        # Simulace výsledku
        result = {
            'trades': [],  # Bude naplněno z Excel souboru
            'messages': result_messages,
            'excel_file': excel_file
        }
        
        print("✅ Test dokončen!")
        print()

        # Výpis zpráv z testu
        print("📋 ZPRÁVY Z TESTU:")
        print("=" * 80)
        for msg in result_messages:
            print(msg.strip())

        print()

        # Analýza výsledků z Excel souboru
        print("📋 ANALÝZA VÝSLEDKŮ Z EXCEL:")
        print("=" * 80)

        buy_limit_found = False
        sl_calc_found = False

        # Načtení obchodů z Excel souboru
        if excel_file and os.path.exists(excel_file):
            try:
                import openpyxl
                wb = openpyxl.load_workbook(excel_file)

                if 'Obchody' in wb.sheetnames:
                    ws = wb['Obchody']
                    print(f"📊 Celkem obchodů v Excel: {ws.max_row - 1}")

                    if ws.max_row > 1:
                        # Najdeme sloupce
                        headers = {}
                        for col in range(1, ws.max_column + 1):
                            header = ws.cell(row=1, column=col).value
                            if header:
                                headers[header] = col

                        print(f"📋 Sloupce v Excel: {list(headers.keys())}")

                        # Analýza každého obchodu
                        for row in range(2, ws.max_row + 1):
                            print(f"\n🔹 OBCHOD {row-1}:")

                            # Základní informace
                            if 'Sequence_ID' in headers:
                                seq_id = ws.cell(row=row, column=headers['Sequence_ID']).value
                                print(f"   Sequence_ID: {seq_id}")

                            if 'Direction' in headers:
                                direction = ws.cell(row=row, column=headers['Direction']).value
                                print(f"   Direction: {direction}")

                            if 'Start_Date' in headers and 'End_Date' in headers:
                                start_date = ws.cell(row=row, column=headers['Start_Date']).value
                                end_date = ws.cell(row=row, column=headers['End_Date']).value
                                print(f"   Období: {start_date} - {end_date}")

                            # Exit_Reason kontrola
                            if 'Exit_Reason' in headers:
                                exit_reason = ws.cell(row=row, column=headers['Exit_Reason']).value
                                print(f"   Exit_Reason: {exit_reason}")

                                if exit_reason:
                                    if 'BUY_LIMIT_NOT_FILLED' in str(exit_reason):
                                        print(f"   ✅ BUY_LIMIT_NOT_FILLED nalezen!")
                                        buy_limit_found = True
                                    elif 'BUY_LIMIT_FILLED' in str(exit_reason):
                                        print(f"   ✅ BUY_LIMIT_FILLED nalezen!")
                                        buy_limit_found = True
                                    elif 'BUY_LIMIT' in str(exit_reason):
                                        print(f"   ⚠️  Jiná BUY_LIMIT varianta: {exit_reason}")
                                        buy_limit_found = True
                                    else:
                                        print(f"   ❌ Žádná BUY_LIMIT informace")
                                else:
                                    print(f"   ❌ Exit_Reason je prázdný")

                            # SL_Calc kontrola
                            if 'SL_Calc' in headers:
                                sl_calc = ws.cell(row=row, column=headers['SL_Calc']).value
                                if sl_calc and str(sl_calc).strip():
                                    print(f"   ✅ SL_Calc: {str(sl_calc)[:100]}...")
                                    sl_calc_found = True
                                else:
                                    print(f"   ❌ SL_Calc chybí nebo je prázdný")
                            else:
                                print(f"   ❌ SL_Calc sloupec nenalezen")

                            # P&L
                            if 'Net_PnL' in headers:
                                net_pnl = ws.cell(row=row, column=headers['Net_PnL']).value
                                print(f"   P&L: ${net_pnl:.2f}" if net_pnl else "   P&L: N/A")

                            # Segments
                            if 'Segments_Count' in headers:
                                segments = ws.cell(row=row, column=headers['Segments_Count']).value
                                print(f"   Segments: {segments}")
                    else:
                        print("❌ Žádné obchody v Excel")
                else:
                    print("❌ Sheet 'Obchody' nenalezen")

                wb.close()

            except Exception as e:
                print(f"❌ Chyba při čtení Excel: {e}")
        else:
            print("❌ Excel soubor nenalezen nebo nevytvořen")
        
        # Kontrola Excel souboru
        excel_file = f"TrendTaker_WebGUI_{test_params['variant']}_{test_params['start_date']}_{test_params['end_date']}.xlsx"
        
        print(f"\n📊 KONTROLA EXCEL SOUBORU:")
        print("=" * 80)
        
        if os.path.exists(excel_file):
            print(f"✅ Excel soubor vytvořen: {excel_file}")
            
            # Kontrola obsahu Excel souboru
            try:
                import openpyxl
                wb = openpyxl.load_workbook(excel_file)
                
                print(f"📋 Sheety: {wb.sheetnames}")
                
                # Kontrola obchodů v Excel
                if 'Obchody' in wb.sheetnames:
                    ws = wb['Obchody']
                    print(f"💹 Obchody v Excel: {ws.max_row - 1} řádků")
                    
                    if ws.max_row > 1:
                        # Najdeme Exit_Reason sloupec
                        exit_reason_col = None
                        sl_calc_col = None
                        
                        for col in range(1, ws.max_column + 1):
                            header = ws.cell(row=1, column=col).value
                            if header == 'Exit_Reason':
                                exit_reason_col = col
                            elif header == 'SL_Calc':
                                sl_calc_col = col
                        
                        # Kontrola Exit_Reason v Excel
                        if exit_reason_col:
                            exit_reason_excel = ws.cell(row=2, column=exit_reason_col).value
                            print(f"📊 Exit_Reason v Excel: {exit_reason_excel}")
                            
                            if exit_reason_excel and 'BUY_LIMIT' in str(exit_reason_excel):
                                print(f"✅ BUY_LIMIT informace v Excel nalezena!")
                            else:
                                print(f"❌ BUY_LIMIT informace v Excel chybí!")
                        else:
                            print(f"❌ Exit_Reason sloupec v Excel nenalezen")
                        
                        # Kontrola SL_Calc v Excel
                        if sl_calc_col:
                            sl_calc_excel = ws.cell(row=2, column=sl_calc_col).value
                            print(f"📊 SL_Calc v Excel: {sl_calc_excel}")
                            
                            if sl_calc_excel:
                                print(f"✅ SL_Calc v Excel nalezen!")
                            else:
                                print(f"❌ SL_Calc v Excel je prázdný")
                        else:
                            print(f"❌ SL_Calc sloupec v Excel nenalezen")
                
                wb.close()
                
            except Exception as e:
                print(f"❌ Chyba při čtení Excel: {e}")
        else:
            print(f"❌ Excel soubor nenalezen: {excel_file}")
        
        # Finální hodnocení
        print(f"\n🎯 FINÁLNÍ HODNOCENÍ:")
        print("=" * 80)
        
        success_checks = []
        
        # Check 1: Test proběhl
        if 'trades' in result:
            success_checks.append("✅ Test proběhl úspěšně")
        else:
            success_checks.append("❌ Test selhal")
        
        # Check 2: Obchody existují
        if 'trades' in result and len(result['trades']) > 0:
            success_checks.append("✅ Obchody vytvořeny")
        else:
            success_checks.append("❌ Žádné obchody")
        
        # Check 3: BUY_LIMIT informace
        if buy_limit_found:
            success_checks.append("✅ BUY_LIMIT informace nalezena")
        else:
            success_checks.append("❌ BUY_LIMIT informace chybí")
        
        # Check 4: SL_Calc
        if sl_calc_found:
            success_checks.append("✅ SL_Calc implementován")
        else:
            success_checks.append("❌ SL_Calc chybí")
        
        # Check 5: Excel soubor
        if os.path.exists(excel_file):
            success_checks.append("✅ Excel soubor vytvořen")
        else:
            success_checks.append("❌ Excel soubor chybí")
        
        # Výsledek
        success_count = len([check for check in success_checks if check.startswith("✅")])
        total_checks = len(success_checks)
        
        print(f"📊 VÝSLEDKY:")
        for check in success_checks:
            print(f"   {check}")
        
        print(f"\n📈 CELKOVÉ SKÓRE: {success_count}/{total_checks} ({success_count/total_checks*100:.0f}%)")
        
        if success_count == total_checks:
            print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
            print("✅ BUY_LIMIT_NOT_FILLED a SL_Calc fungují správně")
        elif success_count >= total_checks * 0.8:
            print("✅ VĚTŠINA TESTŮ ÚSPĚŠNÁ")
            print("⚠️  Drobné problémy k dořešení")
        else:
            print("❌ VÝZNAMNÉ PROBLÉMY")
            print("🔧 Nutné další opravy")
    
    except Exception as e:
        print(f"❌ Chyba při testu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simulate_webgui_test()
