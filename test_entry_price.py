#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test správného Entry Price - měl by brát Open svíčky kde sekvence začíná
"""

import urllib.request
import json
import time

def test_entry_price():
    """Testuje správný Entry Price pro LONG sekvenci 20.8.-3.9.2025"""
    
    print("🎯 TEST ENTRY PRICE")
    print("=" * 80)
    print("Očekávaný Entry Price: 3349.098 (Open 21.8.2025)")
    print("Předchozí chybný Entry: 3338.405 (Close 20.8. nebo Open 22.8.)")
    print()
    
    # Parametry pro test
    params = {
        'dataFile': 'XAUUSD_GMT+2_US-DST_D1.csv',
        'dataRange': 'CUSTOM',
        'startDate': '2025-08-20',
        'endDate': '2025-09-05',
        'variant': 'Tolerant',
        'rrr': 6,
        'exitPolicy': 'Optimistic',
        'overnightMode': 'STANDARD',
        'startEquity': 10000,
        'riskPct': 2.0
    }
    
    # POST požadavek
    url = 'http://localhost:8080/run_test'
    data = json.dumps(params).encode('utf-8')
    
    try:
        print("🚀 Spouštím test...")
        req = urllib.request.Request(url, data=data, headers={'Content-Type': 'application/json'})
        with urllib.request.urlopen(req, timeout=120) as response:
            content = response.read().decode('utf-8')
            
            # Parsování JSON odpovědi
            response_data = json.loads(content)
            if response_data.get('success'):
                results_text = response_data.get('results', '')
                
                print("✅ Test dokončen, hledám Entry Price...")
                
                # Hledání Entry Price v odpovědi
                lines = results_text.split('\n')
                found_entry = False
                
                for line in lines:
                    if 'Entry_Price' in line and '3' in line:
                        print(f"📊 Nalezen Entry Price: {line.strip()}")
                        found_entry = True
                        
                        # Extrakce číselné hodnoty
                        if '3349' in line:
                            print("✅ SPRÁVNĚ! Entry Price obsahuje 3349 (Open 21.8.)")
                        elif '3338' in line:
                            print("❌ CHYBA! Entry Price obsahuje 3338 (Close 20.8. nebo Open 22.8.)")
                        else:
                            print("⚠️  Entry Price má neočekávanou hodnotu")
                        break
                
                if not found_entry:
                    print("⚠️  Entry Price nebyl nalezen v odpovědi")
                    print("Hledám v TOP5 obchodech...")
                    
                    # Hledání v TOP5 sekcích
                    in_top5 = False
                    for line in lines:
                        if 'TOP5' in line or 'LONG' in line or 'SHORT' in line:
                            if '3349' in line:
                                print(f"✅ SPRÁVNĚ! Nalezen v TOP5: {line.strip()}")
                                found_entry = True
                                break
                            elif '3338' in line:
                                print(f"❌ CHYBA! Nalezen v TOP5: {line.strip()}")
                                found_entry = True
                                break
                
                if not found_entry:
                    print("❌ Entry Price nebyl nalezen nikde v odpovědi")
                    print("Prvních 2000 znaků odpovědi:")
                    print(results_text[:2000])
                    
            else:
                print(f"❌ Test selhal: {response_data.get('error', 'Neznámá chyba')}")
                
    except Exception as e:
        print(f"❌ Chyba při HTTP požadavku: {e}")

def main():
    print("🎯 TEST SPRÁVNÉHO ENTRY PRICE")
    print("=" * 80)
    print("Testuje, že Entry Price bere Open svíčky kde sekvence začíná")
    print()
    
    print("📊 OČEKÁVANÁ DATA pro LONG sekvenci 20.8.-3.9.2025:")
    print("20.8.2025: Green Marubozu (signál)")
    print("21.8.2025: Entry den - Open = 3349.098 ← SPRÁVNÝ Entry Price")
    print("22.8.2025: Pokračování - Open = 3338.405 ← CHYBNÝ Entry Price")
    print()
    
    test_entry_price()
    
    print(f"\n🎯 ZÁVĚR:")
    print("Pokud Entry Price = 3349.098 → ✅ OPRAVA FUNGUJE")
    print("Pokud Entry Price = 3338.405 → ❌ STÁLE CHYBA")

if __name__ == "__main__":
    main()
