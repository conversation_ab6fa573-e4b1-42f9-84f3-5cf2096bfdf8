#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test ukládání obcho<PERSON> do Excel souboru
O<PERSON>, že LONG-FLAT-LIMIT obchody se správně ukládají jako jed<PERSON>liv<PERSON> záznamy
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

# Simulace WebGUI třídy pro testování
class TestExcelTrades:
    def __init__(self):
        self.USD_PER_BOD_PER_LOT = 100.0
        self.SPREAD_BOD = 0.15
        self.SWAP_LONG_BOD = -0.55237
        self.SWAP_SHORT_BOD = 0.29425
    
    def simulate_test_trades(self):
        """Simuluje několik obchodů pro testování Excel výstupu"""
        
        trades = []
        equity = 10000
        
        # Trade 1: LONG-FLAT-LIMIT (konsolidovaný)
        trade1 = {
            "Start_Date": "2025-08-20",
            "End_Date": "2025-08-22",
            "Direction": "Long",
            "Lots": 1.0,
            "ATR10": 50.0,
            "Entry_Price": 3315.47,
            "Exit_Price": 3370.25,
            "Stop_Loss": 3265.47,
            "Take_Profit": 3615.47,
            "Exit_Reason": "EOD_CLOSE -> BUY_LIMIT_FILLED -> SEQ_END",
            "Spread_Cost": 45.0,  # 3x spread (3 segmenty)
            "Swaps_USD": 0.0,
            "PnL_Bod": 54.78,
            "PnL_USD": 5478.0,
            "Net_PnL": 5433.0,
            "Equity_After": 15433.0
        }
        trades.append(trade1)
        equity = trade1["Equity_After"]
        
        # Trade 2: SHORT standardní
        trade2 = {
            "Start_Date": "2025-08-25",
            "End_Date": "2025-08-27",
            "Direction": "Short",
            "Lots": 1.5,
            "ATR10": 45.0,
            "Entry_Price": 3400.0,
            "Exit_Price": 3355.0,
            "Stop_Loss": 3445.0,
            "Take_Profit": 3130.0,
            "Exit_Reason": "TP",
            "Spread_Cost": 22.5,
            "Swaps_USD": -33.15,  # 2 dny swapu
            "PnL_Bod": 45.0,
            "PnL_USD": 6750.0,
            "Net_PnL": 6694.35,
            "Equity_After": 22127.35
        }
        trades.append(trade2)
        equity = trade2["Equity_After"]
        
        # Trade 3: LONG-FLAT-LIMIT (Buy Limit nenaplněn)
        trade3 = {
            "Start_Date": "2025-09-01",
            "End_Date": "2025-09-01",
            "Direction": "Long",
            "Lots": 2.0,
            "ATR10": 48.0,
            "Entry_Price": 3500.0,
            "Exit_Price": 3520.0,
            "Stop_Loss": 3452.0,
            "Take_Profit": 3788.0,
            "Exit_Reason": "EOD_CLOSE -> BUY_LIMIT_NOT_FILLED",
            "Spread_Cost": 30.0,
            "Swaps_USD": 0.0,
            "PnL_Bod": 20.0,
            "PnL_USD": 4000.0,
            "Net_PnL": 3970.0,
            "Equity_After": 26097.35
        }
        trades.append(trade3)
        
        return trades
    
    def create_test_excel_data(self):
        """Vytvoří testovací data pro Excel"""
        
        trades = self.simulate_test_trades()
        
        print("🧪 TESTOVACÍ OBCHODY PRO EXCEL:")
        print("=" * 60)
        
        for i, trade in enumerate(trades, 1):
            print(f"Trade {i}: {trade['Direction']} {trade['Start_Date']} - {trade['End_Date']}")
            print(f"   Entry: {trade['Entry_Price']:.2f} -> Exit: {trade['Exit_Price']:.2f}")
            print(f"   P&L: {trade['PnL_Bod']:.2f} bodů = ${trade['Net_PnL']:.2f}")
            print(f"   Exit Reason: {trade['Exit_Reason']}")
            print(f"   Equity After: ${trade['Equity_After']:.2f}")
            print()
        
        # Simulace sekvencí
        seqs_data = [
            {
                "Start_Date": "2025-08-20",
                "End_Date": "2025-08-22", 
                "Direction": "Up",
                "Length": 3,
                "Start_Idx": 0,
                "End_Idx": 2
            },
            {
                "Start_Date": "2025-08-25",
                "End_Date": "2025-08-27",
                "Direction": "Down", 
                "Length": 3,
                "Start_Idx": 5,
                "End_Idx": 7
            },
            {
                "Start_Date": "2025-09-01",
                "End_Date": "2025-09-01",
                "Direction": "Up",
                "Length": 1, 
                "Start_Idx": 12,
                "End_Idx": 12
            }
        ]
        
        # Simulace denních dat
        days_data = []
        for i in range(15):
            date = pd.Timestamp("2025-08-20") + pd.Timedelta(days=i)
            days_data.append({
                "Date": date,
                "Open": 3300 + i * 5,
                "High": 3320 + i * 5,
                "Low": 3290 + i * 5,
                "Close": 3310 + i * 5,
                "Color": "Green" if i % 2 == 0 else "Red",
                "Is_Marubozu": i % 3 == 0,
                "Series_Order": i % 4,
                "Direction": "Up" if i % 2 == 0 else "Down",
                "ATR10_s1": 45.0 + i,
                "Tolerated_Opposite": False
            })
        
        return trades, seqs_data, days_data
    
    def analyze_excel_structure(self):
        """Analyzuje strukturu Excel souboru"""
        
        trades, seqs, days = self.create_test_excel_data()
        
        print("📊 ANALÝZA EXCEL STRUKTURY:")
        print("=" * 60)
        
        print(f"✅ Počet obchodů: {len(trades)}")
        print(f"✅ Počet sekvencí: {len(seqs)}")
        print(f"✅ Počet denních záznamů: {len(days)}")
        print()
        
        print("📋 OČEKÁVANÉ EXCEL SHEETY:")
        print("-" * 40)
        print("1. 'Obchody' - seznam všech obchodů")
        print("2. 'Denní_Analýza' - denní data s Marubozu")
        print("3. 'Sekvence' - nalezené sekvence")
        print("4. 'Parametry' - nastavení testu")
        print()
        
        print("🔍 KLÍČOVÉ SLOUPCE V 'Obchody':")
        print("-" * 40)
        for key in trades[0].keys():
            print(f"   • {key}")
        print()
        
        print("🎯 LONG-FLAT-LIMIT SPECIFIKA:")
        print("-" * 40)
        print("   • Exit_Reason obsahuje 'BUY_LIMIT_FILLED' nebo 'BUY_LIMIT_NOT_FILLED'")
        print("   • Swaps_USD = 0.0 pro Long pozice")
        print("   • Spread_Cost může být vyšší (více segmentů)")
        print("   • Net_PnL = PnL_USD - Spread_Cost (bez swapů)")
        print()
        
        print("📈 TOP 5 OBCHODY (podle Net_PnL):")
        print("-" * 40)
        sorted_trades = sorted(trades, key=lambda x: x['Net_PnL'], reverse=True)
        for i, trade in enumerate(sorted_trades[:5], 1):
            print(f"{i}. {trade['Direction']} {trade['Start_Date']}: ${trade['Net_PnL']:.2f}")
        print()
        
        # Výpočet celkových metrik
        total_pnl = sum(t['Net_PnL'] for t in trades)
        win_trades = [t for t in trades if t['Net_PnL'] > 0]
        loss_trades = [t for t in trades if t['Net_PnL'] < 0]
        
        win_rate = len(win_trades) / len(trades) * 100 if trades else 0
        gross_profit = sum(t['Net_PnL'] for t in win_trades)
        gross_loss = abs(sum(t['Net_PnL'] for t in loss_trades))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        print("📊 CELKOVÉ METRIKY:")
        print("-" * 40)
        print(f"   • Celkový P&L: ${total_pnl:.2f}")
        print(f"   • Win Rate: {win_rate:.1f}%")
        print(f"   • Profit Factor: {profit_factor:.2f}")
        print(f"   • Počet výherních: {len(win_trades)}")
        print(f"   • Počet ztrátových: {len(loss_trades)}")
        print()
        
        return trades, seqs, days

def test_excel_trades():
    """Test Excel obchodů"""
    
    print("🧪 TEST EXCEL OBCHODŮ - LONG-FLAT-LIMIT")
    print("=" * 60)
    
    tester = TestExcelTrades()
    trades, seqs, days = tester.analyze_excel_structure()
    
    print("🎯 KONTROLNÍ BODY PRO WEBGUI TEST:")
    print("=" * 60)
    print("1. ✅ Spusťte test na http://localhost:8080")
    print("2. ✅ Nastavte LONG-FLAT-LIMIT mode")
    print("3. ✅ Spusťte test a stáhněte Excel")
    print("4. ✅ Otevřete sheet 'Obchody'")
    print("5. ✅ Zkontrolujte:")
    print("   • Každá sekvence = 1 řádek v 'Obchody'")
    print("   • Exit_Reason obsahuje LONG-FLAT-LIMIT info")
    print("   • Swaps_USD = 0.0 pro Long pozice")
    print("   • Správné Net_PnL výpočty")
    print("   • Equity_After postupně roste")
    print("6. ✅ Zkontrolujte TOP 5 v WebGUI výsledcích")
    print()
    
    print("🚨 MOŽNÉ PROBLÉMY:")
    print("-" * 40)
    print("   ❌ Prázdný sheet 'Obchody' = chyba v simulate_trades")
    print("   ❌ Více řádků pro jednu sekvenci = chyba v konsolidaci")
    print("   ❌ Chybné Equity_After = chyba v equity výpočtu")
    print("   ❌ Chybné swapy = chyba v LONG-FLAT-LIMIT logice")
    print()
    
    print("✅ OČEKÁVANÝ VÝSLEDEK:")
    print("-" * 40)
    print(f"   • {len(trades)} obchodů v Excel sheetu 'Obchody'")
    print(f"   • TOP 5 obchodů zobrazeno ve WebGUI")
    print(f"   • Celkový P&L: ${sum(t['Net_PnL'] for t in trades):.2f}")
    print(f"   • Win Rate: {len([t for t in trades if t['Net_PnL'] > 0]) / len(trades) * 100:.1f}%")

if __name__ == "__main__":
    test_excel_trades()
