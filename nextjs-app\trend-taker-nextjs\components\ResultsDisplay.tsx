'use client';

import { useState } from 'react';
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

interface ResultsDisplayProps {
  results: string;
  loading: boolean;
  error?: string;
}

export default function ResultsDisplay({ results, loading, error }: ResultsDisplayProps) {
  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Loader2 className="animate-spin mr-2" size={20} />
          ⏳ Probíhá testování...
        </h3>
        <div className="flex items-center justify-center py-8">
          <div className="loading-spinner"></div>
        </div>
        <p className="text-center text-gray-600">
          Prosím čekejte, test může trvat několik minut.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        {error ? (
          <>
            <AlertCircle className="text-red-500 mr-2" size={20} />
            📊 Výsledky testu - CHYBA
          </>
        ) : (
          <>
            <CheckCircle className="text-green-500 mr-2" size={20} />
            📊 Výsledky testu
          </>
        )}
      </h3>
      
      <div className="results-container">
        {error ? (
          <div className="text-red-600 font-semibold">
            CHYBA: {error}
          </div>
        ) : (
          <pre className="text-sm">{results || 'Připraven k testování...\nVyberte parametry a klikněte na \'Spustit Test\''}</pre>
        )}
      </div>
    </div>
  );
}
