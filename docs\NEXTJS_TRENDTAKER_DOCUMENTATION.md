# Next.js TrendTaker v2.0 - Complete Documentation

## 📋 **Overview**

TrendTaker v2.0 is a sophisticated trading strategy application that implements Marubozu candlestick pattern detection with advanced position management. This Next.js implementation provides 100% algorithmic consistency with the original Python WebGUI version.

## 🎯 **Core Trading Strategy**

### **Marubozu Pattern Detection**
- **Wick Ratio**: ≤ 18% of candle range
- **Body Ratio**: ≥ 60% of candle range (configurable)
- **Range Filter**: Range > ATR10_s1 AND ATR10_s1 > 0
- **Direction**: Green (Bull) or Red (Bear) candles

### **Trading Variants**
1. **Strict**: Sequence ends on any opposite candle
2. **Tolerant**: Advanced tolerance with pending_entry logic

### **Overnight Modes**
1. **STANDARD**: Hold positions overnight with swaps
2. **LONG-FLAT-LIMIT**: LONG positions flat at close, Buy Limit next day

## 🔧 **Technical Implementation**

### **Key Files Structure**
```
nextjs-app/trend-taker-nextjs/
├── app/
│   ├── api/
│   │   ├── run-test/route.ts      # Single test API
│   │   └── grid-test/route.ts     # Grid test API
│   └── page.tsx                   # Main UI
├── components/
│   ├── TestForm.tsx               # Test configuration form
│   └── GridTestForm.tsx           # Grid test form
├── lib/
│   ├── trendTaker.ts              # Core engine
│   └── dataUtils.ts               # Data utilities
└── types/index.ts                 # TypeScript interfaces
```

### **Core Engine Features**
- **ATR Calculation**: 10-period Average True Range with shift(1)
- **Sequence Building**: Strict/Tolerant logic with pending_entry
- **Position Simulation**: LONG-FLAT-LIMIT and STANDARD modes
- **Excel Export**: Multi-sheet workbooks with detailed analysis

## 💰 **Financial Calculations**

### **Spread Costs**
- **Rate**: 0.15 points = $15 USD per lot
- **Standard Trades**: Single spread cost per trade
- **LONG-FLAT-LIMIT**: Spread cost per segment (each Buy Limit fill)

### **SWAP Calculations**
- **LONG**: -0.55237 points/day (cost)
- **SHORT**: +0.29425 points/day (credit)
- **Wednesday 3x Rule**: Triple swap covers weekend
- **LONG-FLAT-LIMIT**: Only SHORT positions pay swaps

### **Position Sizing**
```
Risk Amount = Start Equity × Risk %
ATR10 = 10-period Average True Range
Lots = Risk Amount ÷ (ATR10 × $100 per point)
```

## 📊 **Advanced Stop Loss Methods v2.0**

### **1. Initial Method**
- **Entry SL**: Entry ± ATR10
- **Adjustment**: 50% of previous candle range after first day

### **2. BarsBack_Low Method**
- **SL Level**: Low of X bars back from current day
- **Dynamic**: Updates daily based on historical lows

### **3. BarsBack_50pct Method**
- **SL Level**: (High + Low) / 2 of X bars back
- **Balanced**: Mid-point of historical range

## 🔄 **LONG-FLAT-LIMIT Logic**

### **Multi-Segment Trading**
1. **Day 1**: Enter at Open, exit at Close
2. **Day 2+**: Buy Limit at (Open - Offset points)
3. **Fill Logic**: If Low ≤ Buy Limit → Fill at Buy Limit price
4. **Exit**: Always flat at Close each day

### **Segment Tracking**
- Each Buy Limit fill creates new segment
- Individual P&L and spread cost per segment
- Consolidated trade with segment details

## 📈 **Excel Export Structure**

### **Sheet 1: Obchody (Main Trades)**
- Trade summary without segment details
- Consolidated P&L, spread costs, and swaps
- Sequence identifiers and SL calculation history

### **Sheet 2: Detailní_Segmenty (Segment Details)**
- Individual segment breakdown for LONG-FLAT-LIMIT
- Entry/exit prices, stop losses, and P&L per segment
- Detailed audit trail of multi-day positions

### **Sheet 3: Denní_Analýza (Daily Analysis)**
- Complete candlestick data with indicators
- Marubozu detection results and ATR values
- SL method details and calculated levels

### **Sheet 4: Sekvence (Sequences)**
- Sequence start/end dates and directions
- Index ranges and duration statistics

### **Sheet 5: Parametry (Parameters)**
- Complete test configuration
- Risk management settings
- Strategy variant and method selections

## 🧪 **Testing Modes**

### **Single Test**
- Individual strategy test with specific parameters
- Custom date ranges or full dataset
- Detailed Excel export with all sheets

### **Grid Test**
- Multiple parameter combinations
- Batch processing with performance metrics
- Comparative analysis across variants

## ⚙️ **Configuration Parameters**

### **Core Settings**
- **Data File**: D1 candlestick data (CSV format)
- **Date Range**: Custom period or full dataset
- **Variant**: Strict or Tolerant sequence logic
- **Min Body Ratio**: Marubozu body size threshold (default 60%)

### **Risk Management**
- **Start Equity**: Initial capital ($10,000 default)
- **Risk %**: Position size as % of equity (2% default)
- **RRR**: Risk/Reward Ratio (1.0-10.0 range)

### **Strategy Options**
- **Exit Policy**: Conservative or Optimistic
- **Overnight Mode**: STANDARD or LONG-FLAT-LIMIT
- **Buy Limit Offset**: Points below Open (LONG-FLAT-LIMIT only)
- **SL Method**: Initial, BarsBack_Low, or BarsBack_50pct
- **BarsBack**: Lookback period for BarsBack methods

## 🔍 **Data Processing Workflow**

### **Critical Processing Order**
1. **Load full dataset** from CSV file
2. **Calculate ATR on entire dataset** (ensures historical context)
3. **Detect Marubozu on entire dataset** (proper ATR validation)
4. **Build sequences on entire dataset** (complete pattern analysis)
5. **Filter by date range** (preserves calculation accuracy)
6. **Simulate trades** with proper risk management
7. **Generate Excel export** with all analysis sheets

## 🚨 **Critical Bug Fixes (v2.0)**

### **Date Parsing Fix**
- **Issue**: Timezone shifts causing wrong OHLC assignment
- **Solution**: Explicit UTC parsing with `.000Z` suffix

### **ATR Calculation Fix**
- **Issue**: Incorrect pandas rolling(10).mean().shift(1) implementation
- **Solution**: Proper rolling means calculation with shift logic

### **Workflow Fix**
- **Issue**: ATR calculated on filtered data (insufficient history)
- **Solution**: ATR calculated on full dataset, then filtered

### **Spread Cost Fix**
- **Issue**: LONG-FLAT-LIMIT charged spread only for first segment
- **Solution**: Spread cost for every segment (each Buy Limit fill)

### **SWAP Calculation Fix**
- **Issue**: Simple day multiplication without Wednesday 3x rule
- **Solution**: Day-by-day iteration with Wednesday 3x multiplier

## 📋 **Performance Metrics**

### **Trade Statistics**
- **Total Trades**: Number of completed positions
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross Profit ÷ Gross Loss
- **Net Profit**: Total profit after all costs
- **Max Drawdown**: Largest equity decline

### **Cost Analysis**
- **Spread Costs**: Total entry costs across all trades
- **Swap Costs/Credits**: Overnight financing impact
- **Net P&L**: Gross P&L minus all transaction costs

## 🔧 **Development Notes**

### **TypeScript Implementation**
- Strict type checking with comprehensive interfaces
- Error handling for data validation and processing
- Modular architecture for maintainability

### **Algorithm Consistency**
- 100% identical logic to Python WebGUI reference
- Extensive testing and validation against original
- Detailed commit history documenting all fixes

### **Performance Optimization**
- Efficient data processing with minimal memory usage
- Parallel processing where applicable
- Optimized Excel generation with proper formatting

## 📚 **API Endpoints**

### **POST /api/run-test**
Single strategy test with comprehensive analysis.

**Request Body:**
```json
{
  "dataFile": "XAUUSD_GMT+2_US-DST_D1.csv",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "variant": "Strict",
  "minBodyRatio": 60,
  "startEquity": 10000,
  "riskPct": 2,
  "rrr": 2.5,
  "exitPolicy": "Conservative",
  "overnightMode": "LONG-FLAT-LIMIT",
  "buyLimitOffset": 5,
  "longLmtSlMethod": "Initial",
  "barsBack": 3
}
```

**Response:**
- Detailed test results with trade statistics
- Excel file download with all analysis sheets
- Performance metrics and equity curve data

### **POST /api/grid-test**
Batch testing across multiple parameter combinations.

**Request Body:**
```json
{
  "dataFile": "XAUUSD_GMT+2_US-DST_D1.csv",
  "dataRange": "CUSTOM",
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "startEquity": 10000,
  "riskPct": 2,
  "exitPolicy": "Conservative",
  "overnightMode": "LONG-FLAT-LIMIT",
  "buyLimitOffset": 5,
  "longLmtSlMethod": "Initial",
  "barsBack": 3,
  "rrrValues": [1.5, 2.0, 2.5, 3.0]
}
```

**Response:**
- Comparative results across all RRR values
- Excel file with grid test summary
- Performance ranking and optimization insights

## 🎨 **User Interface**

### **Main Dashboard**
- **Test Configuration**: Parameter selection with validation
- **Results Display**: Real-time test progress and results
- **Excel Download**: Automatic file generation and download
- **Grid Testing**: Batch analysis with comparative metrics

### **Form Validation**
- **Date Range**: Ensures valid date selections
- **Parameter Bounds**: RRR limits, risk percentage validation
- **File Selection**: Data file existence and format validation
- **Method Dependencies**: Conditional parameter display

## 🔒 **Error Handling**

### **Data Validation**
- **CSV Format**: Proper OHLC data structure validation
- **Date Parsing**: Robust timezone and format handling
- **Missing Data**: Graceful handling of data gaps
- **Parameter Validation**: Range checking and type validation

### **Processing Errors**
- **Memory Management**: Large dataset handling
- **Calculation Errors**: Division by zero and edge cases
- **Excel Generation**: File system and formatting errors
- **API Timeouts**: Long-running process management

## 🚀 **Deployment**

### **Requirements**
- **Node.js**: v18+ with npm/yarn
- **Dependencies**: Next.js 14, ExcelJS, PapaParse
- **Data Files**: CSV format in designated data directory
- **Memory**: Sufficient RAM for large dataset processing

### **Installation**
```bash
cd nextjs-app/trend-taker-nextjs
npm install
npm run dev  # Development server
npm run build && npm start  # Production build
```

### **Configuration**
- **Data Path**: Configure data file directory in dataUtils.ts
- **Export Path**: Set Excel export directory for downloads
- **API Limits**: Adjust timeout and memory limits as needed

---

## 📞 **Support & Maintenance**

This documentation covers the complete Next.js TrendTaker v2.0 implementation. For technical issues or feature requests, refer to the Git commit history for detailed change documentation and bug fix implementations.

**Version**: 2.0
**Last Updated**: 2025-01-18
**Compatibility**: 100% consistent with Python WebGUI v2.0
**Repository**: https://github.com/Traderpoint/TrendTaker.git
**Branch**: dev-nextjs
