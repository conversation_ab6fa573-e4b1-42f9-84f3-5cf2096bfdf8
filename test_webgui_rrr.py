#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test WebGUI RRR parametru - simulace HTTP požadavku
"""

import urllib.parse
import urllib.request
import json
import time

def test_webgui_rrr(rrr_value):
    """Testuje WebGUI s konkrétní RRR hodnotou"""

    # Parametry pro test
    params = {
        'dataFile': 'XAUUSD_GMT+2_US-DST_D1.csv',
        'dataRange': 'CUSTOM',
        'startDate': '2025-07-01',
        'endDate': '2025-09-30',
        'variant': 'Tolerant',
        'rrr': rrr_value,
        'exitPolicy': 'Optimistic',
        'overnightMode': 'STANDARD',
        'startEquity': 10000,
        'riskPct': 2.0
    }

    # POST požadavek
    url = 'http://localhost:8080/run_test'
    data = json.dumps(params).encode('utf-8')

    print(f"🎯 Testování RRR {rrr_value}")
    print(f"URL: {url}")
    print(f"Data: {params}")

    try:
        # HTTP POST požadavek
        req = urllib.request.Request(url, data=data, headers={'Content-Type': 'application/json'})
        with urllib.request.urlopen(req, timeout=60) as response:
            content = response.read().decode('utf-8')
            
            # Parsování JSON odpovědi
            try:
                response_data = json.loads(content)
                if response_data.get('success'):
                    results_text = response_data.get('results', '')

                    # Hledání klíčových informací
                    lines = results_text.split('\n')

                    found_tp = False
                    found_exit_reason = False
                    found_exit_price = False

                    for line in lines:
                        if 'Take_Profit' in line and not found_tp:
                            print(f"✅ TP: {line.strip()}")
                            found_tp = True
                        elif 'Exit_Reason' in line and not found_exit_reason:
                            print(f"📊 Exit Reason: {line.strip()}")
                            found_exit_reason = True
                        elif 'Exit_Price' in line and not found_exit_price:
                            print(f"💰 Exit Price: {line.strip()}")
                            found_exit_price = True

                    if not (found_tp or found_exit_reason or found_exit_price):
                        print("⚠️  Výsledky neobsahují obchodní data")
                        print("Možná nebyl nalezen žádný obchod v daném období")
                        print("Prvních 1000 znaků výsledků:")
                        print(results_text[:1000])

                else:
                    print(f"❌ Test selhal: {response_data.get('error', 'Neznámá chyba')}")

            except json.JSONDecodeError:
                print("❌ Odpověď není platný JSON")
                print("Prvních 500 znaků odpovědi:")
                print(content[:500])
                
    except Exception as e:
        print(f"❌ Chyba při HTTP požadavku: {e}")
    
    print("-" * 60)

def main():
    print("🌐 TEST WEBGUI RRR PARAMETRU")
    print("=" * 80)
    print("Testování různých RRR hodnot pro ověření správného použití parametru")
    print()
    
    # Test různých RRR hodnot
    test_rrr_values = [4, 6, 10, 15]
    
    for rrr in test_rrr_values:
        test_webgui_rrr(rrr)
        time.sleep(1)  # Krátká pauza mezi požadavky
    
    print()
    print("🎯 OČEKÁVANÉ VÝSLEDKY:")
    print("RRR 4:  TP=3487.74 → Exit_Reason=TP")
    print("RRR 6:  TP=3557.06 → Exit_Reason=TP") 
    print("RRR 10: TP=3695.70 → Exit_Reason=SEQ_END")
    print("RRR 15: TP=3869.00 → Exit_Reason=SEQ_END")
    print()
    print("Pokud všechny testy ukazují stejné TP/Exit_Reason,")
    print("pak je někde hardcoded RRR hodnota!")

if __name__ == "__main__":
    main()
