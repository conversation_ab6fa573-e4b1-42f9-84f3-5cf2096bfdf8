'use client';

import { useState, useEffect, useCallback } from 'react';
import { TestParams, DatasetRange } from '@/types';
import { Play, Trash2, Download, BarChart3 } from 'lucide-react';

interface TestFormProps {
  onSubmit: (params: TestParams) => void;
  loading: boolean;
  onClear: () => void;
  onDownload: () => void;
  onHeatmap: () => void;
  showDownload: boolean;
  showHeatmap: boolean;
}

export default function TestForm({
  onSubmit,
  loading,
  onClear,
  onDownload,
  onHeatmap,
  showDownload,
  showHeatmap
}: TestFormProps) {
  const [params, setParams] = useState<TestParams>({
    testMode: 'SINGLE',
    dataFile: '',
    dataRange: 'CUSTOM',
    startDate: '2020-01-01',
    endDate: '2025-09-05',
    variant: 'Tolerant', // ✅ Fixed: Match Python WebGUI default
    rrr: 6.5,
    exitPolicy: 'Optimistic',
    overnightMode: 'LONG-FLAT-LIMIT', // ✅ Fixed: Match Python WebGUI default
    startEquity: 10000,
    riskPct: 2.0,
    // v2.0 New Parameters
    buyLimitOffset: 5,
    minBodyRatio: 60, // ✅ Min. velikost těla svíčky (%)
    longLmtSlMethod: 'Initial',
    barsBack: 3
  });

  const [datasetRange, setDatasetRange] = useState<DatasetRange | null>(null);
  const [availableFiles, setAvailableFiles] = useState<any[]>([]);
  const [loadingFiles, setLoadingFiles] = useState(true);
  const [showVariantRules, setShowVariantRules] = useState(false);

  // Načtení dostupných D1 souborů
  const loadAvailableFiles = useCallback(async () => {
    try {
      setLoadingFiles(true);
      const response = await fetch('/api/data-files');
      const data = await response.json();

      if (data.success && data.files.length > 0) {
        setAvailableFiles(data.files);

        // Automaticky vybere první dostupný soubor
        if (!params.dataFile && data.files.length > 0) {
          setParams(prev => ({
            ...prev,
            dataFile: data.files[0].filename
          }));
        }
      } else {
        setAvailableFiles([]);
        console.warn('Nenalezeny žádné D1 datové soubory');
      }
    } catch (error) {
      console.error('Chyba při načítání dostupných souborů:', error);
      setAvailableFiles([]);
    } finally {
      setLoadingFiles(false);
    }
  }, [params.dataFile]);

  // Načtení rozsahu datasetu pro vybraný soubor
  const loadDatasetRange = useCallback(async () => {
    if (!params.dataFile) return;

    try {
      const response = await fetch(`/api/dataset-range?filename=${encodeURIComponent(params.dataFile)}`);
      const data = await response.json();
      setDatasetRange(data);

      // Automaticky aktualizuje datumy pokud je vybrán FULL dataset
      if (data.success && params.dataRange === 'FULL') {
        setParams(prev => ({
          ...prev,
          startDate: data.start_date,
          endDate: data.end_date
        }));
      }
    } catch (error) {
      console.error('Chyba při načítání rozsahu datasetu:', error);
    }
  }, [params.dataFile, params.dataRange]);

  useEffect(() => {
    loadAvailableFiles();
  }, [loadAvailableFiles]);

  useEffect(() => {
    loadDatasetRange();
  }, [loadDatasetRange]);

  // Automatické nastavení dat při změně datasetRange
  useEffect(() => {
    if (datasetRange?.success) {
      if (params.dataRange === 'FULL') {
        // FULL Dataset - použij celý rozsah
        setParams(prev => ({
          ...prev,
          startDate: datasetRange.start_date!,
          endDate: datasetRange.end_date!
        }));
      } else if (params.dataRange === 'CUSTOM') {
        // CUSTOM Data - aktuální datum - 1 měsíc až konec datasetu
        const today = new Date();
        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        const startDate = oneMonthAgo.toISOString().split('T')[0];

        setParams(prev => ({
          ...prev,
          startDate: startDate,
          endDate: datasetRange.end_date!
        }));
      }
    }
  }, [datasetRange, params.dataRange]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(params);
  };

  const handleTestModeChange = (mode: 'SINGLE' | 'GRID') => {
    setParams(prev => ({ ...prev, testMode: mode }));
  };

  const handleDataRangeChange = (range: 'CUSTOM' | 'FULL') => {
    setParams(prev => ({ ...prev, dataRange: range }));

    if (range === 'FULL' && datasetRange?.success) {
      // FULL Dataset - použij celý rozsah datasetu
      setParams(prev => ({
        ...prev,
        startDate: datasetRange.start_date!,
        endDate: datasetRange.end_date!
      }));
    } else if (range === 'CUSTOM') {
      // CUSTOM Data - aktuální datum - 1 měsíc až konec datasetu
      const today = new Date();
      const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
      const startDate = oneMonthAgo.toISOString().split('T')[0];

      const endDate = datasetRange?.success ? datasetRange.end_date! : '2025-09-05';

      setParams(prev => ({
        ...prev,
        startDate: startDate,
        endDate: endDate
      }));

      console.log(`✅ Custom Data nastaveno: ${startDate} - ${endDate}`);
    }
  };

  const handleDataFileChange = (filename: string) => {
    setParams(prev => ({ ...prev, dataFile: filename }));

    // Pokud je vybrán FULL dataset, automaticky aktualizuje datumy po načtení nového souboru
    if (params.dataRange === 'FULL') {
      // Datumy se aktualizují automaticky v loadDatasetRange useEffect
    }
  };

  const isGridMode = params.testMode === 'GRID';
  const isFullDataset = params.dataRange === 'FULL';

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-secondary mb-2">🎯 Trend Taker Test 1.0</h1>
        <h2 className="text-xl text-gray-600">Next.js Web Interface</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Test Mode */}
        <div className="form-group">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Režim testování:
          </label>
          <select
            value={params.testMode}
            onChange={(e) => handleTestModeChange(e.target.value as 'SINGLE' | 'GRID')}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="SINGLE">SINGLE TEST (jeden parametr set)</option>
            <option value="GRID">GRID TEST (všechny kombinace)</option>
          </select>
        </div>

        {/* Data File */}
        <div className="form-group">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Datový soubor (D1 data z C:\Temp\Trading\DATA ICM):
          </label>
          {loadingFiles ? (
            <div className="w-full p-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500">
              Načítám dostupné D1 soubory...
            </div>
          ) : availableFiles.length > 0 ? (
            <select
              value={params.dataFile}
              onChange={(e) => handleDataFileChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">-- Vyberte D1 datový soubor --</option>
              {availableFiles.map((file) => (
                <option key={file.filename} value={file.filename}>
                  {file.symbol} ({file.filename}) - {file.totalRecords} záznamů
                </option>
              ))}
            </select>
          ) : (
            <div className="w-full p-2 border border-red-300 rounded-md bg-red-50 text-red-700">
              ❌ Nenalezeny žádné D1 datové soubory v adresáři C:\Temp\Trading\DATA ICM
              <br />
              <small>Zkontrolujte, že adresář existuje a obsahuje CSV soubory s D1 v názvu.</small>
            </div>
          )}
        </div>

        {/* Dataset Info */}
        {datasetRange && datasetRange.success && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
            <h4 className="text-sm font-medium text-blue-800 mb-2">📊 Informace o datasetu:</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div><strong>Symbol:</strong> {datasetRange.symbol}</div>
              <div><strong>Období:</strong> {datasetRange.start_date} - {datasetRange.end_date}</div>
              <div><strong>Záznamů:</strong> {datasetRange.total_records?.toLocaleString()}</div>
              <div><strong>Soubor:</strong> {datasetRange.filename}</div>
            </div>
          </div>
        )}

        {/* Data Range - pro SINGLE i GRID */}
        <div className="form-group">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Rozsah dat:
          </label>
          <select
            value={params.dataRange}
            onChange={(e) => handleDataRangeChange(e.target.value as 'CUSTOM' | 'FULL')}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="CUSTOM">Custom Data (vlastní rozsah)</option>
            <option value="FULL">Full Dataset (celý dataset)</option>
          </select>
        </div>

        {/* Date Range */}
        <div className={`grid grid-cols-2 gap-4 ${isFullDataset ? 'opacity-50 pointer-events-none' : ''}`}>
          <div className="form-group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Datum od:
            </label>
            <input
              type="date"
              value={params.startDate}
              onChange={(e) => setParams(prev => ({ ...prev, startDate: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <div className="form-group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Datum do:
            </label>
            <input
              type="date"
              value={params.endDate}
              onChange={(e) => setParams(prev => ({ ...prev, endDate: e.target.value }))}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Single Test Parameters */}
        {!isGridMode && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="form-group">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Varianta:
                  <button
                    type="button"
                    onClick={() => setShowVariantRules(!showVariantRules)}
                    className="ml-2 text-blue-500 hover:text-blue-700 text-sm"
                    title="Zobrazit pravidla variant"
                  >
                    ❓
                  </button>
                </label>
                <select
                  value={params.variant}
                  onChange={(e) => setParams(prev => ({ ...prev, variant: e.target.value as 'Strict' | 'Tolerant' }))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="Strict">Strict</option>
                  <option value="Tolerant">Tolerant</option>
                </select>

                {/* Variant Rules */}
                {showVariantRules && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
                    <h4 className="font-semibold text-gray-800 mb-3">📋 Pravidla variant:</h4>

                    <div className="mb-4">
                      <h5 className="font-medium text-gray-700 mb-2">🔒 Strict:</h5>
                      <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                        <li>Pouze přesné Marubozu svíčky (wick ratio &lt; 0.18)</li>
                        <li>Žádné tolerance pro opačné svíčky</li>
                        <li>Sekvence končí při první opačné svíčce</li>
                        <li>Konzervativní přístup - méně obchodů, vyšší přesnost</li>
                      </ul>
                    </div>

                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">🔄 Tolerant:</h5>
                      <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                        <li>Marubozu svíčky (wick ratio &lt; 0.18) + tolerance</li>
                        <li>Toleruje opačné svíčky pokud neprolomí klíčové úrovně:</li>
                        <li>• UP sekvence: Red svíčka OK pokud Close ≥ předchozí Low</li>
                        <li>• DOWN sekvence: Green svíčka OK pokud Close ≤ předchozí High</li>
                        <li>Opačná Marubozu vždy ukončí sekvenci a začne novou</li>
                        <li>Flexibilní přístup - více obchodů, zachytí pokračování trendů</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
              <div className="form-group">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Risk/Reward:
                </label>
                <input
                  type="number"
                  value={params.rrr}
                  onChange={(e) => setParams(prev => ({ ...prev, rrr: parseFloat(e.target.value) }))}
                  min="1"
                  step="0.5"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="form-group">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Exit Policy:
                </label>
                <select
                  value={params.exitPolicy}
                  onChange={(e) => setParams(prev => ({ ...prev, exitPolicy: e.target.value as 'Optimistic' | 'Conservative' }))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="Optimistic">Optimistic</option>
                  <option value="Conservative">Conservative</option>
                </select>
              </div>
              <div className="form-group">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Overnight Mode:
                </label>
                <select
                  value={params.overnightMode}
                  onChange={(e) => setParams(prev => ({ ...prev, overnightMode: e.target.value as 'STANDARD' | 'LONG-FLAT-LIMIT' }))}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="STANDARD">STANDARD (držíme pozice přes noc)</option>
                  <option value="LONG-FLAT-LIMIT">LONG-FLAT-LIMIT (Long bez swapu)</option>
                </select>
              </div>
            </div>
          </>
        )}

        {/* v2.0 Advanced Parameters */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">🎯 Advanced Parameters v2.0</h3>

          {/* LONG-FLAT-LIMIT specific parameters */}
          {params.overnightMode === 'LONG-FLAT-LIMIT' && (
            <div className="bg-blue-50 p-4 rounded-lg mb-4">
              <h4 className="text-md font-medium text-blue-800 mb-3">LONG-FLAT-LIMIT Settings</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="form-group">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Buy Limit Offset (bodů):
                  </label>
                  <input
                    type="number"
                    value={params.buyLimitOffset}
                    onChange={(e) => setParams(prev => ({ ...prev, buyLimitOffset: parseFloat(e.target.value) }))}
                    min="1"
                    max="20"
                    step="1"
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>
                <div className="form-group">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    LONG LMT SL Method:
                  </label>
                  <select
                    value={params.longLmtSlMethod}
                    onChange={(e) => {
                      const method = e.target.value as 'Initial' | 'BarsBack_Low' | 'BarsBack_50pct';
                      setParams(prev => ({ ...prev, longLmtSlMethod: method }));
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="Initial">Initial (Entry - ATR10)</option>
                    <option value="BarsBack_Low">BarsBack Low</option>
                    <option value="BarsBack_50pct">BarsBack 50%</option>
                  </select>
                </div>
              </div>

              {/* BarsBack field - show only for BarsBack methods */}
              {(params.longLmtSlMethod === 'BarsBack_Low' || params.longLmtSlMethod === 'BarsBack_50pct') && (
                <div className="form-group mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    BarsBack - X (počet barů zpětně):
                  </label>
                  <input
                    type="number"
                    value={params.barsBack}
                    onChange={(e) => setParams(prev => ({ ...prev, barsBack: parseInt(e.target.value) }))}
                    min="1"
                    max="10"
                    step="1"
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {params.longLmtSlMethod === 'BarsBack_Low'
                      ? 'SL se nastaví na Low X barů zpětně od aktuální svíčky'
                      : 'SL se nastaví na (High + Low)/2 X barů zpětně od aktuální svíčky'
                    }
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Tolerant variant info - No additional parameters needed */}
          {params.variant === 'Tolerant' && (
            <div className="bg-green-50 p-4 rounded-lg mb-4">
              <h4 className="text-md font-medium text-green-800 mb-3">Tolerant Variant</h4>
              <p className="text-sm text-green-700 mb-2">
                Tolerant varianta používá pokročilou logiku pro toleranci opačných svíček:
              </p>
              <ul className="text-xs text-green-600 ml-4 list-disc space-y-1">
                <li><strong>UP sekvence:</strong> Red svíčka OK pokud Close ≥ předchozí Low</li>
                <li><strong>DOWN sekvence:</strong> Green svíčka OK pokud Close ≤ předchozí High</li>
                <li>Opačná Marubozu vždy ukončí sekvenci a začne novou</li>
                <li>Flexibilní přístup - více obchodů, zachytí pokračování trendů</li>
              </ul>
            </div>
          )}
        </div>

        {/* Global Parameters */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">🎯 Global Parameters</h3>
          <div className="form-group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Min. velikost těla svíčky (%):
            </label>
            <input
              type="number"
              value={params.minBodyRatio}
              onChange={(e) => setParams(prev => ({ ...prev, minBodyRatio: parseFloat(e.target.value) }))}
              min="30"
              max="90"
              step="5"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              Minimální velikost těla svíčky pro detekci Marubozu (default: 60%)
            </p>
          </div>
        </div>

        {/* Common Parameters */}
        <div className="grid grid-cols-2 gap-4">
          <div className="form-group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Počáteční kapitál:
            </label>
            <input
              type="number"
              value={params.startEquity}
              onChange={(e) => setParams(prev => ({ ...prev, startEquity: parseFloat(e.target.value) }))}
              min="1000"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          <div className="form-group">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Risk % per trade:
            </label>
            <input
              type="number"
              value={params.riskPct}
              onChange={(e) => setParams(prev => ({ ...prev, riskPct: parseFloat(e.target.value) }))}
              min="0.1"
              max="10"
              step="0.1"
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-center space-x-4 pt-6">
          <button
            type="submit"
            disabled={loading}
            className="flex items-center space-x-2 bg-primary hover:bg-blue-600 text-white px-6 py-2 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Play size={16} />
            <span>{isGridMode ? '🔥 Spustit GRID TEST' : '🚀 Spustit Test'}</span>
          </button>
          
          <button
            type="button"
            onClick={onClear}
            className="flex items-center space-x-2 bg-danger hover:bg-red-600 text-white px-6 py-2 rounded-md transition-colors"
          >
            <Trash2 size={16} />
            <span>🗑 Vymazat</span>
          </button>
          
          {showDownload && (
            <button
              type="button"
              onClick={onDownload}
              className="flex items-center space-x-2 bg-success hover:bg-green-600 text-white px-6 py-2 rounded-md transition-colors"
            >
              <Download size={16} />
              <span>📥 Stáhnout Excel</span>
            </button>
          )}
          
          {showHeatmap && (
            <button
              type="button"
              onClick={onHeatmap}
              className="flex items-center space-x-2 bg-info hover:bg-purple-600 text-white px-6 py-2 rounded-md transition-colors"
            >
              <BarChart3 size={16} />
              <span>📊 HeatMap</span>
            </button>
          )}
        </div>
      </form>
    </div>
  );
}
