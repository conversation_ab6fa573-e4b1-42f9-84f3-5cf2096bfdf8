@echo off
echo ================================================================================
echo 🎯 TREND TAKER TEST 1.0 - Next.js Web GUI
echo ================================================================================
echo.

REM Kontrola Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js není nainstalován!
    echo 📥 Stáhněte a nainstalujte Node.js z https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js je dostupný
echo.

REM Kontrola datového souboru
if not exist "XAUUSD_GMT+2_US-DST_D1.csv" (
    echo ❌ Datový soubor XAUUSD_GMT+2_US-DST_D1.csv nebyl nalezen!
    echo 📁 Zkopírujte datový soubor do této složky
    pause
    exit /b 1
)

echo ✅ Datový soubor nalezen
echo.

REM Kontrola node_modules
if not exist "node_modules" (
    echo 📦 Instaluji závislosti...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Chyba při instalaci závislostí!
        pause
        exit /b 1
    )
    echo ✅ Závislosti nainstalovány
    echo.
)

REM Vytvoření public složky
if not exist "public" (
    mkdir public
    echo ✅ Vytvořena public složka
)

echo 🚀 Spouštím Next.js server...
echo 🌐 Aplikace bude dostupná na: http://localhost:3000
echo 🛑 Pro ukončení stiskněte Ctrl+C
echo.
echo ================================================================================

REM Spuštění serveru
npm run dev

pause
