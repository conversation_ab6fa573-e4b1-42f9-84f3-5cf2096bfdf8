#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pro ov<PERSON><PERSON><PERSON><PERSON> vysok<PERSON>ch RRR hodnot (10-20) pro LONG sekvenci 20.8.2025 - 3.9.2025
"""

import pandas as pd
import numpy as np

# Data z obchodu
entry_price = 3349.1  # Entry price z obchodního záznamu
atr10 = 34.66  # ATR10 z obchodního záznamu

# Testovací data z XAUUSD kolem 20.8.2025 - 5.9.2025
test_data = [
    # Datum, Open, High, Low, Close
    ("2025.08.20", 3315.465, 3350.198, 3311.255, 3347.335),  # Zelená - začátek LONG
    ("2025.08.21", 3349.098, 3351.935, 3324.948, 3338.935),  # Červená - Entry den
    ("2025.08.22", 3338.405, 3378.585, 3321.045, 3371.235),  # <PERSON><PERSON><PERSON>
    ("2025.08.25", 3370.835, 3376.135, 3359.495, 3365.605),  # <PERSON><PERSON><PERSON>
    ("2025.08.26", 3365.135, 3393.495, 3351.175, 3393.465),  # <PERSON><PERSON><PERSON>
    ("2025.08.27", 3392.648, 3398.569, 3373.665, 3396.495),  # Zelená
    ("2025.08.28", 3397.835, 3423.069, 3384.445, 3415.795),  # Zelená
    ("2025.08.29", 3416.965, 3453.565, 3404.185, 3446.805),  # Zelená
    ("2025.09.01", 3445.648, 3489.595, 3436.548, 3476.225),  # Zelená
    ("2025.09.02", 3476.505, 3539.849, 3469.805, 3532.405),  # Zelená
    ("2025.09.03", 3532.698, 3578.175, 3525.848, 3558.475),  # Zelená - konec sekvence
    ("2025.09.04", 3560.448, 3563.998, 3510.425, 3544.645),  # Červená
    ("2025.09.05", 3545.935, 3599.905, 3539.715, 3585.195),  # Zelená
]

def test_high_rrr_scenarios():
    """Testuje scénáře s vysokými RRR hodnotami"""
    print("🎯 TEST VYSOKÝCH RRR HODNOT (10-20)")
    print("=" * 80)
    print(f"Entry Price: {entry_price}")
    print(f"ATR10: {atr10}")
    print(f"Stop Loss: {entry_price - atr10:.2f}")
    print()
    
    # Kalkulace TP pro vysoké RRR
    high_rrr_levels = {}
    for rrr in range(10, 21):
        tp_price = entry_price + (rrr * atr10)
        high_rrr_levels[rrr] = tp_price
        print(f"RRR {rrr:2d}: TP = {tp_price:7.2f}")
    
    print()
    print("🔍 KONTROLA DOSAŽENÍ VYSOKÝCH RRR:")
    print("=" * 50)
    
    max_high_reached = 0
    max_high_date = ""
    
    # Najdeme nejvyšší High během sekvence
    for date, o, h, l, c in test_data:
        if h > max_high_reached:
            max_high_reached = h
            max_high_date = date
    
    print(f"Nejvyšší High během sekvence: {max_high_reached:.2f} ({max_high_date})")
    print()
    
    # Kontrola, které vysoké RRR by bylo dosaženo
    achieved_high_rrr = []
    for rrr in range(10, 21):
        tp_price = high_rrr_levels[rrr]
        if max_high_reached >= tp_price:
            achieved_high_rrr.append((rrr, tp_price))
            print(f"✅ RRR {rrr:2d} ({tp_price:7.2f}) - DOSAŽENO!")
        else:
            print(f"❌ RRR {rrr:2d} ({tp_price:7.2f}) - NEDOSAŽENO (chybí {tp_price - max_high_reached:.2f})")
    
    return achieved_high_rrr

def analyze_potential_profits():
    """Analyzuje potenciální zisky s vysokými RRR"""
    print("\n💰 ANALÝZA POTENCIÁLNÍCH ZISKŮ:")
    print("=" * 50)
    
    # Skutečný zisk
    actual_exit = 3558.48
    actual_profit = actual_exit - entry_price
    actual_rrr = actual_profit / atr10
    
    print(f"Skutečný exit: {actual_exit:.2f}")
    print(f"Skutečný profit: {actual_profit:.2f} bodů")
    print(f"Skutečný RRR: {actual_rrr:.2f}")
    print()
    
    # Porovnání s vysokými RRR
    print("Porovnání s vysokými RRR:")
    for rrr in [10, 12, 15, 20]:
        tp_price = entry_price + (rrr * atr10)
        potential_profit = tp_price - entry_price
        improvement = potential_profit / actual_profit
        
        print(f"RRR {rrr:2d}: TP {tp_price:7.2f} | Profit {potential_profit:6.2f} | {improvement:.1f}x více")

def main():
    print("🎯 ANALÝZA VYSOKÝCH RRR - LONG sekvence 20.8.-3.9.2025")
    print("=" * 80)
    print("Testování možností s RRR 10-20 (nyní dostupné ve WebGUI)")
    print()
    
    achieved_high_rrr = test_high_rrr_scenarios()
    analyze_potential_profits()
    
    print(f"\n🎯 ZÁVĚR:")
    print("=" * 30)
    
    if achieved_high_rrr:
        max_achieved = max(achieved_high_rrr, key=lambda x: x[0])
        print(f"✅ Nejvyšší dosažitelné RRR: {max_achieved[0]} (TP: {max_achieved[1]:.2f})")
        print(f"📈 WebGUI nyní podporuje RRR až do 20!")
        print(f"🔧 GRID TEST nyní testuje RRR 5.0-12.0")
        print(f"💡 Můžete testovat vyšší RRR pro lepší zisky!")
    else:
        print("❌ Žádné vysoké RRR (10+) nebylo dosaženo v této sekvenci")
        print("📊 Ale WebGUI nyní podporuje testování RRR až do 20!")
    
    print(f"\n🌐 Otevřete http://localhost:8080 a vyzkoušejte nové RRR možnosti!")

if __name__ == "__main__":
    main()
