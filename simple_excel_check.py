#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jednoduchá kontrola Excel souboru
"""

import pandas as pd
import os

def check_excel():
    """Jednoduchá kontrola Excel souboru"""
    
    excel_file = "TrendTaker_WebGUI_Tolerant_2025-08-17_2025-09-18.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel soubor nenalezen: {excel_file}")
        return
    
    print(f"✅ Excel soubor nalezen: {excel_file}")
    
    try:
        # Načtení všech sheetů
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print(f"\n📊 Nalezené sheety:")
        for sheet_name, df in excel_data.items():
            print(f"   • {sheet_name}: {len(df)} řádků")
        
        # Kontrola obchodů
        if 'Obchody' in excel_data:
            trades_df = excel_data['Obchody']
            print(f"\n💹 OBCHODY:")
            if len(trades_df) > 0:
                print(f"   Celkem obchodů: {len(trades_df)}")
                print(f"   Sloupce: {list(trades_df.columns)}")
                
                # První obchod
                if len(trades_df) > 0:
                    first_trade = trades_df.iloc[0]
                    print(f"\n   První obchod:")
                    for col in trades_df.columns:
                        print(f"     {col}: {first_trade[col]}")
            else:
                print("   ❌ Žádné obchody")
        
        # Kontrola detailních segmentů
        if 'Detailní_Segmenty' in excel_data:
            segments_df = excel_data['Detailní_Segmenty']
            print(f"\n🔍 DETAILNÍ SEGMENTY:")
            print(f"   Celkem segmentů: {len(segments_df)}")
            if len(segments_df) > 0:
                print(f"   Sloupce: {list(segments_df.columns)}")
        else:
            print(f"\n⚠️  Sheet 'Detailní_Segmenty' nenalezen")
        
        # Kontrola sekvencí
        if 'Sekvence' in excel_data:
            sequences_df = excel_data['Sekvence']
            print(f"\n📈 SEKVENCE:")
            print(f"   Celkem sekvencí: {len(sequences_df)}")
            if len(sequences_df) > 0:
                up_count = len(sequences_df[sequences_df['Direction'] == 'Up'])
                down_count = len(sequences_df[sequences_df['Direction'] == 'Down'])
                print(f"   UP sekvence: {up_count}")
                print(f"   DOWN sekvence: {down_count}")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    check_excel()
