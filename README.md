# 🎯 Trend Taker - <PERSON><PERSON><PERSON><PERSON> Sekvenční Strategie

Kompletní sada nástrojů pro testování Marubozu sekvenční obchodní strategie v různých implementacích.

## 📁 Struktura Repository

```
TrendTaker/
├── python-console/          # Python konzolová verze
├── python-webgui/           # Python Web GUI verze  
├── nextjs-app/              # Next.js moderní webová aplikace
└── README.md               # Tento soubor
```

## 🚀 Verze Aplikací

### 1. 🐍 Python Console (`python-console/`)
- **Soubor:** `Trend Taker Test 1.0.py`
- **Typ:** Konzolová aplikace
- **Použití:** Rych<PERSON> testování, skripty, automatizace
- **Spuštění:** `python "Trend Taker Test 1.0.py"`

### 2. 🌐 Python Web GUI (`python-webgui/`)
- **Soubor:** `Trend Taker Web GUI.py`
- **Typ:** Webov<PERSON> (HTTP server)
- **Port:** http://localhost:8080
- **Spuštění:** `python "Trend Taker Web GUI.py"`

### 3. ⚡ Next.js App (`nextjs-app/`)
- **Typ:** Moderní React/TypeScript webová aplikace
- **Port:** http://localhost:3000
- **Technologie:** Next.js 14, TypeScript, Tailwind CSS
- **Spuštění:** `npm install && npm run dev`

## ✨ Funkce

Všechny verze podporují:

- **Single Test** - Testování jednotlivých parametrů
- **Grid Test** - Kompletní analýza všech kombinací parametrů
- **Excel Export** - Automatické generování výsledků
- **Marubozu Detection** - Detekce Marubozu svíček
- **Sekvenční Logika** - Strict a Tolerant varianty
- **Risk Management** - Position sizing podle ATR
- **Swap Calculation** - STANDARD a LONG-FLAT-LIMIT módy
- **Advanced SL Methods** - Initial, BarsBack Low, BarsBack 50% (v2.0+)

## 📊 Testované Parametry

- **RRR (Risk/Reward):** 5.0 - 8.0 (krok 0.5)
- **Varianty:** Strict, Tolerant
- **Exit Policy:** Conservative, Optimistic  
- **Overnight Mode:** STANDARD, LONG-FLAT-LIMIT
- **Timeframe:** D1 (denní svíčky)
- **Instrument:** XAUUSD (Gold)

## 🔧 Požadavky

### Společné
- Datový soubor: `XAUUSD_GMT+2_US-DST_D1.csv`

### Python verze
- Python 3.7+
- pandas, numpy, openpyxl

### Next.js verze
- Node.js 18+
- npm nebo yarn

## 📈 Výstupy

### Excel soubory obsahují:
- **Obchody** - Detailní seznam všech obchodů
- **Denní Analýza** - Analýza sekvencí po dnech
- **Sekvence** - Seznam nalezených sekvencí
- **Parametry** - Nastavení testu
- **Top výsledky** - Nejlepší kombinace (Grid test)

### Metriky:
- Win Rate, Profit Factor
- Net Profit, Max Drawdown
- Počet obchodů, Average lots
- Swap costs, Spread costs

## 🎯 Doporučené Použití

- **Vývoj/Debug:** Python Console
- **Rychlé testování:** Python Web GUI
- **Produkce/Prezentace:** Next.js App

## 🆕 Nové Funkce v v2.0 (Září 2025)

### 🎯 Advanced Stop Loss Methods pro LONG-FLAT-LIMIT
- **Initial**: Původní logika (Entry - ATR10)
- **BarsBack Low**: SL na Low X barů zpětně
- **BarsBack 50%**: SL na 50% range X barů zpětně
- **Konzervativní logika**: SL se nikdy nezvyšuje (min logic)

### 📊 Rozšířená Denní Analýza
- **LONG_LMT_SL_Method**: Zobrazuje použitou SL metodu
- **BarsBack**: Počet barů zpětně pro výpočet
- **Calculated_SL**: Dopočítaný SL pro každý den

### 📋 Detailní Exit Reasons
- **SEQ_END_EOD_CLOSE**: Detailní důvod ukončení sekvence
- **BUY_LIMIT_NOT_FILLED**: Informace o nevyplněných Buy Limit příkazech
- **SL_Calc**: Kompletní historie SL změn během obchodu

## 📝 Poznámky

- Všechny verze používají stejnou core logiku
- Next.js verze má nejlepší UX a performance
- Python verze jsou kompatibilní s existujícími skripty
- Grid test testuje 32 kombinací parametrů
- v2.0 přidává pokročilé SL metody pro lepší risk management

## 🔄 Migrace

Při přechodu mezi verzemi jsou výsledky 100% kompatibilní díky stejné implementaci výpočetní logiky.

## 📄 Licence

Tento projekt je určen pro interní použití Traderpoint.
