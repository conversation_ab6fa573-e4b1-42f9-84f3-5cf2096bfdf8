#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test nové <PERSON> definice s min. velikostí těla svíčky
"""

import pandas as pd
import numpy as np
import os

# Simulace WebGUI třídy pro testování
class TestMarubozuDetection:
    def __init__(self):
        self.WICK_RATIO_MAX = 0.18
        self.ATR_PERIOD = 10
    
    def detect_marubozu(self, open_prices, high_prices, low_prices, close_prices, atr10_s1, min_body_ratio):
        """Detekce Marubozu s konfigurovatelnou min. velikostí těla"""
        
        # Základní výpočty
        ranges = high_prices - low_prices
        bull = close_prices >= open_prices
        
        # Open wick (stín na straně otevření)
        open_wick = np.where(bull, open_prices - low_prices, high_prices - open_prices)
        wick_ratio = np.divide(open_wick, ranges, out=np.zeros_like(open_wick), where=ranges!=0)
        
        # Velikost těla svíčky
        body = np.abs(close_prices - open_prices)
        body_ratio = np.divide(body, ranges, out=np.zeros_like(body), where=ranges!=0)
        
        # Marubozu podmínky
        is_marubozu = (wick_ratio < self.WICK_RATIO_MAX) & (body_ratio >= min_body_ratio) & (ranges > atr10_s1) & np.isfinite(atr10_s1)
        
        return is_marubozu, wick_ratio, body_ratio

def test_marubozu_body_ratio():
    """Test různých hodnot min_body_ratio"""
    
    print("🧪 TEST MARUBOZU S MIN. VELIKOSTÍ TĚLA SVÍČKY")
    print("=" * 60)
    
    # Načtení testovacích dat
    data_file = r"C:\Temp\Trading\DATA ICM\XAUUSD_GMT+2_US-DST_D1.csv"
    
    if not os.path.exists(data_file):
        print(f"❌ Datový soubor nenalezen: {data_file}")
        return
    
    print(f"📂 Načítám data: {os.path.basename(data_file)}")
    
    try:
        df = pd.read_csv(data_file, encoding='utf-8')
        print(f"📊 Načteno {len(df)} záznamů")
        
        # Standardizace sloupců
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower().strip()
            if col_lower in ['datum', 'date']:
                column_mapping[col] = 'Date'
            elif col_lower in ['čas', 'cas', 'time']:
                column_mapping[col] = 'Time'
            elif col_lower in ['otevírací', 'open']:
                column_mapping[col] = 'Open'
            elif col_lower in ['nejvyšší', 'high']:
                column_mapping[col] = 'High'
            elif col_lower in ['nejnižší', 'low']:
                column_mapping[col] = 'Low'
            elif col_lower in ['uzavírací', 'close']:
                column_mapping[col] = 'Close'
        
        df = df.rename(columns=column_mapping)
        
        # Parsování datumu
        if 'Date' in df.columns:
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        else:
            df['Date'] = pd.to_datetime(df.iloc[:, 0], errors='coerce')
        
        # Filtrování na testovací období
        start_date = '2025-08-01'
        end_date = '2025-09-30'
        df_filtered = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)].copy().reset_index(drop=True)
        
        print(f"📅 Filtrováno na období {start_date} - {end_date}: {len(df_filtered)} záznamů")
        
        if len(df_filtered) == 0:
            print("❌ Žádná data v testovacím období")
            return
        
        # Příprava dat
        O = df_filtered['Open'].values
        H = df_filtered['High'].values
        L = df_filtered['Low'].values
        C = df_filtered['Close'].values
        
        # ATR10 shift1
        ranges = H - L
        prev_close = np.roll(C, 1)
        prev_close[0] = C[0]
        tr = np.maximum.reduce([ranges, np.abs(H - prev_close), np.abs(L - prev_close)])
        atr10_s1 = pd.Series(tr).rolling(10).mean().shift(1).values
        
        # Test různých min_body_ratio hodnot
        detector = TestMarubozuDetection()
        test_ratios = [0.30, 0.40, 0.50, 0.60, 0.70, 0.80, 0.90]
        
        print(f"\n📊 TESTOVÁNÍ RŮZNÝCH MIN_BODY_RATIO HODNOT:")
        print("-" * 60)
        
        results = []
        
        for min_body_ratio in test_ratios:
            is_marubozu, wick_ratio, body_ratio = detector.detect_marubozu(O, H, L, C, atr10_s1, min_body_ratio)
            
            marubozu_count = np.sum(is_marubozu)
            total_valid = np.sum(np.isfinite(atr10_s1))
            percentage = (marubozu_count / total_valid * 100) if total_valid > 0 else 0
            
            results.append({
                'min_body_ratio': min_body_ratio,
                'marubozu_count': marubozu_count,
                'percentage': percentage
            })
            
            print(f"Min Body Ratio {min_body_ratio*100:2.0f}%: {marubozu_count:3d} Marubozu ({percentage:5.1f}%)")
        
        print(f"\n📈 ANALÝZA VÝSLEDKŮ:")
        print("-" * 60)
        
        # Najdeme několik příkladů Marubozu s různými body ratio
        min_body_ratio = 0.60  # Standardní hodnota
        is_marubozu, wick_ratio, body_ratio = detector.detect_marubozu(O, H, L, C, atr10_s1, min_body_ratio)
        
        marubozu_indices = np.where(is_marubozu)[0]
        
        if len(marubozu_indices) > 0:
            print(f"Příklady Marubozu s min_body_ratio {min_body_ratio*100:.0f}%:")
            
            for i, idx in enumerate(marubozu_indices[:5]):  # Prvních 5 příkladů
                date = df_filtered.iloc[idx]['Date'].strftime('%Y-%m-%d')
                open_price = O[idx]
                high_price = H[idx]
                low_price = L[idx]
                close_price = C[idx]
                
                range_price = high_price - low_price
                body_size = abs(close_price - open_price)
                body_pct = (body_size / range_price * 100) if range_price > 0 else 0
                wick_pct = (wick_ratio[idx] * 100) if np.isfinite(wick_ratio[idx]) else 0
                
                direction = "🟢 Bullish" if close_price >= open_price else "🔴 Bearish"
                
                print(f"  {i+1}. {date}: {direction}")
                print(f"     OHLC: {open_price:.2f} / {high_price:.2f} / {low_price:.2f} / {close_price:.2f}")
                print(f"     Body: {body_pct:.1f}%, Wick: {wick_pct:.1f}%")
        
        print(f"\n🎯 ZÁVĚR:")
        print("-" * 60)
        print(f"✅ Marubozu detekce s konfigurovatelnou min. velikostí těla funguje")
        print(f"✅ Vyšší min_body_ratio = méně Marubozu (přísnější kritéria)")
        print(f"✅ Nižší min_body_ratio = více Marubozu (volnější kritéria)")
        print(f"✅ Standardní hodnota 60% je rozumný kompromis")
        
        # Doporučení
        best_ratio = None
        for result in results:
            if 5 <= result['marubozu_count'] <= 15:  # Rozumný počet
                best_ratio = result
                break
        
        if best_ratio:
            print(f"💡 Doporučená hodnota pro testovací období: {best_ratio['min_body_ratio']*100:.0f}%")
            print(f"   ({best_ratio['marubozu_count']} Marubozu, {best_ratio['percentage']:.1f}%)")
        
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_marubozu_body_ratio()
