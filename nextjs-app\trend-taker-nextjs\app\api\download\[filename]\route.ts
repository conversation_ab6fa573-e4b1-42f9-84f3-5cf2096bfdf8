import { NextRequest, NextResponse } from 'next/server';
import * as fs from 'fs';
import * as path from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    const filename = params.filename;
    const filepath = path.join(process.cwd(), 'public', filename);

    if (!fs.existsSync(filepath)) {
      return new NextResponse('File not found', { status: 404 });
    }

    const fileBuffer = fs.readFileSync(filepath);
    
    // Determine content type
    let contentType = 'application/octet-stream';
    if (filename.endsWith('.xlsx')) {
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    } else if (filename.endsWith('.csv')) {
      contentType = 'text/csv; charset=utf-8';
    } else if (filename.endsWith('.png')) {
      contentType = 'image/png';
    } else if (filename.endsWith('.txt')) {
      contentType = 'text/plain; charset=utf-8';
    }

    const response = new NextResponse(fileBuffer);
    response.headers.set('Content-Type', contentType);
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    response.headers.set('Content-Length', fileBuffer.length.toString());

    console.log(`✅ Soubor stažen: ${filename}`);
    return response;

  } catch (error) {
    console.error(`❌ Chyba při stahování: ${error}`);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
