# 📊 Trend Taker - Nastavení D1 Datových Souborů

Všechny verze Trend Taker aplikací nyní načítají data z centralizovaného adresáře a akceptují **pouze D1 data**.

## 📁 Da<PERSON><PERSON>

**Cesta:** `C:\Temp\Trading\DATA ICM`

Všechny aplikace automaticky hledají D1 datové soubory v tomto adresáři.

## 🎯 Požadavky na D1 Soubory

### Název Souboru
Soubor musí obsahovat **D1** v názvu pro automatickou detekci:
- ✅ `EURUSD_D1.csv`
- ✅ `XAUUSD_GMT+2_US-DST_D1.csv`
- ✅ `GBPUSD_D1_2024.csv`
- ✅ `DAILY_EURUSD.csv`
- ❌ `EURUSD_H1.csv` (není D1)
- ❌ `EURUSD.csv` (chybí D1 označení)

### Formát Souboru
- **Formát:** CSV (Comma Separated Values)
- **Minimální sloupce:** 4 (<PERSON><PERSON>, <PERSON>as, Open, High, Low, Close)
- **Kódování:** UTF-8 nebo Windows-1250
- **Oddělovač:** Čárka (`,`) nebo středník (`;`)

### Příklad Struktury CSV
```csv
Datum,Čas,Otevírací,Nejvyšší,Nejnižší,Uzavírací,Volume
2024-01-01,00:00,1.1050,1.1080,1.1040,1.1070,1000
2024-01-02,00:00,1.1070,1.1090,1.1050,1.1085,1200
```

## 🛠️ Nastavení Adresáře

### 1. Automatické Vytvoření
Použijte D1 Data Manager:
```bash
python D1_Data_Manager.py --create-dir
```

### 2. Manuální Vytvoření
1. Otevřete Průzkumník Windows
2. Přejděte na `C:\Temp\`
3. Vytvořte složku `Trading`
4. Ve složce `Trading` vytvořte složku `DATA ICM`
5. Finální cesta: `C:\Temp\Trading\DATA ICM`

## 📋 Správa D1 Souborů

### D1 Data Manager
Použijte utility skript pro správu souborů:

```bash
# Naskenuje všechny D1 soubory a zobrazí report
python D1_Data_Manager.py --scan

# Zobrazí pouze platné D1 soubory
python D1_Data_Manager.py --list-valid

# Vytvoří datový adresář
python D1_Data_Manager.py --create-dir
```

### Příklad Výstupu
```
🔍 Skenuji D1 datové soubory v adresáři: C:\Temp\Trading\DATA ICM
================================================================================
📁 Nalezeno 5 CSV souborů celkem

📊 REPORT D1 DATOVÝCH SOUBORŮ
================================================================================
📈 Celkem CSV souborů: 5
🎯 D1 souborů nalezeno: 3
✅ Platných D1 souborů: 2
❌ Neplatných D1 souborů: 1

✅ PLATNÉ D1 SOUBORY:
--------------------------------------------------------------------------------
Symbol     Soubor                              Záznamy    Období                    Velikost
--------------------------------------------------------------------------------
EURUSD     EURUSD_D1.csv                       5,247      2020-01-01 - 2024-12-31  2.1 MB
XAUUSD     XAUUSD_GMT+2_US-DST_D1.csv         3,891      2015-01-01 - 2024-12-31  1.8 MB
```

## 🚀 Použití v Aplikacích

### Next.js Verze
1. Spusťte aplikaci: `npm run dev`
2. Otevřete http://localhost:3000
3. V dropdown menu vyberte D1 soubor
4. Aplikace automaticky načte informace o datasetu

### Python Web GUI
1. Spusťte: `python "Trend Taker Web GUI.py"`
2. Otevřete http://localhost:8080
3. Vyberte D1 soubor z dropdown menu
4. Pokračujte v testování

### Python Console
1. Spusťte: `python "Trend Taker Test 1.0.py"`
2. Vyberte D1 soubor ze seznamu
3. Program automaticky pokračuje s vybraným souborem

## 🔧 Řešení Problémů

### Nenalezeny žádné D1 soubory
1. Zkontrolujte, že adresář `C:\Temp\Trading\DATA ICM` existuje
2. Zkontrolujte, že CSV soubory obsahují `D1` v názvu
3. Spusťte `python D1_Data_Manager.py --scan` pro diagnostiku

### Chyba při čtení souboru
1. Zkontrolujte kódování souboru (UTF-8 nebo Windows-1250)
2. Zkontrolujte, že soubor obsahuje alespoň 4 sloupce
3. Zkontrolujte formát datumů v prvních sloupcích

### Soubor není rozpoznán jako D1
1. Přejmenujte soubor tak, aby obsahoval `D1`
2. Příklad: `EURUSD.csv` → `EURUSD_D1.csv`

## 📈 Podporované Symboly

Aplikace podporuje jakýkoliv symbol, pokud:
- Soubor obsahuje `D1` v názvu
- Má správný CSV formát
- Obsahuje platná data

Běžné symboly:
- **Forex:** EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD, USDCHF, NZDUSD
- **Kovy:** XAUUSD (Gold), XAGUSD (Silver)
- **Indexy:** US30, NAS100, SPX500, GER40
- **Komodity:** USOIL, UKOIL, NATGAS

## 🔄 Migrace ze Starých Verzí

Pokud máte data v jiném adresáři:
1. Zkopírujte všechny D1 CSV soubory do `C:\Temp\Trading\DATA ICM`
2. Ujistěte se, že názvy souborů obsahují `D1`
3. Spusťte `python D1_Data_Manager.py --scan` pro ověření

## 💡 Tipy

1. **Organizace souborů:** Používejte konzistentní názvy jako `SYMBOL_D1.csv`
2. **Backup:** Pravidelně zálohujte datové soubory
3. **Aktualizace:** Aktualizujte data pravidelně pro přesné výsledky
4. **Velikost:** Větší soubory (>10MB) mohou trvat déle při načítání

## 📞 Podpora

Pokud máte problémy s nastavením dat:
1. Spusťte `python D1_Data_Manager.py --scan` pro diagnostiku
2. Zkontrolujte log výstupy aplikací
3. Ověřte, že máte správná oprávnění k adresáři
