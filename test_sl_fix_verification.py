#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test ověření opravy SL problému
Testuje novou SL logiku a detailní Excel export
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def test_sl_fix():
    """Test opravy SL logiky"""
    
    print("🧪 TEST OPRAVY SL LOGIKY")
    print("=" * 80)
    
    # Simulace problematického scénáře
    print("📊 SIMULACE PROBLEMATICKÉHO SCÉNÁŘE:")
    print("=" * 80)
    
    # Vytvoříme data, kde původní SL logika způsobovala problémy
    test_data = [
        {'Date': '2025-08-23', 'Open': 2556.58, 'High': 2569.35, 'Low': 2547.21, 'Close': 2567.37, 'Range': 22.14},
        {'Date': '2025-08-24', 'Open': 2568.40, 'High': 2583.77, 'Low': 2555.90, 'Close': 2573.46, 'Range': 27.87},
        {'Date': '2025-08-25', 'Open': 2578.14, 'High': 2603.45, 'Low': 2565.57, 'Close': 2591.15, 'Range': 37.88},
        {'Date': '2025-08-26', 'Open': 2593.98, 'High': 2618.17, 'Low': 2582.92, 'Close': 2618.17, 'Range': 35.25},
        {'Date': '2025-08-27', 'Open': 2608.61, 'High': 2629.43, 'Low': 2599.12, 'Close': 2625.16, 'Range': 30.31}
    ]
    
    df = pd.DataFrame(test_data)
    df['Date'] = pd.to_datetime(df['Date'])
    
    # Parametry
    entry = 2556.58
    initial_stop = 2528.47  # entry - ATR10 (28.11)
    tp = 2725.26
    buy_limit_offset = 5
    
    O = df['Open'].values
    H = df['High'].values
    L = df['Low'].values
    C = df['Close'].values
    
    print("📋 TESTOVACÍ DATA:")
    for _, row in df.iterrows():
        print(f"{row['Date'].strftime('%Y-%m-%d')}: O={row['Open']:.2f} H={row['High']:.2f} L={row['Low']:.2f} C={row['Close']:.2f} Range={row['Range']:.2f}")
    print()
    
    print("🔧 TESTOVÁNÍ NOVÉ SL LOGIKY:")
    print("=" * 80)
    
    # Simulace s novou logikou
    current_day = 0
    current_entry = entry
    current_stop = initial_stop
    position_active = True
    segments = []
    
    while current_day < len(df) and position_active:
        hi, lo, cl = H[current_day], L[current_day], C[current_day]
        date_str = df.iloc[current_day]['Date'].strftime('%Y-%m-%d')
        
        print(f"📅 DEN {current_day+1} ({date_str}):")
        print(f"   Current Entry: {current_entry:.2f}")
        print(f"   Current SL: {current_stop:.2f}")
        print(f"   OHLC: {O[current_day]:.2f}/{hi:.2f}/{lo:.2f}/{cl:.2f}")
        
        # Kontrola TP/SL během dne
        hit_tp = hi >= tp
        hit_sl = lo <= current_stop
        
        print(f"   TP check: {hi:.2f} >= {tp:.2f} = {hit_tp}")
        print(f"   SL check: {lo:.2f} <= {current_stop:.2f} = {hit_sl}")
        
        if hit_tp and hit_sl:
            exit_px = tp  # Optimistic
            reason = "TP (same-day)"
            position_active = False
            print(f"   ✅ TP a SL hit - exit na TP: {exit_px:.2f}")
        elif hit_tp:
            exit_px = tp
            reason = "TP"
            position_active = False
            print(f"   ✅ TP hit: {exit_px:.2f}")
        elif hit_sl:
            exit_px = current_stop
            reason = "SL"
            position_active = False
            print(f"   ❌ SL hit: {exit_px:.2f}")
        else:
            # Pozice přežila den - uzavřeme na Close
            exit_px = cl
            if current_day == len(df) - 1:
                reason = "SEQ_END"
                position_active = False
                print(f"   📅 Konec sekvence - exit na Close: {exit_px:.2f}")
            else:
                reason = "EOD_CLOSE"
                print(f"   🌅 EOD Close: {exit_px:.2f}")
        
        # Výpočet P&L pro tento segment
        segment_pnl_bod = exit_px - current_entry
        
        print(f"   💰 Segment P&L: {segment_pnl_bod:.2f} bodů")
        
        segments.append({
            "day": current_day + 1,
            "date": date_str,
            "entry": current_entry,
            "exit": exit_px,
            "stop": current_stop,
            "pnl_bod": segment_pnl_bod,
            "reason": reason
        })
        
        if position_active and current_day < len(df) - 1:
            # Pokračujeme další den s Buy Limit
            next_day = current_day + 1
            if next_day < len(df):
                next_open = O[next_day]
                buy_limit_price = next_open - buy_limit_offset
                next_low = L[next_day]
                next_date = df.iloc[next_day]['Date'].strftime('%Y-%m-%d')
                
                print(f"   📋 Další den ({next_date}) Buy Limit setup:")
                print(f"      Next Open: {next_open:.2f}")
                print(f"      Buy Limit: {buy_limit_price:.2f} (Open - {buy_limit_offset})")
                print(f"      Next Low: {next_low:.2f}")
                
                if next_low <= buy_limit_price:
                    # Buy Limit se naplnil
                    current_entry = buy_limit_price
                    current_day = next_day
                    
                    # NOVÁ SL LOGIKA: max(původní_SL, nový_SL)
                    if current_day > 0:
                        prev_range = H[current_day-1] - L[current_day-1]
                        new_sl = current_entry - (prev_range * 0.5)
                        old_stop = current_stop
                        current_stop = max(current_stop, new_sl)  # NOVÁ LOGIKA!
                        
                        print(f"      🔄 NOVÁ SL LOGIKA:")
                        print(f"         Předposlední range: {prev_range:.2f}")
                        print(f"         Nový SL výpočet: {new_sl:.2f}")
                        print(f"         Starý SL: {old_stop:.2f}")
                        print(f"         Finální SL: max({old_stop:.2f}, {new_sl:.2f}) = {current_stop:.2f}")
                        
                        if current_stop == old_stop:
                            print(f"         ✅ SL zůstal stejný (bezpečnější)")
                        else:
                            print(f"         ⬆️  SL se zvýšil o {current_stop - old_stop:.2f} bodů")
                    
                    print(f"      ✅ Buy Limit naplněn na {buy_limit_price:.2f}")
                else:
                    # Buy Limit se nenaplnil
                    position_active = False
                    print(f"      ❌ Buy Limit nenaplněn (Low {next_low:.2f} > Limit {buy_limit_price:.2f})")
            else:
                position_active = False
        else:
            position_active = False
        
        print()
        
        if not position_active:
            break
    
    # Analýza výsledků
    print("🔍 ANALÝZA VÝSLEDKŮ NOVÉ LOGIKY:")
    print("=" * 80)
    
    print("📋 DETAILNÍ SEGMENTY:")
    total_pnl = 0
    for i, seg in enumerate(segments, 1):
        total_pnl += seg['pnl_bod']
        print(f"Segment {i}: {seg['date']}")
        print(f"  Entry: {seg['entry']:.2f}, Exit: {seg['exit']:.2f}")
        print(f"  SL used: {seg['stop']:.2f}")
        print(f"  P&L: {seg['pnl_bod']:.2f} bodů")
        print(f"  Reason: {seg['reason']}")
        print()
    
    print(f"📊 CELKOVÉ VÝSLEDKY:")
    print(f"Celkem segmentů: {len(segments)}")
    print(f"Celkový P&L: {total_pnl:.2f} bodů")
    print(f"Poslední exit: {segments[-1]['date']} na {segments[-1]['reason']}")
    
    # Porovnání se starou logikou
    print(f"\n📈 POROVNÁNÍ SE STAROU LOGIKOU:")
    print("=" * 80)
    print("🔴 STARÁ LOGIKA:")
    print("   - SL se neustále zvyšoval")
    print("   - SL: 2528.47 → 2552.33 → 2559.20 → 2570.04 → 2585.99")
    print("   - Riziko předčasného SL hitu")
    print()
    print("🟢 NOVÁ LOGIKA:")
    sl_values = [seg['stop'] for seg in segments]
    print(f"   - SL: {' → '.join([f'{sl:.2f}' for sl in sl_values])}")
    print("   - SL se nezvyšuje agresivně")
    print("   - Bezpečnější pro Long pozice")
    
    # Test Excel struktury
    print(f"\n📊 EXCEL STRUKTURA (simulace):")
    print("=" * 80)
    print("📋 Sheet 'Obchody' (konsolidovaný):")
    print(f"   Sequence_ID: SEQ_001")
    print(f"   Start_Date: {segments[0]['date']}")
    print(f"   End_Date: {segments[-1]['date']}")
    print(f"   Entry_Price: {segments[0]['entry']:.2f}")
    print(f"   Exit_Price: {segments[-1]['exit']:.2f}")
    print(f"   Net_PnL: {total_pnl:.2f} bodů")
    print(f"   Segments_Count: {len(segments)}")
    print()
    print("📋 Sheet 'Detailní_Segmenty':")
    for i, seg in enumerate(segments, 1):
        print(f"   Segment {i}: {seg['date']} | Entry: {seg['entry']:.2f} | Exit: {seg['exit']:.2f} | P&L: {seg['pnl_bod']:.2f} | Reason: {seg['reason']}")
    
    print(f"\n✅ TEST DOKONČEN - NOVÁ LOGIKA VYPADÁ LÉPE!")

if __name__ == "__main__":
    test_sl_fix()
