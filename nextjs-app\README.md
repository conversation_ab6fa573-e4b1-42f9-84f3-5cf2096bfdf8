# ⚡ Trend Taker - Next.js App

Moderní webová aplikace pro testování Marubozu sekvenční strategie postavená na Next.js s TypeScript.

## 📋 Popis

Nejmodernější verze s React komponentami, TypeScript typovou bezpečností a responzivním designem optimalizovaným pro všechna zařízení.

## 🚀 Rychlé Spuštění

```bash
# Instalace závislostí
npm install

# Spuštění vývojového serveru
npm run dev
```

Aplikace bude dostupná na: **http://localhost:3000**

### Alternativní spuštění:
```bash
# Windows
start.bat

# Linux/Mac
chmod +x start.sh && ./start.sh
```

## ✨ Funkce

- ✅ **Single Test** - Testování jednotlivých parametrů
- ✅ **Grid Test** - Kompletní grid test všech kombinací
- ✅ **Responzivní design** - Funguje na mobilu i desktopu
- ✅ **TypeScript** - Plná typová bezpečnost
- ✅ **Real-time UI** - Loading stavy a error handling
- ✅ **Excel export** - Automatické generování a stahování
- ✅ **HeatMap** - Textová vizualizace výsledků
- ✅ **Modern UX** - Tailwind CSS styling

## 🏗️ Technologie

- **Next.js 14** - React framework s App Router
- **TypeScript** - Typová bezpečnost
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Moderní ikony
- **XLSX** - Excel manipulace
- **Papa Parse** - CSV parsing

## 📁 Struktura

```
nextjs-app/
├── app/                    # Next.js App Router
│   ├── api/               # API endpointy
│   ├── globals.css        # Globální styly
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Hlavní stránka
├── components/            # React komponenty
├── lib/                  # Core logika (TypeScript port)
├── types/                # TypeScript definice
├── public/               # Statické soubory
└── package.json          # Závislosti
```

## 🔧 API Endpointy

- `GET /api/dataset-range` - Rozsah dat v datasetu
- `POST /api/run-test` - Single test
- `POST /api/grid-test` - Grid test
- `GET /api/download/[filename]` - Stahování souborů
- `POST /api/generate-heatmap` - Generování HeatMap

## 📊 Parametry

### Single Test:
- Varianta (Strict/Tolerant)
- RRR (1.0 - 10.0, krok 0.5)
- Exit Policy (Conservative/Optimistic)
- Overnight Mode (STANDARD/LONG-FLAT-LIMIT)
- Období testování
- Kapitál a risk %

### Grid Test:
- RRR: 5.0 - 8.0 (krok 0.5)
- Všechny kombinace variant a módů
- Custom nebo Full dataset rozsah

## 📈 Výstupy

### Excel soubory obsahují:
- **Obchody** - Detailní seznam obchodů
- **Denní_Analýza** - Analýza sekvencí
- **Sekvence** - Seznam nalezených sekvencí
- **Parametry** - Nastavení testu
- **Top_10_NetProfit** - Nejlepší podle zisku
- **Top_10_ProfitFactor** - Nejlepší podle PF

### HeatMap:
- Textový přehled podle RRR
- Statistiky a souhrny
- Exportovatelný formát

## 🎯 Výhody oproti Python verzím

- ⚡ **Rychlejší** - Optimalizované zpracování
- 📱 **Responzivní** - Funguje na všech zařízeních
- 🔒 **Typově bezpečné** - TypeScript eliminuje chyby
- 🎨 **Moderní UI** - Tailwind CSS design
- 🔄 **Better UX** - Loading stavy, error handling
- 🚀 **Snadné nasazení** - Vercel, Netlify ready

## 📁 Požadavky

- Node.js 18+
- npm nebo yarn
- Datový soubor: `XAUUSD_GMT+2_US-DST_D1.csv` (v root složce)

## 🔧 Vývoj

```bash
# Vývojový server
npm run dev

# Produkční build
npm run build
npm start

# Linting
npm run lint
```

## 🐛 Troubleshooting

### Module not found
```bash
npm install
```

### Dataset not found
Zkopírujte `XAUUSD_GMT+2_US-DST_D1.csv` do root složky projektu.

### Port již používán
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:3000 | xargs kill -9
```

## 📝 Poznámky

- Všechny výpočty jsou 100% kompatibilní s Python verzemi
- Excel soubory se ukládají do `public/` složky
- Podporuje současné spouštění více testů
- Optimalizováno pro produkční nasazení

## 🎯 Doporučené Použití

Ideální pro:
- Produkční prostředí
- Prezentace klientům
- Mobilní přístup
- Týmovou spolupráci
- Dlouhodobé nasazení
