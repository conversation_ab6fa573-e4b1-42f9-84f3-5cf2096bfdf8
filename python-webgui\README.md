# 🌐 Trend Taker - Python Web GUI

Webové rozhraní pro testování Marubozu sekvenční strategie postavené na Python HTTP serveru.

## 📋 Popis

Webová aplikace s HTML formulářem pro snadné nastavení parametrů a spouštění testů přes prohlížeč.

## 🚀 Spuštění

```bash
python "Trend Taker Web GUI.py"
```

Aplikace bude dostupná na: **http://localhost:8080**

## 📊 Funkce

- ✅ **Single Test** - Testování jednotlivých parametrů
- ✅ **Grid Test** - Kompletní grid test všech kombinací
- ✅ Webový formulář pro nastavení parametrů
- ✅ Real-time zobrazení výsledků
- ✅ Excel export a stahování
- ✅ HeatMap generování (textový formát)
- ✅ Automatické načítání rozsahu datasetu

## 🎛️ Parametry

### Single Test:
- Varianta (Strict/Tolerant)
- RRR (Risk/Reward ratio)
- Exit Policy (Conservative/Optimistic)
- Overnight Mode (STANDARD/LONG-FLAT-LIMIT)
- <PERSON>bdob<PERSON> testování
- Kapitál a risk %

### Grid Test:
- Automaticky testuje všechny kombinace
- RRR: 5.0 - 8.0 (krok 0.5)
- Všechny varianty a módy
- Custom nebo Full dataset

## 📁 Požadavky

- Python 3.7+
- pandas, numpy, openpyxl
- Datový soubor: `XAUUSD_GMT+2_US-DST_D1.csv`

## 🌐 Webové Rozhraní

### Hlavní funkce:
- 📊 Formulář pro nastavení parametrů
- ⏳ Loading indikátor během testování
- 📈 Real-time zobrazení výsledků
- 📥 Tlačítka pro stahování Excel souborů
- 🗑️ Vymazání výsledků

### API Endpointy:
- `GET /` - Hlavní stránka
- `POST /run_test` - Spuštění single testu
- `POST /grid_test` - Spuštění grid testu
- `GET /get_dataset_range` - Načtení rozsahu dat
- `GET /download/<filename>` - Stahování souborů
- `POST /generate_heatmap` - Generování HeatMap

## 📈 Výstupy

### Excel soubory:
- `TrendTaker_WebGUI_<variant>_<dates>.xlsx` (Single test)
- `MegaTest_Results_<timestamp>.xlsx` (Grid test)

### HeatMap:
- Textový přehled výsledků podle RRR
- Top kombinace podle Net Profit a Profit Factor

## 🔧 Technické Detaily

- **Server:** Python HTTP server
- **Port:** 8080
- **Frontend:** Vanilla HTML/CSS/JavaScript
- **Backend:** Python s pandas/numpy
- **Export:** openpyxl pro Excel

## 🎯 Použití

Ideální pro:
- Interaktivní testování
- Prezentace výsledků
- Rychlé experimenty s parametry
- Grid testing s vizualizací

## ⚠️ Opravy

- ✅ Opravena chyba s parametry v Grid testu
- ✅ Správné pořadí parametrů v `simulate_trades()`
- ✅ Funkční datumy a rozsahy dat

## 📝 Poznámky

- Automaticky otevře prohlížeč při spuštění
- Podporuje současné stahování více souborů
- Kompatibilní s ostatními verzemi aplikace
