#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trend Taker Test 1.0 - Web GUI Application
Webové rozhraní pro testování Marubozu sekvenční strategie
Spustí se na http://localhost:8080
"""

# Potlačení Python varování
import os
os.environ['PYTHONHOME'] = ''
os.environ['PYTHONPATH'] = ''

import http.server
import socketserver
import webbrowser
import threading
import json
import urllib.parse
from datetime import datetime
import pandas as pd
import numpy as np
from math import floor
import glob

# Konstanty pro práci s daty
DATA_DIRECTORY = r"C:\Temp\Trading\DATA ICM"
SUPPORTED_TIMEFRAMES = ['D1']

def is_d1_data_file(filename):
    """Detekuje zda je soubor D1 data podle názvu"""
    filename_upper = filename.upper()
    if not filename_upper.endswith('.CSV'):
        return False
    return ('_D1' in filename_upper or
            'D1_' in filename_upper or
            '_D1.' in filename_upper or
            'DAILY' in filename_upper)

def extract_symbol_from_filename(filename):
    """Extrahuje symbol z názvu souboru"""
    name_without_ext = filename.replace('.csv', '').replace('.CSV', '')
    # Pokusí se extrahovat symbol před prvním podtržítkem
    parts = name_without_ext.split('_')
    if parts:
        return parts[0].upper()
    return name_without_ext.upper()

def get_available_d1_files():
    """Načte seznam všech dostupných D1 souborů z datového adresáře"""
    try:
        if not os.path.exists(DATA_DIRECTORY):
            print(f"⚠️ Datový adresář neexistuje: {DATA_DIRECTORY}")
            return []

        csv_files = glob.glob(os.path.join(DATA_DIRECTORY, "*.csv"))
        d1_files = []

        for file_path in csv_files:
            filename = os.path.basename(file_path)
            if is_d1_data_file(filename):
                symbol = extract_symbol_from_filename(filename)
                d1_files.append({
                    'filename': filename,
                    'full_path': file_path,
                    'symbol': symbol,
                    'timeframe': 'D1',
                    'is_valid': os.path.exists(file_path) and os.path.getsize(file_path) > 0
                })

        # Seřadí podle symbolu
        return sorted(d1_files, key=lambda x: x['symbol'])

    except Exception as e:
        print(f"❌ Chyba při načítání D1 souborů: {e}")
        return []

def get_data_file_path(filename):
    """Získá úplnou cestu k datovému souboru"""
    if os.path.isabs(filename):
        return filename
    return os.path.join(DATA_DIRECTORY, filename)

def validate_d1_data_file(file_path):
    """Validuje zda je soubor platný D1 datový soubor"""
    try:
        if not os.path.exists(file_path):
            return False, 'Soubor neexistuje'

        filename = os.path.basename(file_path)
        if not is_d1_data_file(filename):
            return False, 'Soubor není D1 data (musí obsahovat D1 v názvu)'

        if os.path.getsize(file_path) == 0:
            return False, 'Soubor je prázdný'

        # Základní kontrola CSV formátu
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:5]

        if len(lines) < 2:
            return False, 'Soubor má méně než 2 řádky'

        # Kontrola zda obsahuje CSV data
        data_line = None
        for line in lines:
            if line.strip() and not line.startswith('#'):
                data_line = line
                break

        if not data_line:
            return False, 'Nenalezena platná data řádka'

        columns = len(data_line.split(','))
        if columns < 4:
            return False, 'Nedostatek sloupců v CSV (očekáváno alespoň 4)'

        return True, 'OK'

    except Exception as e:
        return False, f'Chyba při validaci souboru: {str(e)}'

class TrendTakerWebHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Konstanty
        self.USD_PER_BOD_PER_LOT = 100.0
        self.SPREAD_BOD = 0.15
        self.SWAP_LONG_BOD = -0.55237
        self.SWAP_SHORT_BOD = 0.29425
        self.WICK_RATIO_MAX = 0.18
        self.MIN_BODY_RATIO = 0.60  # Minimální velikost těla svíčky (60% rozsahu)
        self.ATR_PERIOD = 10
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_html_page()
        elif self.path.startswith('/get_dataset_range'):
            self.serve_dataset_range()
        elif self.path == '/get_available_files':
            self.serve_available_files()
        elif self.path.startswith('/download/'):
            self.handle_download()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path == '/run_test':
            self.handle_test_post()
        elif self.path == '/grid_test':
            self.handle_grid_test_post()
        elif self.path == '/generate_heatmap':
            self.handle_heatmap_post()
        else:
            self.send_error(404)
    
    def send_html_page(self):
        html_content = """
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trend Taker Test 1.0 - Web GUI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; margin-bottom: 30px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: inline-block; width: 150px; font-weight: bold; }
        .form-group input, .form-group select { width: 200px; padding: 5px; border: 1px solid #ddd; border-radius: 4px; }
        .form-group select.wide { width: 400px; }
        .button { background-color: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .button:hover { background-color: #2980b9; }
        .button:disabled { background-color: #bdc3c7; cursor: not-allowed; }
        .form-group.disabled { opacity: 0.5; pointer-events: none; }
        .form-group.disabled input, .form-group.disabled select { background-color: #f5f5f5; color: #999; }
        .grid-only { display: none; }
        .date-fields.disabled { opacity: 0.5; pointer-events: none; }
        .results { margin-top: 20px; padding: 15px; background-color: #ecf0f1; border-radius: 5px; min-height: 200px; }
        .results pre { white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 12px; }
        .loading { display: none; text-align: center; margin: 20px; }
        .error { color: #e74c3c; font-weight: bold; }
        .success { color: #27ae60; font-weight: bold; }
        .variant-rules { margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; border: 1px solid #e9ecef; display: none; }
        .rules-section h4 { margin: 0 0 10px 0; color: #2c3e50; }
        .rule-item { margin-bottom: 10px; }
        .rule-item strong { color: #2c3e50; }
        .rule-item ul { margin: 5px 0 0 20px; padding: 0; }
        .rule-item li { margin-bottom: 3px; font-size: 13px; color: #555; }
        .help-icon { cursor: pointer; margin-left: 5px; color: #3498db; font-size: 16px; }
        .help-icon:hover { color: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Trend Taker Test 1.0</h1>
            <h2>Web GUI Interface</h2>
        </div>
        
        <form id="testForm">
            <div class="form-group">
                <label for="testMode">Režim testování:</label>
                <select id="testMode" name="testMode" class="wide" onchange="toggleTestMode()">
                    <option value="SINGLE">SINGLE TEST (jeden parametr set)</option>
                    <option value="GRID">GRID TEST (všechny kombinace)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="dataFile">Datový soubor (D1 data z C:\\Temp\\Trading\\DATA ICM):</label>
                <select id="dataFile" name="dataFile" class="wide">
                    <option value="">-- Načítám dostupné D1 soubory --</option>
                </select>
                <div id="dataFileInfo" style="margin-top: 5px; font-size: 12px; color: #666;"></div>
            </div>
            
            <div class="form-group" id="dataRangeGroup">
                <label for="dataRange">Rozsah dat:</label>
                <select id="dataRange" name="dataRange" class="wide" onchange="toggleDataRange()">
                    <option value="CUSTOM">Custom Data (vlastní rozsah)</option>
                    <option value="FULL">Full Dataset (celý dataset)</option>
                </select>
            </div>

            <div class="form-group date-fields" id="startDateGroup">
                <label for="startDate">Datum od:</label>
                <input type="date" id="startDate" name="startDate" value="2020-01-01">
            </div>

            <div class="form-group date-fields" id="endDateGroup">
                <label for="endDate">Datum do:</label>
                <input type="date" id="endDate" name="endDate" value="2025-09-05">
            </div>
            
            <div class="form-group single-only" id="variantGroup">
                <label for="variant">Varianta: <span class="help-icon" onclick="toggleVariantRules()" title="Zobrazit pravidla variant">❓</span></label>
                <select id="variant" name="variant">
                    <option value="Strict">Strict</option>
                    <option value="Tolerant" selected>Tolerant</option>
                </select>
                <div class="variant-rules">
                    <div class="rules-section">
                        <h4>📋 Pravidla variant:</h4>
                        <div class="rule-item">
                            <strong>🔒 Strict:</strong>
                            <ul>
                                <li>Pouze přesné Marubozu svíčky (wick ratio < 0.18)</li>
                                <li>Žádné tolerance pro opačné svíčky</li>
                                <li>Sekvence končí při první opačné svíčce</li>
                                <li>Konzervativní přístup - méně obchodů, vyšší přesnost</li>
                            </ul>
                        </div>
                        <div class="rule-item">
                            <strong>🔄 Tolerant:</strong>
                            <ul>
                                <li>Marubozu svíčky (wick ratio < 0.18) + tolerance</li>
                                <li>Toleruje opačné svíčky pokud neprolomí klíčové úrovně:</li>
                                <li>• UP sekvence: Red svíčka OK pokud Close ≥ předchozí Low</li>
                                <li>• DOWN sekvence: Green svíčka OK pokud Close ≤ předchozí High</li>
                                <li>Opačná Marubozu vždy ukončí sekvenci a začne novou</li>
                                <li>Flexibilní přístup - více obchodů, zachytí pokračování trendů</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group single-only" id="rrrGroup">
                <label for="rrr">Risk/Reward:</label>
                <input type="number" id="rrr" name="rrr" value="6.5" min="1" max="20" step="0.5">
            </div>

            <div class="form-group single-only" id="exitPolicyGroup">
                <label for="exitPolicy">Exit Policy:</label>
                <select id="exitPolicy" name="exitPolicy">
                    <option value="Optimistic" selected>Optimistic</option>
                    <option value="Conservative">Conservative</option>
                </select>
            </div>

            <div class="form-group single-only" id="overnightModeGroup">
                <label for="overnightMode">Overnight Mode:</label>
                <select id="overnightMode" name="overnightMode" class="wide">
                    <option value="STANDARD">STANDARD (držíme pozice přes noc)</option>
                    <option value="LONG-FLAT-LIMIT" selected>LONG-FLAT-LIMIT (Long intraday + Buy Limit)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="startEquity">Počáteční kapitál:</label>
                <input type="number" id="startEquity" name="startEquity" value="10000" min="1000">
            </div>
            
            <div class="form-group">
                <label for="riskPct">Risk % per trade:</label>
                <input type="number" id="riskPct" name="riskPct" value="2.0" min="0.1" max="10" step="0.1">
            </div>

            <div class="form-group">
                <label for="minBodyRatio">Min. velikost těla svíčky (%):</label>
                <input type="number" id="minBodyRatio" name="minBodyRatio" value="60" min="30" max="90" step="5">
            </div>

            <div class="form-group single-only" id="buyLimitOffsetGroup">
                <label for="buyLimitOffset">Buy Limit offset (body):</label>
                <input type="number" id="buyLimitOffset" name="buyLimitOffset" value="5" min="1" max="20" step="1">
            </div>

            <div class="form-group single-only" id="longLmtSlMethodGroup">
                <label for="longLmtSlMethod">LONG LMT SL Method:</label>
                <select id="longLmtSlMethod" name="longLmtSlMethod" onchange="toggleBarsBackField()">
                    <option value="Initial">Initial (současný)</option>
                    <option value="BarsBack_Low">BarsBack Low</option>
                    <option value="BarsBack_50pct">BarsBack 50%</option>
                </select>
            </div>

            <div class="form-group single-only" id="barsBackGroup" style="display: none;">
                <label for="barsBack">BarsBack - X:</label>
                <input type="number" id="barsBack" name="barsBack" value="3" min="1" max="10" step="1">
                <small>Počet barů zpětně od aktuální svíčky pro SL nastavení</small>
            </div>
            
            <div style="text-align: center; margin: 20px 0;">
                <button type="submit" class="button" id="runButton">🚀 Spustit Test</button>
                <button type="button" class="button" onclick="clearResults()" style="background-color: #e74c3c;">� Vymazat</button>
                <button type="button" class="button" onclick="downloadExcel()" id="downloadButton" style="background-color: #27ae60; display: none;">� Stáhnout Excel</button>
                <button type="button" class="button" onclick="generateHeatmap()" id="heatmapButton" style="background-color: #9b59b6; display: none;">� HeatMap</button>
            </div>
        </form>
        
        <div class="loading" id="loading">
            <h3>⏳ Probíhá testování...</h3>
            <p>Prosím čekejte, test může trvat několik minut.</p>
        </div>
        
        <div class="results" id="results">
            <h3>📊 Výsledky testu:</h3>
            <pre id="resultsText">Připraven k testování...\nVyberte parametry a klikněte na 'Spustit Test'</pre>
        </div>
    </div>

    <script>
        let lastExcelFile = '';

        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const testMode = document.getElementById('testMode').value;
            if (testMode === 'SINGLE') {
                runTest();
            } else {
                runGridTest();
            }
        });

        function toggleTestMode() {
            const testMode = document.getElementById('testMode').value;
            const singleOnlyElements = document.querySelectorAll('.single-only');
            const gridOnlyElements = document.querySelectorAll('.grid-only');

            if (testMode === 'GRID') {
                // Disable single-only fields for GRID test
                singleOnlyElements.forEach(element => {
                    element.classList.add('disabled');
                });
                // Show grid-only fields
                gridOnlyElements.forEach(element => {
                    element.style.display = 'block';
                });
                document.getElementById('runButton').textContent = '🔥 Spustit GRID TEST';

                // Initialize data range toggle
                toggleDataRange();
            } else {
                // Enable all fields for SINGLE test
                singleOnlyElements.forEach(element => {
                    element.classList.remove('disabled');
                });
                // Hide grid-only fields
                gridOnlyElements.forEach(element => {
                    element.style.display = 'none';
                });
                document.getElementById('runButton').textContent = '🚀 Spustit Test';

                // Initialize data range toggle for single test too
                toggleDataRange();
            }
        }

        function toggleDataRange() {
            const dataRange = document.getElementById('dataRange').value;
            const dateFields = document.querySelectorAll('.date-fields');

            if (dataRange === 'FULL') {
                // Disable date fields and load actual dataset range
                dateFields.forEach(element => {
                    element.classList.add('disabled');
                });

                // Získá vybraný soubor
                const dataFileSelect = document.getElementById('dataFile');
                const selectedFile = dataFileSelect ? dataFileSelect.value : '';

                if (!selectedFile) {
                    console.error('❌ Žádný soubor není vybrán pro Full Dataset');
                    // Fallback values
                    document.getElementById('startDate').value = '2003-01-01';
                    document.getElementById('endDate').value = '2025-12-31';
                    return;
                }

                console.log('📊 Načítám Full Dataset rozsah pro soubor:', selectedFile);

                // Load actual dataset range from server
                fetch(`/get_dataset_range?filename=${encodeURIComponent(selectedFile)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('📈 Full Dataset response:', data);
                    if (data.success) {
                        const startDateInput = document.getElementById('startDate');
                        const endDateInput = document.getElementById('endDate');

                        startDateInput.value = data.start_date;
                        endDateInput.value = data.end_date;

                        console.log(`✅ Full Dataset nastaveno: ${data.start_date} - ${data.end_date}`);
                        console.log(`📅 Skutečné hodnoty v input polích: startDate="${startDateInput.value}", endDate="${endDateInput.value}"`);
                    } else {
                        console.error('❌ Chyba při načítání Full Dataset:', data.error);
                        // Fallback values
                        document.getElementById('startDate').value = '2003-01-01';
                        document.getElementById('endDate').value = '2025-12-31';
                    }
                })
                .catch(error => {
                    console.error('❌ Error loading dataset range:', error);
                    // Fallback values
                    document.getElementById('startDate').value = '2003-01-01';
                    document.getElementById('endDate').value = '2025-12-31';
                });
            } else {
                // Enable date fields for custom range
                dateFields.forEach(element => {
                    element.classList.remove('disabled');
                });

                // Nastavit Custom Data: od = aktuální datum - 1 měsíc, do = konec datasetu
                const dataFileSelect = document.getElementById('dataFile');
                const selectedFile = dataFileSelect ? dataFileSelect.value : '';

                if (selectedFile) {
                    // Načíst konec datasetu ze serveru
                    fetch(`/get_dataset_range?filename=${encodeURIComponent(selectedFile)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Aktuální datum - 1 měsíc
                            const today = new Date();
                            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
                            const startDate = oneMonthAgo.toISOString().split('T')[0];

                            document.getElementById('startDate').value = startDate;
                            document.getElementById('endDate').value = data.end_date;

                            console.log(`✅ Custom Data nastaveno: ${startDate} - ${data.end_date}`);
                        } else {
                            // Fallback
                            const today = new Date();
                            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
                            document.getElementById('startDate').value = oneMonthAgo.toISOString().split('T')[0];
                            document.getElementById('endDate').value = '2025-09-05';
                        }
                    })
                    .catch(error => {
                        console.error('❌ Error loading dataset range for Custom:', error);
                        // Fallback
                        const today = new Date();
                        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
                        document.getElementById('startDate').value = oneMonthAgo.toISOString().split('T')[0];
                        document.getElementById('endDate').value = '2025-09-05';
                    });
                } else {
                    // Fallback když není vybrán soubor
                    const today = new Date();
                    const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
                    document.getElementById('startDate').value = oneMonthAgo.toISOString().split('T')[0];
                    document.getElementById('endDate').value = '2025-09-05';
                }
            }
        }

        // Initialize on page load
        toggleTestMode();

        function runTest() {
            const formData = new FormData(document.getElementById('testForm'));
            const data = Object.fromEntries(formData);

            document.getElementById('runButton').disabled = true;
            document.getElementById('downloadButton').style.display = 'none';
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultsText').textContent = 'Spouštím test...\\n';

            fetch('/run_test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('runButton').disabled = false;

                if (data.success) {
                    document.getElementById('resultsText').textContent = data.results;
                    if (data.excel_file) {
                        lastExcelFile = data.excel_file;
                        document.getElementById('downloadButton').style.display = 'inline-block';
                    }
                } else {
                    document.getElementById('resultsText').textContent = 'CHYBA: ' + data.error;
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('runButton').disabled = false;
                document.getElementById('resultsText').textContent = 'CHYBA: ' + error;
            });
        }

        function runMegaTest() {
            if (!confirm('MEGA TEST spustí kompletní grid test se všemi kombinacemi parametrů (RRR 5.0-12.0, oba režimy, oba exit policies). Může trvat několik minut. Pokračovat?')) {
                return;
            }

            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('runButton').disabled = true;

            fetch('/mega_test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').style.display = 'block';
                document.getElementById('runButton').disabled = false;

                if (data.success) {
                    document.getElementById('resultsText').textContent = data.results;
                    if (data.excel_file) {
                        lastExcelFile = data.excel_file;
                        document.getElementById('downloadButton').style.display = 'inline-block';
                    }
                } else {
                    document.getElementById('resultsText').textContent = 'CHYBA: ' + data.error;
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').style.display = 'block';
                document.getElementById('runButton').disabled = false;
                document.getElementById('resultsText').textContent = 'CHYBA: ' + error;
            });
        }

        function runGridTest() {
            if (!confirm('GRID TEST spustí kompletní grid test se všemi kombinacemi parametrů (RRR 5.0-12.0, oba režimy, oba exit policies). Může trvat několik minut. Pokračovat?')) {
                return;
            }

            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('runButton').disabled = true;

            const formData = {
                testMode: 'GRID',
                dataFile: document.getElementById('dataFile').value,
                dataRange: document.getElementById('dataRange').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                startEquity: document.getElementById('startEquity').value,
                riskPct: document.getElementById('riskPct').value,
                minBodyRatio: document.getElementById('minBodyRatio').value,
                buyLimitOffset: document.getElementById('buyLimitOffset').value,
                longLmtSlMethod: document.getElementById('longLmtSlMethod').value,
                barsBack: document.getElementById('barsBack').value
            };

            console.log('🔥 GRID TEST formData:', formData);

            fetch('/grid_test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').style.display = 'block';
                document.getElementById('runButton').disabled = false;

                if (data.success) {
                    document.getElementById('resultsText').textContent = data.results;
                    if (data.excel_file) {
                        lastExcelFile = data.excel_file;
                        document.getElementById('downloadButton').style.display = 'inline-block';
                        document.getElementById('heatmapButton').style.display = 'inline-block';
                    }
                } else {
                    document.getElementById('resultsText').textContent = 'CHYBA: ' + data.error;
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results').style.display = 'block';
                document.getElementById('runButton').disabled = false;
                document.getElementById('resultsText').textContent = 'CHYBA: ' + error;
            });
        }

        function generateHeatmap() {
            if (!lastExcelFile) {
                alert('Nejdříve spusťte GRID TEST!');
                return;
            }

            fetch('/generate_heatmap', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({excel_file: lastExcelFile})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('HeatMap vygenerována: ' + data.heatmap_file);
                    window.open('/download/' + data.heatmap_file, '_blank');
                } else {
                    alert('Chyba při generování HeatMap: ' + data.error);
                }
            })
            .catch(error => {
                alert('Chyba při generování HeatMap: ' + error);
            });
        }

        function clearResults() {
            document.getElementById('resultsText').textContent = 'Výsledky vymazány.\\n\\nPřipraven k testování...';
            document.getElementById('downloadButton').style.display = 'none';
            lastExcelFile = '';
        }

        function toggleVariantRules() {
            const rulesDiv = document.querySelector('.variant-rules');
            if (rulesDiv.style.display === 'none' || rulesDiv.style.display === '') {
                rulesDiv.style.display = 'block';
            } else {
                rulesDiv.style.display = 'none';
            }
        }

        function toggleBarsBackField() {
            const slMethod = document.getElementById('longLmtSlMethod').value;
            const barsBackGroup = document.getElementById('barsBackGroup');

            if (slMethod === 'BarsBack_Low' || slMethod === 'BarsBack_50pct') {
                barsBackGroup.style.display = 'block';
            } else {
                barsBackGroup.style.display = 'none';
            }
        }

        function downloadExcel() {
            if (lastExcelFile) {
                window.open('/download/' + lastExcelFile, '_blank');
            } else {
                alert('Žádný Excel soubor k stažení!');
            }
        }

        // Načtení dostupných D1 souborů
        async function loadAvailableFiles() {
            console.log('🔍 Načítám dostupné D1 soubory...');
            try {
                const response = await fetch('/get_available_files');
                console.log('📡 Response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('📊 Received data:', data);

                const select = document.getElementById('dataFile');
                const info = document.getElementById('dataFileInfo');

                if (!select || !info) {
                    throw new Error('HTML elementy dataFile nebo dataFileInfo nenalezeny');
                }

                select.innerHTML = '';

                if (data.success && data.files && data.files.length > 0) {
                    console.log(`✅ Nalezeno ${data.files.length} D1 souborů`);
                    select.innerHTML = '<option value="">-- Vyberte D1 datový soubor --</option>';

                    data.files.forEach(file => {
                        const option = document.createElement('option');
                        option.value = file.filename;
                        option.textContent = `${file.symbol} - ${file.filename} (${file.total_records || 'N/A'} záznamů)`;
                        select.appendChild(option);
                    });

                    // Automaticky vybere první soubor
                    if (data.files.length > 0) {
                        select.value = data.files[0].filename;
                        loadDatasetRange();
                    }

                    info.innerHTML = `✅ Nalezeno ${data.files.length} D1 souborů`;
                    info.style.color = '#27ae60';
                } else {
                    console.log('❌ Žádné D1 soubory nenalezeny nebo chyba:', data.error);
                    select.innerHTML = '<option value="">❌ Nenalezeny žádné D1 soubory</option>';
                    info.innerHTML = `❌ ${data.error || 'Nenalezeny žádné D1 datové soubory'}`;
                    info.style.color = '#e74c3c';
                }
            } catch (error) {
                console.error('❌ Chyba při načítání souborů:', error);
                const select = document.getElementById('dataFile');
                const info = document.getElementById('dataFileInfo');

                if (select && info) {
                    select.innerHTML = '<option value="">❌ Chyba při načítání</option>';
                    info.innerHTML = `❌ Chyba při načítání dostupných souborů: ${error.message}`;
                    info.style.color = '#e74c3c';
                } else {
                    console.error('❌ HTML elementy nenalezeny!');
                }
            }
        }

        // Načtení rozsahu dat pro vybraný soubor
        async function loadDatasetRange() {
            const dataFileSelect = document.getElementById('dataFile');
            const info = document.getElementById('dataFileInfo');

            if (!dataFileSelect || !dataFileSelect.value) {
                console.log('⚠️ Žádný soubor není vybrán');
                return;
            }

            console.log('📊 Načítám rozsah dat pro soubor:', dataFileSelect.value);

            try {
                const response = await fetch(`/get_dataset_range?filename=${encodeURIComponent(dataFileSelect.value)}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('📈 Dataset range data:', data);

                if (data.success) {
                    const startDate = data.start_date;
                    const endDate = data.end_date;
                    const totalRecords = data.total_records;
                    const symbol = data.symbol;

                    info.innerHTML = `✅ ${symbol}: ${totalRecords.toLocaleString()} záznamů (${startDate} - ${endDate})`;
                    info.style.color = '#27ae60';

                    console.log(`✅ Dataset info: ${symbol}, ${totalRecords} záznamů, ${startDate} - ${endDate}`);
                } else {
                    info.innerHTML = `❌ ${data.error}`;
                    info.style.color = '#e74c3c';
                    console.error('❌ Dataset range error:', data.error);
                }
            } catch (error) {
                console.error('❌ Chyba při načítání rozsahu dat:', error);
                if (info) {
                    info.innerHTML = `❌ Chyba při načítání informací o souboru: ${error.message}`;
                    info.style.color = '#e74c3c';
                }
            }
        }

        // Načtení při startu stránky
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, inicializuji aplikaci...');

            // Nejdříve načte dostupné soubory
            loadAvailableFiles().then(() => {
                console.log('📁 Soubory načteny, inicializuji datumy...');

                // Po načtení souborů a dat inicializuj datumy podle defaultní volby
                setTimeout(() => {
                    toggleDataRange();
                    console.log('✅ Inicializace datumů dokončena');
                }, 800); // Delší zpoždění pro načtení dataset range
            });

            // Přidá event listenery
            const dataFileSelect = document.getElementById('dataFile');
            const dataRangeSelect = document.getElementById('dataRange');

            if (dataFileSelect) {
                dataFileSelect.addEventListener('change', function() {
                    console.log('📁 Změna souboru:', this.value);
                    if (this.value) {
                        loadDatasetRange();

                        // Pokud je vybrán Full Dataset, aktualizuj datumy
                        if (dataRangeSelect && dataRangeSelect.value === 'FULL') {
                            console.log('🔄 Aktualizuji Full Dataset datumy pro nový soubor');
                            setTimeout(() => toggleDataRange(), 300);
                        }
                    }
                });
            } else {
                console.error('❌ Element dataFile nenalezen!');
            }

            // Event listener pro změnu rozsahu dat
            if (dataRangeSelect) {
                dataRangeSelect.addEventListener('change', function() {
                    console.log('📅 Změna rozsahu dat:', this.value);
                    toggleDataRange();
                });
            } else {
                console.error('❌ Element dataRange nenalezen!');
            }
        });
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html_content.encode('utf-8'))

    def serve_available_files(self):
        """Vrátí seznam dostupných D1 datových souborů"""
        try:
            available_files = get_available_d1_files()

            if not available_files:
                response = {
                    'success': False,
                    'error': f'Nenalezeny žádné D1 datové soubory v adresáři {DATA_DIRECTORY}',
                    'files': []
                }
            else:
                # Získá základní informace o každém souboru
                files_with_info = []
                for file_info in available_files:
                    try:
                        is_valid, error_msg = validate_d1_data_file(file_info['full_path'])
                        if is_valid:
                            # Pokus o načtení základních informací
                            try:
                                df = pd.read_csv(file_info['full_path'], nrows=1000)  # Načte jen prvních 1000 řádků pro rychlost
                                total_records = len(pd.read_csv(file_info['full_path']))
                                files_with_info.append({
                                    'filename': file_info['filename'],
                                    'symbol': file_info['symbol'],
                                    'timeframe': file_info['timeframe'],
                                    'is_valid': True,
                                    'total_records': total_records
                                })
                            except:
                                files_with_info.append({
                                    'filename': file_info['filename'],
                                    'symbol': file_info['symbol'],
                                    'timeframe': file_info['timeframe'],
                                    'is_valid': True,
                                    'total_records': 'N/A'
                                })
                    except:
                        continue

                response = {
                    'success': True,
                    'files': files_with_info,
                    'total_files': len(available_files),
                    'valid_files': len(files_with_info)
                }
        except Exception as e:
            response = {
                'success': False,
                'error': f'Chyba při načítání datových souborů: {str(e)}',
                'files': []
            }

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def serve_dataset_range(self):
        """Vrátí skutečný rozsah dat v datasetu"""
        try:
            # Získá název souboru z query parametru
            query_params = urllib.parse.parse_qs(urllib.parse.urlparse(self.path).query)
            filename = query_params.get('filename', [None])[0]

            if not filename:
                response = {
                    'success': False,
                    'error': 'Název souboru není specifikován. Použijte ?filename=nazev_souboru.csv'
                }
            else:
                data_file_path = get_data_file_path(filename)
                is_valid, error_msg = validate_d1_data_file(data_file_path)

                if not is_valid:
                    response = {
                        'success': False,
                        'error': f'Neplatný D1 datový soubor: {error_msg}'
                    }
                else:
                    df = pd.read_csv(data_file_path, encoding='utf-8')

                    # Standardizace názvů sloupců
                    column_mapping = {}
                    for col in df.columns:
                        col_lower = col.lower().strip()
                        if col_lower in ['datum', 'date']:
                            column_mapping[col] = 'Datum'
                        elif col_lower in ['čas', 'cas', 'time']:
                            column_mapping[col] = 'Čas'

                    df = df.rename(columns=column_mapping)

                    # Parsování datumu s podporou různých formátů
                    def parse_datetime_row(row):
                        try:
                            datum_str = str(row['Datum']).strip()
                            cas_str = str(row.get('Čas', '00:00')).strip()

                            # Normalizace datumu
                            if '.' in datum_str:
                                datum_str = datum_str.replace('.', '-')

                            # Normalizace času
                            if ':' in cas_str and len(cas_str.split(':')) == 3:
                                cas_str = cas_str[:5]

                            return pd.to_datetime(f"{datum_str} {cas_str}", errors='coerce')
                        except:
                            return pd.NaT

                    if 'Datum' in df.columns:
                        df['Date'] = df.apply(parse_datetime_row, axis=1)
                    else:
                        # Pokus o první sloupec jako datum
                        df['Date'] = pd.to_datetime(df.iloc[:, 0], errors='coerce')

                    start_date = df['Date'].min().strftime('%Y-%m-%d')
                    end_date = df['Date'].max().strftime('%Y-%m-%d')
                    symbol = extract_symbol_from_filename(filename)

                    response = {
                        'success': True,
                        'start_date': start_date,
                        'end_date': end_date,
                        'total_records': len(df),
                        'symbol': symbol,
                        'filename': filename,
                        'file_path': data_file_path
                    }
        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def handle_test_post(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            # Spuštění testu
            results, excel_file = self.run_trend_taker_test(data)

            response = {
                'success': True,
                'results': results,
                'excel_file': excel_file
            }

        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def handle_grid_test_post(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            # Spuštění GRID testu
            results, excel_file = self.run_grid_test(data)

            response = {
                'success': True,
                'results': results,
                'excel_file': excel_file
            }

        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def handle_heatmap_post(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            # Generování HeatMap
            heatmap_file = self.generate_heatmap(data['excel_file'])

            response = {
                'success': True,
                'heatmap_file': heatmap_file
            }

        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

    def handle_download(self):
        """Handler pro stahování Excel a CSV souborů"""
        try:
            filename = self.path.split('/')[-1]
            filepath = os.path.join(os.getcwd(), filename)

            if os.path.exists(filepath):
                self.send_response(200)

                # Nastavení správného Content-Type
                if filename.endswith('.xlsx'):
                    self.send_header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                elif filename.endswith('.csv'):
                    self.send_header('Content-Type', 'text/csv; charset=utf-8')
                else:
                    self.send_header('Content-Type', 'application/octet-stream')

                self.send_header('Content-Disposition', f'attachment; filename="{filename}"')
                self.send_header('Content-Length', str(os.path.getsize(filepath)))
                self.end_headers()

                # Stahování po částech
                with open(filepath, 'rb') as f:
                    while True:
                        chunk = f.read(8192)
                        if not chunk:
                            break
                        self.wfile.write(chunk)

                print(f"✅ Soubor stažen: {filename}")
            else:
                print(f"❌ Soubor nenalezen: {filepath}")
                self.send_error(404, "Soubor nenalezen")
        except Exception as e:
            print(f"❌ Chyba při stahování: {e}")
            self.send_error(500, f"Chyba při stahování: {str(e)}")
    
    def run_trend_taker_test(self, params):
        results = []
        excel_filename = None
        results.append("🚀 Spouštím Trend Taker Test...\n")

        try:
            # Načtení parametrů
            data_file = params.get('dataFile')
            if not data_file:
                raise ValueError('Datový soubor není specifikován')

            data_range = params.get('dataRange', 'CUSTOM')
            start_date = params.get('startDate', '2020-01-01')
            end_date = params.get('endDate', '2025-09-05')
            variant = params.get('variant', 'Tolerant')
            rrr = float(params.get('rrr', 6.5))
            exit_policy = params.get('exitPolicy', 'Optimistic')
            overnight_mode = params.get('overnightMode', 'LONG-FLAT-LIMIT')
            start_equity = float(params.get('startEquity', 10000))
            risk_pct = float(params.get('riskPct', 2.0)) / 100.0
            min_body_ratio = float(params.get('minBodyRatio', 60)) / 100.0  # Převod z % na desetinné číslo
            buy_limit_offset = float(params.get('buyLimitOffset', 5))  # Body pro Buy Limit offset
            long_lmt_sl_method = params.get('longLmtSlMethod', 'Initial')  # Metoda SL pro LONG LMT
            bars_back = int(params.get('barsBack', 3))  # Počet barů zpětně

            # Získá úplnou cestu k souboru a validuje
            data_file_path = get_data_file_path(data_file)
            is_valid, error_msg = validate_d1_data_file(data_file_path)
            if not is_valid:
                raise ValueError(f'Neplatný D1 datový soubor: {error_msg}')

            # Načtení dat pro určení rozsahu (pokud je potřeba)
            raw_df = pd.read_csv(data_file_path, encoding='utf-8')

            # Standardizace názvů sloupců pro datum
            date_column_mapping = {}
            for col in raw_df.columns:
                col_lower = col.lower().strip()
                if col_lower in ['datum', 'date']:
                    date_column_mapping[col] = 'Date'
                    break

            if not date_column_mapping:
                raise ValueError('Sloupec s datem nebyl nalezen')

            raw_df = raw_df.rename(columns=date_column_mapping)
            raw_df['Date'] = pd.to_datetime(raw_df['Date'])

            # Určení rozsahu dat
            if data_range == 'FULL':
                # Full Dataset - použij celý rozsah dat
                actual_start_date = raw_df['Date'].min().strftime('%Y-%m-%d')
                actual_end_date = raw_df['Date'].max().strftime('%Y-%m-%d')
                start_date = actual_start_date
                end_date = actual_end_date
                results.append(f"📊 Full Dataset: {start_date} až {end_date}\n")
            else:
                # Custom Data - použij zadané datum
                results.append(f"📊 Custom Data: {start_date} až {end_date}\n")

            results.append(f"📊 Parametry testu:\n")
            results.append(f"   • Soubor: {data_file}\n")
            results.append(f"   • Dataset: {'Full Dataset (celý dataset)' if data_range == 'FULL' else 'Custom Data (vlastní rozsah)'}\n")
            results.append(f"   • Období: {start_date} - {end_date}\n")
            results.append(f"   • Varianta: {variant}\n")
            results.append(f"   • RRR: {rrr}\n")
            results.append(f"   • Exit Policy: {exit_policy}\n")
            results.append(f"   • Overnight Mode: {overnight_mode}\n")
            results.append(f"   • Kapitál: ${start_equity:,.0f}\n")
            results.append(f"   • Risk: {risk_pct*100:.1f}%\n\n")

            # Kontrola existence souboru (už byla provedena ve validate_d1_data_file, ale pro jistotu)
            if not os.path.exists(data_file_path):
                raise FileNotFoundError(f"Soubor {data_file} nebyl nalezen na cestě: {data_file_path}")

            # Použití už načtených dat
            results.append("📂 Zpracovávám D1 data...\n")
            raw = raw_df.copy()

            # Standardizace názvů sloupců - podporuje různé formáty
            column_mapping = {}
            for col in raw.columns:
                col_lower = col.lower().strip()
                if col_lower in ['datum', 'date']:
                    column_mapping[col] = 'Date'
                elif col_lower in ['čas', 'cas', 'time']:
                    column_mapping[col] = 'Time'
                elif col_lower in ['otevírací', 'oteviraci', 'open', 'o']:
                    column_mapping[col] = 'Open'
                elif col_lower in ['nejvyšší', 'nejvyssi', 'high', 'h']:
                    column_mapping[col] = 'High'
                elif col_lower in ['nejnižší', 'nejnizsi', 'low', 'l']:
                    column_mapping[col] = 'Low'
                elif col_lower in ['uzavírací', 'uzaviraci', 'close', 'c']:
                    column_mapping[col] = 'Close'
                elif col_lower in ['volume', 'objem', 'objem tiků', 'v']:
                    column_mapping[col] = 'Volume'

            raw = raw.rename(columns=column_mapping)

            # Konverze datumu - podporuje různé formáty
            def parse_datetime(row):
                try:
                    datum_str = str(row['Date']).strip()
                    cas_str = str(row['Time']).strip()

                    # Normalizace datumu
                    if '.' in datum_str:
                        # 2003.05.05 -> 2003-05-05
                        datum_str = datum_str.replace('.', '-')

                    # Normalizace času - odstraní vteřiny
                    if ':' in cas_str and len(cas_str.split(':')) == 3:
                        # 00:00:00 -> 00:00
                        cas_str = cas_str[:5]

                    return pd.to_datetime(f"{datum_str} {cas_str}", errors='coerce')
                except:
                    return pd.NaT

            raw["dt"] = raw.apply(parse_datetime, axis=1)

            # Konverze numerických hodnot
            for col in ["Open","High","Low","Close"]:
                if col in raw.columns:
                    raw[col] = pd.to_numeric(raw[col], errors="coerce")

            # Vyčištění a validace
            raw = raw.dropna(subset=["dt","Open","High","Low","Close"]).copy()

            # Validace OHLC dat
            valid_mask = (
                (raw["Open"] > 0) &
                (raw["High"] > 0) &
                (raw["Low"] > 0) &
                (raw["Close"] > 0) &
                (raw["High"] >= raw[["Open", "Close"]].max(axis=1)) &
                (raw["Low"] <= raw[["Open", "Close"]].min(axis=1))
            )
            raw = raw[valid_mask].copy()

            raw = raw.sort_values("dt").reset_index(drop=True)
            results.append(f"✅ Načteno {len(raw)} D1 barů\n")

            # Výpočet indikátorů na celém datasetu (pro správný ATR)
            results.append("📊 Počítám indikátory na celém datasetu...\n")
            o_all = raw["Open"].to_numpy(float)
            h_all = raw["High"].to_numpy(float)
            l_all = raw["Low"].to_numpy(float)
            c_all = raw["Close"].to_numpy(float)

            # ATR výpočet na celém datasetu
            rng_all = h_all - l_all
            prev_c_all = np.concatenate([[np.nan], c_all[:-1]])
            tr_all = np.maximum.reduce([rng_all, np.abs(h_all - prev_c_all), np.abs(l_all - prev_c_all)])
            atr_series_all = pd.Series(tr_all).rolling(self.ATR_PERIOD).mean()
            atr10_s1_all = atr_series_all.shift(1).to_numpy()

            # Marubozu detekce na celém datasetu
            bull_all = c_all >= o_all
            open_wick_all = np.where(bull_all, o_all - l_all, h_all - o_all)
            wick_ratio_all = np.divide(open_wick_all, rng_all, out=np.zeros_like(open_wick_all), where=rng_all!=0)

            # Výpočet velikosti těla svíčky
            body_all = np.abs(c_all - o_all)
            body_ratio_all = np.divide(body_all, rng_all, out=np.zeros_like(body_all), where=rng_all!=0)

            is_maru_all = (wick_ratio_all < self.WICK_RATIO_MAX) & (body_ratio_all >= min_body_ratio) & (rng_all > atr10_s1_all) & np.isfinite(atr10_s1_all)

            # Vytvoření kompletního DataFrame
            base_all = pd.DataFrame({
                "Date": pd.to_datetime(raw["dt"].dt.date),
                "Open": o_all, "High": h_all, "Low": l_all, "Close": c_all,
                "Range": rng_all, "Bull": bull_all, "ATR10_s1": atr10_s1_all, "Is_Marubozu": is_maru_all
            }).dropna().reset_index(drop=True)

            # Filtrace podle datumů POUZE pro finální výsledky
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            base = base_all[(base_all["Date"] >= start_dt) & (base_all["Date"] <= end_dt)].copy().reset_index(drop=True)
            results.append(f"📅 Filtrováno na {len(base)} barů pro období {start_date} - {end_date}\n")

            if len(base) == 0:
                raise ValueError("Žádná data v zadaném období!")

            marubozu_count = base['Is_Marubozu'].sum()
            results.append(f"🎯 Nalezeno {marubozu_count} Marubozu signálů ({marubozu_count/len(base)*100:.1f}%)\n")

            # Sekvenční logika na celém datasetu
            results.append(f"🔄 Aplikuji {variant} sekvenční logiku na celém datasetu...\n")
            if variant == "Strict":
                days_data_all = self.build_days_strict(base_all)
            else:
                days_data_all = self.build_days_tolerant(base_all)

            # Filtrace denních dat na požadované období
            days_data = days_data_all[(days_data_all["Date"] >= start_dt) & (days_data_all["Date"] <= end_dt)].copy().reset_index(drop=True)

            # Vytvoření sekvencí z filtrovaných dat
            seqs_data = self.build_sequences(days_data)
            results.append(f"📊 Nalezeno {len(seqs_data)} sekvencí v období\n")

            # Simulace obchodů
            results.append("💹 Simulujem obchody...\n")
            trades_data = self.simulate_trades(days_data, seqs_data, rrr, exit_policy, start_equity, risk_pct, overnight_mode, buy_limit_offset, long_lmt_sl_method, bars_back)

            # Uložení do Excel
            excel_filename = f"TrendTaker_WebGUI_{variant}_{start_date}_{end_date}.xlsx"
            excel_created = self.save_to_excel(trades_data, days_data, seqs_data, params, excel_filename)

            if excel_created and os.path.exists(excel_filename):
                results.append(f"💾 Excel soubor vytvořen: {excel_filename}\n")
                results.append(f"📥 <a href='/download/{excel_filename}' target='_blank'>Stáhnout Excel soubor</a>\n")
            else:
                results.append(f"💾 Výsledky uloženy do CSV souborů (Excel nedostupný)\n")
                csv_base = excel_filename.replace('.xlsx', '')
                results.append(f"📥 <a href='/download/{csv_base}_trades.csv' target='_blank'>Stáhnout obchody (CSV)</a>\n")
                results.append(f"📥 <a href='/download/{csv_base}_days.csv' target='_blank'>Stáhnout denní data (CSV)</a>\n")
                if len(seqs_data) > 0:
                    results.append(f"📥 <a href='/download/{csv_base}_seqs.csv' target='_blank'>Stáhnout sekvence (CSV)</a>\n")

            # Výsledky
            results.append(f"\n{'='*60}\n")
            results.append("🏆 VÝSLEDKY TESTU\n")
            results.append(f"{'='*60}\n")

            if len(trades_data) > 0:
                pnl_values = [t['Net_PnL'] for t in trades_data]
                wins = sum(1 for p in pnl_values if p > 0)
                win_rate = wins / len(trades_data) * 100
                total_pnl = sum(pnl_values)
                final_equity = trades_data[-1]['Equity_After']  # Poslední equity
                roi = ((final_equity - start_equity) / start_equity) * 100

                results.append(f"Období: {start_date} - {end_date}\n")
                results.append(f"Varianta: {variant} / {exit_policy}\n")
                results.append(f"RRR: {rrr}\n\n")
                results.append(f"Počet obchodů: {len(trades_data)}\n")
                results.append(f"Počet sekvencí: {len(seqs_data)}\n")
                results.append(f"Win Rate: {win_rate:.1f}%\n")
                results.append(f"Počáteční kapitál: ${start_equity:,.0f}\n")
                results.append(f"Finální kapitál: ${final_equity:,.0f}\n")
                results.append(f"Čistý zisk: ${total_pnl:,.0f}\n")
                results.append(f"ROI: {roi:,.1f}%\n\n")

                # Top obchody - podrobné informace
                sorted_trades = sorted(trades_data, key=lambda x: x['Net_PnL'], reverse=True)
                results.append("📈 TOP 5 OBCHODŮ (podrobně):\n")
                results.append("=" * 120 + "\n")
                results.append(f"{'#':<2} {'Start':<10} {'End':<10} {'Dir':<5} {'Lots':<6} {'ATR10':<6} "
                             f"{'Entry':<8} {'Exit':<8} {'SL':<8} {'TP':<8} {'Reason':<12} "
                             f"{'Spread':<7} {'Swaps':<7} {'PnL_Bod':<8} {'PnL_USD':<9} {'Net_PnL':<9} {'Equity':<10}\n")
                results.append("-" * 120 + "\n")

                for i, trade in enumerate(sorted_trades[:5], 1):
                    results.append(f"{i:<2} {trade['Start_Date']:<10} {trade['End_Date']:<10} "
                                 f"{trade['Direction']:<5} {trade['Lots']:<6.2f} {trade['ATR10']:<6.1f} "
                                 f"{trade['Entry_Price']:<8.2f} {trade['Exit_Price']:<8.2f} "
                                 f"{trade['Stop_Loss']:<8.2f} {trade['Take_Profit']:<8.2f} "
                                 f"{trade['Exit_Reason']:<12} {trade['Spread_Cost']:<7.0f} "
                                 f"{trade['Swaps_USD']:<7.0f} {trade['PnL_Bod']:<8.1f} "
                                 f"{trade['PnL_USD']:<9.0f} {trade['Net_PnL']:<9.0f} {trade['Equity_After']:<10.0f}\n")

                results.append("=" * 120 + "\n")

                results.append(f"\n📊 Excel soubor obsahuje:\n")
                results.append(f"   • Detailní seznam všech obchodů\n")
                results.append(f"   • Denní analýzu sekvencí\n")
                results.append(f"   • Parametry testu\n")
                results.append(f"   • Soubor: {excel_filename}\n")

                results.append("\n✅ Test dokončen úspěšně!\n")
            else:
                results.append("❌ Žádné obchody nebyly provedeny!\n")

        except Exception as e:
            results.append(f"❌ Chyba: {str(e)}\n")
            import traceback
            results.append(f"Detail: {traceback.format_exc()}\n")

        return ''.join(results), excel_filename

    def build_days_strict(self, df):
        """Strict sekvenční logika - 100% stejná jako v hlavní aplikaci"""
        rows=[]; on=False; dir_bull=None; order=0
        for i, row in df.iterrows():
            day_bull = bool(row["Bull"]); mb = bool(row["Is_Marubozu"]); series_order=0
            if on:
                if (dir_bull and day_bull) or ((not dir_bull) and (not day_bull)):
                    order+=1; series_order=order
                else:
                    on=False; dir_bull=None; order=0
                    if mb:
                        on=True; dir_bull=day_bull; order=1; series_order=1
            else:
                if mb:
                    on=True; dir_bull=day_bull; order=1; series_order=1
            rows.append({
                "Date": row["Date"], "Open": row["Open"], "High": row["High"], "Low": row["Low"], "Close": row["Close"],
                "Color": "Green" if day_bull else "Red",
                "Is_Marubozu": mb, "Series_Order": int(series_order),
                "Direction": "Up" if (dir_bull if on else day_bull) else "Down",
                "ATR10_s1": float(row["ATR10_s1"])
            })
        return pd.DataFrame(rows)

    def build_days_tolerant(self, df):
        """Tolerant sekvenční logika - 100% stejná jako v hlavní aplikaci"""
        rows=[]; on=False; dir_bull=None; order=0; last_dir_idx=None; pending_entry=False
        for i, row in df.iterrows():
            day_bull = bool(row["Bull"]); mb = bool(row["Is_Marubozu"]); series_order=0; tolerated=False

            if pending_entry:
                # Toto je den vstupu do obchodu (den po Marubozu)
                on=True; order=1; series_order=1; last_dir_idx=i; pending_entry=False
            elif on:
                if dir_bull:  # UP
                    if day_bull:
                        order+=1; last_dir_idx=i; series_order=order
                    else:
                        # Pokud je to Marubozu opačného směru, ukončí sekvenci a začne novou
                        if mb:
                            on=False; dir_bull=False; order=0; last_dir_idx=None; pending_entry=True
                        else:
                            last_low = float(df.iloc[last_dir_idx]["Low"]) if last_dir_idx is not None else float(row["Low"])
                            if float(row["Close"]) >= last_low:
                                order+=1; series_order=order; tolerated=True
                            else:
                                on=False; dir_bull=None; order=0; last_dir_idx=None
                else:         # DOWN
                    if not day_bull:
                        order+=1; last_dir_idx=i; series_order=order
                    else:
                        # Pokud je to Marubozu opačného směru, ukončí sekvenci a začne novou
                        if mb:
                            on=False; dir_bull=True; order=0; last_dir_idx=None; pending_entry=True
                        else:
                            last_high = float(df.iloc[last_dir_idx]["High"]) if last_dir_idx is not None else float(row["High"])
                            if float(row["Close"]) <= last_high:
                                order+=1; series_order=order; tolerated=True
                            else:
                                on=False; dir_bull=None; order=0; last_dir_idx=None
            else:
                if mb:
                    dir_bull=day_bull; pending_entry=True  # Čekáme na vstup následující den
            rows.append({
                "Date": row["Date"], "Open": row["Open"], "High": row["High"], "Low": row["Low"], "Close": row["Close"],
                "Color": "Green" if day_bull else "Red",
                "Is_Marubozu": mb, "Series_Order": int(series_order),
                "Direction": "Up" if (dir_bull if on else day_bull) else "Down",
                "ATR10_s1": float(row["ATR10_s1"]),
                "Tolerated_Opposite": tolerated
            })
        return pd.DataFrame(rows)

    def build_sequences(self, days_df):
        """Vytvoří sekvence z denních dat - 100% stejná logika"""
        seqs=[]; i=0; n=len(days_df)
        while i<n:
            if int(days_df.at[i,"Series_Order"])==1:
                dir_here=days_df.at[i,"Direction"]; s=i; j=i
                while j+1<n and int(days_df.at[j+1,"Series_Order"])>=1 and days_df.at[j+1,"Direction"]==dir_here:
                    j+=1
                seqs.append({"Start_Date": pd.to_datetime(days_df.at[s,"Date"]),
                             "End_Date": pd.to_datetime(days_df.at[j,"Date"]),
                             "Direction": dir_here, "Start_Idx": s, "End_Idx": j})
                i=j+1
            else:
                i+=1
        return pd.DataFrame(seqs)

    def simulate_trades(self, days_df, seqs_df, rrr, exit_policy, start_equity, risk_pct, overnight_mode, buy_limit_offset=5, long_lmt_sl_method="Initial", bars_back=3):
        """Simuluje obchody podle sekvencí s podporou LONG-FLAT-LIMIT"""
        if overnight_mode == "LONG-FLAT-LIMIT":
            return self.simulate_trades_long_flat_limit(days_df, seqs_df, rrr, exit_policy, start_equity, risk_pct, buy_limit_offset, long_lmt_sl_method, bars_back)
        else:
            return self.simulate_trades_standard(days_df, seqs_df, rrr, exit_policy, start_equity, risk_pct, overnight_mode)

    def simulate_trades_standard(self, days_df, seqs_df, rrr, exit_policy, start_equity, risk_pct, overnight_mode):
        """Standardní simulace obchodů (původní logika)"""
        trades=[]; equity=start_equity
        O=days_df["Open"].to_numpy(float); H=days_df["High"].to_numpy(float)
        L=days_df["Low"].to_numpy(float); C=days_df["Close"].to_numpy(float)
        D=pd.to_datetime(days_df["Date"]).to_numpy(); ATR=days_df["ATR10_s1"].to_numpy(float)

        exit_policy_code = "cons" if exit_policy == "Conservative" else "opti"
        spread_usd_per_lot = self.SPREAD_BOD * self.USD_PER_BOD_PER_LOT

        # Swap konstanty
        SWAP_LONG_BOD = -0.55237
        SWAP_SHORT_BOD = 0.29425

        for seq_idx, (_, seq) in enumerate(seqs_df.sort_values("Start_Date").iterrows()):
            s=int(seq["Start_Idx"]); e=int(seq["End_Idx"]); is_up=(seq["Direction"]=="Up")
            atr10 = ATR[s]
            sequence_id = f"SEQ_{seq_idx+1:03d}"  # SEQ_001, SEQ_002, atd.
            if not np.isfinite(atr10) or atr10<=0: continue

            # Position sizing
            risk_usd = equity * risk_pct
            risk_per_lot = atr10 * self.USD_PER_BOD_PER_LOT
            if risk_per_lot <= 0: continue
            lots = risk_usd / risk_per_lot
            lots = max(0.01, min(100.0, round(lots, 2)))

            # Vstup na Open svíčky kde sekvence začíná (s)
            entry = O[s]
            stop = entry - atr10 if is_up else entry + atr10
            tp = entry + rrr*atr10 if is_up else entry - rrr*atr10
            spread = spread_usd_per_lot * lots

        # SL Calc - zdroj výpočtu SL
        sl_calc = f"Entry {entry:.2f} {'- ATR10' if is_up else '+ ATR10'} {atr10:.2f}"

        # Simulace obchodu - začíná od s (den vstupu do obchodu)
        carry_idx=[]
        for i in range(s, e+1):
            hi, lo, cl, dt = H[i], L[i], C[i], D[i]
            hit_tp = (hi>=tp) if is_up else (lo<=tp)
            hit_sl = (lo<=stop) if is_up else (hi>=stop)

            if hit_tp and hit_sl:
                exit_px = stop if exit_policy_code=="cons" else tp
                reason = "SL (same-day)" if exit_policy_code=="cons" else "TP (same-day)"
            elif hit_tp:
                exit_px=tp; reason="TP"
            elif hit_sl:
                exit_px=stop; reason="SL"
            else:
                if i==e:
                    exit_px=cl; reason="SEQ_END_EOD_CLOSE"
                else:
                    carry_idx.append(i); continue

            # Výpočet P&L
            pnl_bod = (exit_px - entry) if is_up else (entry - exit_px)
            pnl_usd = pnl_bod * self.USD_PER_BOD_PER_LOT * lots

            # Swapy podle overnight mode
            swaps = 0.0
            if overnight_mode == "STANDARD":
                # STANDARD: všechny pozice držíme přes noc
                for d in carry_idx:
                    bod = self.SWAP_LONG_BOD if is_up else self.SWAP_SHORT_BOD
                    mult = 3.0 if pd.to_datetime(D[d]).weekday() == 2 else 1.0  # Wednesday
                    swaps += bod * self.USD_PER_BOD_PER_LOT * lots * mult
            elif overnight_mode == "LONG-FLAT-LIMIT":
                # LONG-FLAT-LIMIT: Long pozice bez swapu, Short se standardními swapy
                if not is_up:  # Pouze SHORT pozice mají swapy
                    for d in carry_idx:
                        bod = self.SWAP_SHORT_BOD
                        mult = 3.0 if pd.to_datetime(D[d]).weekday() == 2 else 1.0  # Wednesday
                        swaps += bod * self.USD_PER_BOD_PER_LOT * lots * mult
                # Long pozice: swaps = 0.0 (už nastaveno výše)

            net = pnl_usd - spread + swaps
            equity += net

            trades.append({
                "Start_Date": pd.to_datetime(D[s]).strftime("%Y-%m-%d"),
                "End_Date": pd.to_datetime(D[i]).strftime("%Y-%m-%d"),
                "Direction": "LONG" if is_up else "SHORT",
                "Lots": round(lots,2),
                "ATR10": round(atr10,2),
                "Entry_Price": round(entry,2),
                "Exit_Price": round(exit_px,2),
                "Stop_Loss": round(stop,2),
                "Take_Profit": round(tp,2),
                "Exit_Reason": reason,
                "SL_Calc": sl_calc,
                "Spread_Cost": round(spread,2),
                "Swaps_USD": round(swaps,2),
                "PnL_Bod": round(pnl_bod,2),
                "PnL_USD": round(pnl_usd,2),
                "Net_PnL": round(net,2),
                "Equity_After": round(equity,2),
                "Sequence_ID": sequence_id
            })
            break

        return trades

    def simulate_trades_long_flat_limit(self, days_df, seqs_df, rrr, exit_policy, start_equity, risk_pct, buy_limit_offset, long_lmt_sl_method, bars_back):
        """Simulace obchodů s LONG-FLAT-LIMIT logikou"""
        trades=[]; equity=start_equity
        O=days_df["Open"].to_numpy(float); H=days_df["High"].to_numpy(float)
        L=days_df["Low"].to_numpy(float); C=days_df["Close"].to_numpy(float)
        D=pd.to_datetime(days_df["Date"]).to_numpy(); ATR=days_df["ATR10_s1"].to_numpy(float)

        exit_policy_code = "cons" if exit_policy == "Conservative" else "opti"
        spread_usd_per_lot = self.SPREAD_BOD * self.USD_PER_BOD_PER_LOT

        for seq_idx, (_, seq) in enumerate(seqs_df.iterrows()):
            s=int(seq["Start_Idx"]); e=int(seq["End_Idx"])
            is_up=(seq["Direction"]=="Up")
            atr10 = ATR[s]
            sequence_id = f"SEQ_{seq_idx+1:03d}"  # SEQ_001, SEQ_002, atd.
            if not np.isfinite(atr10) or atr10<=0: continue

            # Position sizing (stejný výpočet jako ve standardní metodě)
            risk_usd = equity * risk_pct
            risk_per_lot = atr10 * self.USD_PER_BOD_PER_LOT
            if risk_per_lot <= 0: continue
            lots = risk_usd / risk_per_lot
            lots = max(0.01, min(100.0, round(lots, 2)))

            if lots<=0: continue

            spread = spread_usd_per_lot * lots
            entry = O[s]

            # LONG LMT pozice je vždy jen když je směr sekvence UP (ve směru sekvence)
            # is_up = True znamená UP sekvenci → LONG pozici (ve směru sekvence)
            # is_up = False znamená DOWN sekvenci → SHORT pozici

            if is_up:
                # UP sekvence → LONG pozice (nové SL metody)
                stop = entry - atr10  # Základní SL, bude upraven v simulate_long_flat_limit_position
                tp = entry + (rrr * atr10)
                initial_sl_calc = f"Initial: Entry {entry:.2f} - ATR10 {atr10:.2f} = {stop:.2f}"

                # LONG pozice - LONG-FLAT-LIMIT logika s novými SL metodami
                trades_from_long = self.simulate_long_flat_limit_position(
                    s, e, entry, stop, tp, lots, spread, atr10, rrr, exit_policy_code,
                    buy_limit_offset, long_lmt_sl_method, bars_back, O, H, L, C, D, ATR
                )
                for trade in trades_from_long:
                    equity += trade["Net_PnL"]
                    trade["Equity_After"] = round(equity, 2)
                    trade["Sequence_ID"] = sequence_id
                    trades.append(trade)
            else:
                # DOWN sekvence → SHORT pozice (standardní SL)
                stop = entry + atr10
                tp = entry - (rrr * atr10)
                initial_sl_calc = f"Entry {entry:.2f} + ATR10 {atr10:.2f} = {stop:.2f}"

                # SHORT pozice - standardní logika se swapy
                trade = self.simulate_short_standard_position(
                    s, e, entry, stop, tp, lots, spread, atr10, rrr, exit_policy_code,
                    O, H, L, C, D, ATR
                )
                if trade:
                    trade["SL_Calc"] = initial_sl_calc
                    equity += trade["Net_PnL"]
                    trade["Equity_After"] = round(equity, 2)
                    trade["Sequence_ID"] = sequence_id
                    trades.append(trade)

        return trades

    def simulate_long_flat_limit_position(self, s, e, entry, stop, tp, lots, spread, atr10, rrr, exit_policy_code,
                                        buy_limit_offset, long_lmt_sl_method, bars_back, O, H, L, C, D, ATR):
        """Simuluje LONG pozici s LONG-FLAT-LIMIT logikou - vrací jeden konsolidovaný trade"""

        # První den - vstup na Open
        current_day = s
        current_entry = entry
        position_active = True
        total_pnl_bod = 0
        total_pnl_usd = 0
        total_spread_cost = 0
        segments = []
        final_exit_reason = ""
        final_exit_price = 0
        final_exit_date = ""

        # SL Calc tracking
        sl_calc_history = [f"Initial: Entry {entry:.2f} - ATR10 {atr10:.2f} = {stop:.2f}"]

        while current_day <= e and position_active:
            hi, lo, cl, dt = H[current_day], L[current_day], C[current_day], D[current_day]

            # Kontrola TP/SL během dne
            hit_tp = hi >= tp
            hit_sl = lo <= stop

            if hit_tp and hit_sl:
                exit_px = stop if exit_policy_code == "cons" else tp
                reason = "SL (same-day)" if exit_policy_code == "cons" else "TP (same-day)"
                position_active = False
            elif hit_tp:
                exit_px = tp
                reason = "TP"
                position_active = False
            elif hit_sl:
                exit_px = stop
                reason = "SL"
                position_active = False
            else:
                # Pozice přežila den - uzavřeme na Close
                exit_px = cl
                if current_day == e:
                    reason = "SEQ_END_EOD_CLOSE"
                    position_active = False
                else:
                    reason = "EOD_CLOSE"

            # Výpočet P&L pro tento segment
            segment_pnl_bod = exit_px - current_entry
            segment_pnl_usd = segment_pnl_bod * self.USD_PER_BOD_PER_LOT * lots

            total_pnl_bod += segment_pnl_bod
            total_pnl_usd += segment_pnl_usd
            total_spread_cost += spread  # Spread za každý segment

            segments.append({
                "day": current_day,
                "date": pd.to_datetime(dt).strftime("%Y-%m-%d"),
                "entry": current_entry,
                "exit": exit_px,
                "stop": stop,
                "pnl_bod": segment_pnl_bod,
                "reason": reason,
                "spread": spread,
                "sl_calc": sl_calc_history[-1] if sl_calc_history else f"SL {stop:.2f}"
            })

            final_exit_price = exit_px
            final_exit_date = pd.to_datetime(dt).strftime("%Y-%m-%d")
            final_exit_reason = reason

            if position_active and current_day < e:
                # Pokračujeme další den s Buy Limit
                next_day = current_day + 1
                if next_day <= e:
                    next_open = O[next_day]
                    buy_limit_price = next_open - buy_limit_offset

                    # Kontrola, zda se Buy Limit naplnil
                    next_low = L[next_day]
                    if next_low <= buy_limit_price:
                        # Buy Limit se naplnil
                        current_entry = buy_limit_price
                        current_day = next_day

                        # Aktualizace SL podle zvolené metody
                        old_stop = stop

                        if long_lmt_sl_method == "Initial":
                            # Původní logika - 50% předposlední svíčky
                            if current_day > 0:
                                prev_range = H[current_day-1] - L[current_day-1]
                                new_sl = current_entry - (prev_range * 0.5)
                                # KONZERVATIVNÍ: použijeme nižší SL
                                stop = min(stop, new_sl)

                                # SL Calc update
                                if stop != old_stop:
                                    sl_calc_history.append(f"Day {current_day+1}: Initial - Entry {current_entry:.2f} - (Range {prev_range:.2f} * 0.5) = {new_sl:.2f}, min({old_stop:.2f}, {new_sl:.2f}) = {stop:.2f}")
                                else:
                                    sl_calc_history.append(f"Day {current_day+1}: Initial - SL unchanged {stop:.2f} (min logic)")

                        elif long_lmt_sl_method == "BarsBack_Low":
                            # BarsBack Low - SL na Low X barů zpětně
                            bars_back_idx = max(0, current_day - bars_back)
                            if bars_back_idx < current_day:
                                new_sl = L[bars_back_idx]
                                # KONZERVATIVNÍ: použijeme nižší SL
                                stop = min(stop, new_sl)

                                # SL Calc update
                                if stop != old_stop:
                                    sl_calc_history.append(f"Day {current_day+1}: BarsBack_Low - Low[{bars_back_idx}] = {new_sl:.2f}, min({old_stop:.2f}, {new_sl:.2f}) = {stop:.2f}")
                                else:
                                    sl_calc_history.append(f"Day {current_day+1}: BarsBack_Low - SL unchanged {stop:.2f} (min logic)")

                        elif long_lmt_sl_method == "BarsBack_50pct":
                            # BarsBack 50% - SL na 50% X barů zpětně (H+L)/2
                            bars_back_idx = max(0, current_day - bars_back)
                            if bars_back_idx < current_day:
                                new_sl = (H[bars_back_idx] + L[bars_back_idx]) / 2
                                # KONZERVATIVNÍ: použijeme nižší SL
                                stop = min(stop, new_sl)

                                # SL Calc update
                                if stop != old_stop:
                                    sl_calc_history.append(f"Day {current_day+1}: BarsBack_50pct - (H[{bars_back_idx}] {H[bars_back_idx]:.2f} + L[{bars_back_idx}] {L[bars_back_idx]:.2f})/2 = {new_sl:.2f}, min({old_stop:.2f}, {new_sl:.2f}) = {stop:.2f}")
                                else:
                                    sl_calc_history.append(f"Day {current_day+1}: BarsBack_50pct - SL unchanged {stop:.2f} (min logic)")

                        final_exit_reason += " -> BUY_LIMIT_FILLED"
                    else:
                        # Buy Limit se nenaplnil
                        final_exit_reason += " -> BUY_LIMIT_NOT_FILLED"
                        position_active = False
                else:
                    position_active = False
            else:
                position_active = False

            if not position_active:
                break

        # Vytvoření jednoho konsolidovaného trade záznamu
        net_pnl = total_pnl_usd - total_spread_cost  # Žádné swapy

        trade = {
            "Start_Date": pd.to_datetime(D[s]).strftime("%Y-%m-%d"),
            "End_Date": final_exit_date,
            "Direction": "Long",
            "Lots": round(lots, 2),
            "ATR10": round(atr10, 2),
            "Entry_Price": round(entry, 2),  # Původní entry
            "Exit_Price": round(final_exit_price, 2),
            "Stop_Loss": round(stop, 2),
            "Take_Profit": round(tp, 2),
            "Exit_Reason": final_exit_reason,
            "SL_Calc": " | ".join(sl_calc_history),
            "Spread_Cost": round(total_spread_cost, 2),
            "Swaps_USD": 0.0,  # Žádné swapy
            "PnL_Bod": round(total_pnl_bod, 2),
            "PnL_USD": round(total_pnl_usd, 2),
            "Net_PnL": round(net_pnl, 2),
            "Equity_After": 0,  # Bude nastaveno později
            "Segments_Count": len(segments),
            "Segments_Detail": segments  # Detaily segmentů pro Excel export
        }

        return [trade]  # Vrací list s jedním trade

    def enhance_daily_analysis(self, days_data, params):
        """Rozšíří denní analýzu o LONG LMT SL Method a SL výpočty"""

        # Vytvoření kopie dat
        enhanced_data = days_data.copy()

        # Přidání nových sloupců
        long_lmt_sl_method = params.get('longLmtSlMethod', 'Initial')
        bars_back = int(params.get('barsBack', 3))

        enhanced_data['LONG_LMT_SL_Method'] = long_lmt_sl_method
        enhanced_data['BarsBack'] = bars_back if long_lmt_sl_method != 'Initial' else ''

        # Výpočet SL pro každý den
        sl_values = []

        for i in range(len(enhanced_data)):
            row = enhanced_data.iloc[i]

            # Základní SL (Entry - ATR10 pro LONG pozice)
            direction = row.get('Direction', '')
            if direction == 'Up' or direction == 'UP':
                entry_price = row.get('Open', 0)
                atr10 = row.get('ATR10_s1', 0)
                base_sl = entry_price - atr10

                # Výpočet SL podle metody
                if long_lmt_sl_method == 'Initial':
                    sl_value = f"{base_sl:.2f} (Entry - ATR10)"

                elif long_lmt_sl_method == 'BarsBack_Low':
                    bars_back_idx = max(0, i - bars_back)
                    if bars_back_idx < i:
                        bars_back_low = enhanced_data.iloc[bars_back_idx].get('Low', 0)
                        calculated_sl = min(base_sl, bars_back_low)
                        sl_value = f"{calculated_sl:.2f} (min({base_sl:.2f}, Low[{bars_back_idx}]={bars_back_low:.2f}))"
                    else:
                        sl_value = f"{base_sl:.2f} (Entry - ATR10, no bars back)"

                elif long_lmt_sl_method == 'BarsBack_50pct':
                    bars_back_idx = max(0, i - bars_back)
                    if bars_back_idx < i:
                        bars_back_row = enhanced_data.iloc[bars_back_idx]
                        bars_back_low = bars_back_row.get('Low', 0)
                        bars_back_high = bars_back_row.get('High', 0)
                        bars_back_50pct = (bars_back_high + bars_back_low) / 2
                        calculated_sl = min(base_sl, bars_back_50pct)
                        sl_value = f"{calculated_sl:.2f} (min({base_sl:.2f}, 50%[{bars_back_idx}]={bars_back_50pct:.2f}))"
                    else:
                        sl_value = f"{base_sl:.2f} (Entry - ATR10, no bars back)"

                else:
                    sl_value = f"{base_sl:.2f} (Entry - ATR10)"

            else:
                # Pro DOWN pozice (SHORT)
                entry_price = row.get('Open', 0)
                atr10 = row.get('ATR10_s1', 0)
                base_sl = entry_price + atr10
                sl_value = f"{base_sl:.2f} (Entry + ATR10)"

            sl_values.append(sl_value)

        enhanced_data['Calculated_SL'] = sl_values

        return enhanced_data

    def simulate_short_standard_position(self, s, e, entry, stop, tp, lots, spread, atr10, rrr, exit_policy_code,
                                       O, H, L, C, D, ATR):
        """Simuluje SHORT pozici se standardní logikou (se swapy)"""
        carry_idx = []

        # SL Calc pro SHORT pozice
        initial_sl_calc = f"Entry {entry:.2f} + ATR10 {atr10:.2f}"

        for i in range(s, e+1):
            hi, lo, cl, dt = H[i], L[i], C[i], D[i]
            hit_tp = lo <= tp
            hit_sl = hi >= stop

            if hit_tp and hit_sl:
                exit_px = stop if exit_policy_code == "cons" else tp
                reason = "SL (same-day)" if exit_policy_code == "cons" else "TP (same-day)"
            elif hit_tp:
                exit_px = tp
                reason = "TP"
            elif hit_sl:
                exit_px = stop
                reason = "SL"
            else:
                if i == e:
                    exit_px = cl
                    reason = "SEQ_END_EOD_CLOSE"
                else:
                    carry_idx.append(i)
                    continue

            # Výpočet P&L
            pnl_bod = entry - exit_px  # SHORT pozice
            pnl_usd = pnl_bod * self.USD_PER_BOD_PER_LOT * lots

            # Swapy pro SHORT pozice
            swaps = 0.0
            for d in carry_idx:
                bod = self.SWAP_SHORT_BOD
                mult = 3.0 if pd.to_datetime(D[d]).weekday() == 2 else 1.0  # Wednesday
                swaps += bod * self.USD_PER_BOD_PER_LOT * lots * mult

            net = pnl_usd - spread + swaps

            return {
                "Start_Date": pd.to_datetime(D[s]).strftime("%Y-%m-%d"),
                "End_Date": pd.to_datetime(dt).strftime("%Y-%m-%d"),
                "Direction": "Short",
                "Lots": round(lots, 2),
                "ATR10": round(atr10, 2),
                "Entry_Price": round(entry, 2),
                "Exit_Price": round(exit_px, 2),
                "Stop_Loss": round(stop, 2),
                "Take_Profit": round(tp, 2),
                "Exit_Reason": reason,
                "SL_Calc": initial_sl_calc,
                "Spread_Cost": round(spread, 2),
                "Swaps_USD": round(swaps, 2),
                "PnL_Bod": round(pnl_bod, 2),
                "PnL_USD": round(pnl_usd, 2),
                "Net_PnL": round(net, 2),
                "Equity_After": 0  # Bude nastaveno později
            }

        return None

    def save_to_excel(self, trades_data, days_data, seqs_data, params, filename):
        """Uloží výsledky do Excel souboru"""
        excel_created = False

        # Pokus o instalaci openpyxl pokud není dostupné
        try:
            import openpyxl
        except ImportError:
            try:
                import subprocess
                import sys
                print("Instaluji openpyxl...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "openpyxl"])
                import openpyxl
            except:
                pass

        # Pokus o vytvoření Excel souboru
        try:
            import openpyxl
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # Sheet 1: Obchody
                if trades_data:
                    # Vytvoříme kopii bez Segments_Detail pro hlavní sheet
                    trades_clean = []
                    segments_detail = []

                    for trade in trades_data:
                        # Hlavní obchod bez segments detail (zachováváme SL_Calc)
                        trade_clean = {k: v for k, v in trade.items() if k != 'Segments_Detail'}
                        trades_clean.append(trade_clean)

                        # Extrakce segmentů pro detailní sheet
                        if 'Segments_Detail' in trade and trade['Segments_Detail']:
                            sequence_id = trade.get('Sequence_ID', 'N/A')
                            for i, segment in enumerate(trade['Segments_Detail'], 1):
                                segments_detail.append({
                                    'Sequence_ID': sequence_id,
                                    'Segment_No': i,
                                    'Date': segment.get('date', ''),
                                    'Entry_Price': round(segment.get('entry', 0), 2),
                                    'Exit_Price': round(segment.get('exit', 0), 2),
                                    'Stop_Loss': round(segment.get('stop', 0), 2),
                                    'PnL_Bod': round(segment.get('pnl_bod', 0), 2),
                                    'Exit_Reason': segment.get('reason', ''),
                                    'SL_Calc': segment.get('sl_calc', ''),
                                    'Spread_Cost': round(segment.get('spread', 0), 2)
                                })

                    trades_df = pd.DataFrame(trades_clean)
                    trades_df.to_excel(writer, sheet_name='Obchody', index=False)

                    # Sheet pro detailní segmenty (pouze pokud existují)
                    if segments_detail:
                        segments_df = pd.DataFrame(segments_detail)
                        segments_df.to_excel(writer, sheet_name='Detailní_Segmenty', index=False)

                # Sheet 2: Denní data s rozšířenými informacemi
                days_data_enhanced = self.enhance_daily_analysis(days_data, params)
                days_data_enhanced.to_excel(writer, sheet_name='Denní_Analýza', index=False)

                # Sheet 3: Sekvence
                if len(seqs_data) > 0:
                    seqs_data.to_excel(writer, sheet_name='Sekvence', index=False)

                # Sheet 4: Parametry
                params_df = pd.DataFrame([
                    ["Datový soubor", params.get('dataFile', '')],
                    ["Datum od", params.get('startDate', '')],
                    ["Datum do", params.get('endDate', '')],
                    ["Varianta", params.get('variant', '')],
                    ["RRR", params.get('rrr', '')],
                    ["Exit Policy", params.get('exitPolicy', '')],
                    ["Overnight Mode", params.get('overnightMode', '')],
                    ["Počáteční kapitál", params.get('startEquity', '')],
                    ["Risk %", params.get('riskPct', '')],
                    ["Min. velikost těla svíčky (%)", params.get('minBodyRatio', '')],
                    ["Buy Limit offset (body)", params.get('buyLimitOffset', '')],
                    ["LONG LMT SL Method", params.get('longLmtSlMethod', 'Initial')],
                    ["BarsBack", params.get('barsBack', '3')],
                    ["Počet obchodů", len(trades_data)],
                    ["Počet sekvencí", len(seqs_data)],
                    ["Generováno", pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")]
                ], columns=['Parametr', 'Hodnota'])
                params_df.to_excel(writer, sheet_name='Parametry', index=False)

            excel_created = True
            print(f"✅ Excel soubor vytvořen: {filename}")

        except Exception as e:
            print(f"❌ Chyba při vytváření Excel: {e}")
            # Fallback na CSV
            try:
                csv_base = filename.replace('.xlsx', '')
                if trades_data:
                    trades_df = pd.DataFrame(trades_data)
                    trades_csv = f"{csv_base}_trades.csv"
                    trades_df.to_csv(trades_csv, index=False)
                    print(f"✅ CSV obchody: {trades_csv}")

                days_csv = f"{csv_base}_days.csv"
                days_data.to_csv(days_csv, index=False)
                print(f"✅ CSV denní data: {days_csv}")

                if len(seqs_data) > 0:
                    seqs_csv = f"{csv_base}_seqs.csv"
                    seqs_data.to_csv(seqs_csv, index=False)
                    print(f"✅ CSV sekvence: {seqs_csv}")

                # Parametry jako CSV
                params_df = pd.DataFrame([
                    ["Datový soubor", params.get('dataFile', '')],
                    ["Datum od", params.get('startDate', '')],
                    ["Datum do", params.get('endDate', '')],
                    ["Varianta", params.get('variant', '')],
                    ["RRR", params.get('rrr', '')],
                    ["Exit Policy", params.get('exitPolicy', '')],
                    ["Overnight Mode", params.get('overnightMode', '')],
                    ["Počáteční kapitál", params.get('startEquity', '')],
                    ["Risk %", params.get('riskPct', '')],
                    ["Min. velikost těla svíčky (%)", params.get('minBodyRatio', '')],
                    ["Buy Limit offset (body)", params.get('buyLimitOffset', '')],
                    ["LONG LMT SL Method", params.get('longLmtSlMethod', 'Initial')],
                    ["BarsBack", params.get('barsBack', '3')],
                    ["Počet obchodů", len(trades_data)],
                    ["Počet sekvencí", len(seqs_data)],
                    ["Generováno", pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")]
                ], columns=['Parametr', 'Hodnota'])
                params_csv = f"{csv_base}_params.csv"
                params_df.to_csv(params_csv, index=False)
                print(f"✅ CSV parametry: {params_csv}")

            except Exception as csv_error:
                print(f"❌ Chyba i při CSV: {csv_error}")

        return excel_created

    def run_grid_test(self, params):
        """Spustí kompletní GRID TEST se všemi kombinacemi parametrů"""
        print("🔥 Spouštím GRID TEST...")

        # RRR grid: 5.0..12.0 step 0.5
        RRR_values = [round(x, 1) for x in np.arange(5.0, 12.0 + 0.001, 0.5)]

        # Načtení dat z parametrů
        data_file = params.get('dataFile')
        if not data_file:
            raise ValueError('Datový soubor není specifikován')

        data_file_path = get_data_file_path(data_file)
        is_valid, error_msg = validate_d1_data_file(data_file_path)
        if not is_valid:
            raise ValueError(f'Neplatný D1 datový soubor: {error_msg}')

        print(f"📊 Načítám D1 data z: {data_file_path}")
        df = pd.read_csv(data_file_path, encoding='utf-8')

        # Standardizace názvů sloupců
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower().strip()
            if col_lower in ['datum', 'date']:
                column_mapping[col] = 'Datum'
            elif col_lower in ['čas', 'cas', 'time']:
                column_mapping[col] = 'Čas'

        df = df.rename(columns=column_mapping)

        # Parsování datumu s podporou různých formátů
        def parse_datetime_row(row):
            try:
                datum_str = str(row['Datum']).strip()
                cas_str = str(row.get('Čas', '00:00')).strip()

                # Normalizace datumu
                if '.' in datum_str:
                    datum_str = datum_str.replace('.', '-')

                # Normalizace času
                if ':' in cas_str and len(cas_str.split(':')) == 3:
                    cas_str = cas_str[:5]

                return pd.to_datetime(f"{datum_str} {cas_str}", errors='coerce')
            except:
                return pd.NaT

        df['Date'] = df.apply(parse_datetime_row, axis=1)
        df = df.dropna(subset=['Date']).sort_values('Date').reset_index(drop=True)

        # Parametry z požadavku
        start_equity = float(params.get('startEquity', 10000))
        risk_pct = float(params.get('riskPct', 2.0))
        min_body_ratio = float(params.get('minBodyRatio', 60)) / 100.0  # Převod z % na desetinné číslo
        buy_limit_offset = float(params.get('buyLimitOffset', 5))  # Body pro Buy Limit offset
        long_lmt_sl_method = params.get('longLmtSlMethod', 'Initial')  # Metoda SL pro LONG LMT
        bars_back = int(params.get('barsBack', 3))  # Počet barů zpětně
        data_range = params.get('dataRange', 'CUSTOM')

        # Určení rozsahu dat
        if data_range == 'FULL':
            # Full Dataset - použij celý rozsah dat
            start_date = df['Date'].min()
            end_date = df['Date'].max()
            print(f"📊 Full Dataset: {start_date.strftime('%Y-%m-%d')} až {end_date.strftime('%Y-%m-%d')}")
        else:
            # Custom Data - použij zadané datum
            start_date = pd.to_datetime(params.get('startDate', '2020-01-01'))
            end_date = pd.to_datetime(params.get('endDate', '2025-09-05'))
            print(f"📊 Custom Data: {start_date.strftime('%Y-%m-%d')} až {end_date.strftime('%Y-%m-%d')}")

        # Filtrace dat na požadované období
        df_filtered = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)].copy().reset_index(drop=True)
        print(f"📊 Filtrovaných záznamů: {len(df_filtered)} z {len(df)}")

        if len(df_filtered) == 0:
            raise ValueError("Žádná data v zadaném období!")

        # Příprava dat pro analýzu (použij filtrovaná data)
        O = df_filtered['Otevírací'].values
        H = df_filtered['Nejvyšší'].values
        L = df_filtered['Nejnižší'].values
        C = df_filtered['Uzavírací'].values
        D = df_filtered['Date'].values

        # ATR10 shift1
        ranges = H - L
        prev_close = np.roll(C, 1)
        prev_close[0] = C[0]
        tr = np.maximum.reduce([ranges, np.abs(H - prev_close), np.abs(L - prev_close)])
        atr10_s1 = pd.Series(tr).rolling(10).mean().shift(1).values

        # Marubozu detection
        bull = C >= O
        open_wick = np.where(bull, O - L, H - O)
        wick_ratio = open_wick / np.where(ranges == 0, np.nan, ranges)

        # Výpočet velikosti těla svíčky
        body = np.abs(C - O)
        body_ratio = body / np.where(ranges == 0, np.nan, ranges)

        is_marubozu = (wick_ratio < self.WICK_RATIO_MAX) & (body_ratio >= min_body_ratio) & (ranges > atr10_s1)

        # Build days tables
        base_df = pd.DataFrame({
            'Date': D, 'Open': O, 'High': H, 'Low': L, 'Close': C,
            'Bull': bull, 'Is_Marubozu': is_marubozu, 'ATR10_s1': atr10_s1
        })

        days_strict = self.build_days_strict(base_df)
        days_tolerant = self.build_days_tolerant(base_df)

        # Build sequences
        seqs_strict = self.build_sequences(days_strict)
        seqs_tolerant = self.build_sequences(days_tolerant)

        # Spuštění všech kombinací
        all_results = []
        total_combinations = len(RRR_values) * 2 * 2 * 2  # RRR * variants * exit_policies * overnight_modes
        current = 0

        for rrr in RRR_values:
            for variant, (days_df, seqs_df) in [("Strict", (days_strict, seqs_strict)), ("Tolerant", (days_tolerant, seqs_tolerant))]:
                for exit_policy in ["Conservative", "Optimistic"]:
                    for overnight_mode in ["STANDARD", "LONG-FLAT-LIMIT"]:
                        current += 1
                        print(f"📊 Zpracovávám kombinaci {current}/{total_combinations}: {variant}, RRR={rrr}, {exit_policy}, {overnight_mode}")

                        # Simulace
                        trades = self.simulate_trades(
                            days_df, seqs_df, rrr, exit_policy,
                            start_equity, risk_pct, overnight_mode, buy_limit_offset, long_lmt_sl_method, bars_back
                        )

                        if len(trades) > 0:
                            # Výpočet metrik
                            pnl_values = [t['Net_PnL'] for t in trades]
                            equity_curve = np.cumsum([start_equity] + pnl_values)

                            win_trades = [pnl for pnl in pnl_values if pnl > 0]
                            loss_trades = [pnl for pnl in pnl_values if pnl < 0]

                            win_rate = len(win_trades) / len(pnl_values) * 100 if pnl_values else 0
                            gross_profit = sum(win_trades) if win_trades else 0
                            gross_loss = abs(sum(loss_trades)) if loss_trades else 0
                            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

                            # Max drawdown
                            running_max = np.maximum.accumulate(equity_curve)
                            drawdown = running_max - equity_curve
                            max_drawdown = np.max(drawdown)

                            result = {
                                'Variant': variant,
                                'ExitPolicy': exit_policy,
                                'OvernightMode': overnight_mode,
                                'RRR': rrr,
                                'Trades': len(trades),
                                'WinRate%': round(win_rate, 2),
                                'ProfitFactor': round(profit_factor, 2) if profit_factor != float('inf') else 'inf',
                                'NetProfit_USD': round(sum(pnl_values), 2),
                                'MaxDrawdown_USD': round(max_drawdown, 2),
                                'GrossProfit_USD': round(gross_profit, 2),
                                'GrossLoss_USD': round(gross_loss, 2),
                                'AvgLots': round(np.mean([t['Lots'] for t in trades]), 2),
                                'AvgATR10': round(np.mean([t['ATR10'] for t in trades]), 2),
                                'Swaps_USD': round(sum([t['Swaps_USD'] for t in trades]), 2),
                                'SpreadCost_USD': round(sum([t['Spread_Cost'] for t in trades]), 2),
                                'Equity_End_USD': round(equity_curve[-1], 2)
                            }
                            all_results.append(result)

        # Vytvoření Excel souboru s výsledky
        timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        excel_file = f"MegaTest_Results_{timestamp}.xlsx"

        try:
            import openpyxl
            from openpyxl import Workbook

            wb = Workbook()

            # Sheet 1: Všechny výsledky
            ws1 = wb.active
            ws1.title = "All_Results"

            if all_results:
                # Header
                headers = list(all_results[0].keys())
                for col, header in enumerate(headers, 1):
                    ws1.cell(row=1, column=col, value=header)

                # Data
                for row, result in enumerate(all_results, 2):
                    for col, header in enumerate(headers, 1):
                        ws1.cell(row=row, column=col, value=result[header])

            # Sheet 2: Top 10 podle Net Profit
            ws2 = wb.create_sheet("Top_10_NetProfit")
            top_10_profit = sorted(all_results, key=lambda x: x['NetProfit_USD'], reverse=True)[:10]

            if top_10_profit:
                headers = list(top_10_profit[0].keys())
                for col, header in enumerate(headers, 1):
                    ws2.cell(row=1, column=col, value=header)

                for row, result in enumerate(top_10_profit, 2):
                    for col, header in enumerate(headers, 1):
                        ws2.cell(row=row, column=col, value=result[header])

            # Sheet 3: Top 10 podle Profit Factor
            ws3 = wb.create_sheet("Top_10_ProfitFactor")
            top_10_pf = sorted([r for r in all_results if r['ProfitFactor'] != 'inf'],
                              key=lambda x: x['ProfitFactor'], reverse=True)[:10]

            if top_10_pf:
                headers = list(top_10_pf[0].keys())
                for col, header in enumerate(headers, 1):
                    ws3.cell(row=1, column=col, value=header)

                for row, result in enumerate(top_10_pf, 2):
                    for col, header in enumerate(headers, 1):
                        ws3.cell(row=row, column=col, value=result[header])

            wb.save(excel_file)
            print(f"✅ Excel soubor vytvořen: {excel_file}")

        except ImportError:
            # Fallback na CSV
            excel_file = f"MegaTest_Results_{timestamp}.csv"
            results_df = pd.DataFrame(all_results)
            results_df.to_csv(excel_file, index=False)
            print(f"✅ CSV soubor vytvořen: {excel_file}")

        # Vytvoření souhrnu výsledků
        results_summary = f"""
🔥 MEGA TEST DOKONČEN!
================================================================================

📊 CELKOVÉ STATISTIKY:
• Testováno kombinací: {len(all_results)}
• RRR rozsah: {min(RRR_values)} - {max(RRR_values)}
• Rozsah dat: {data_range} ({start_date.strftime('%Y-%m-%d')} až {end_date.strftime('%Y-%m-%d')})
• Záznamů: {len(df_filtered)}
• Varianty: Strict, Tolerant
• Exit policies: Conservative, Optimistic
• Overnight modes: STANDARD, LONG-FLAT-LIMIT

🏆 TOP 3 PODLE NET PROFIT:
"""

        if all_results:
            top_3_profit = sorted(all_results, key=lambda x: x['NetProfit_USD'], reverse=True)[:3]
            for i, result in enumerate(top_3_profit, 1):
                results_summary += f"""
{i}. {result['Variant']} | {result['ExitPolicy']} | {result['OvernightMode']} | RRR={result['RRR']}
   Net Profit: ${result['NetProfit_USD']:,.2f} | Win Rate: {result['WinRate%']}% | PF: {result['ProfitFactor']} | Trades: {result['Trades']}
"""

            results_summary += f"""

💰 NEJLEPŠÍ PROFIT FACTOR:
"""
            best_pf = max([r for r in all_results if r['ProfitFactor'] != 'inf'],
                         key=lambda x: x['ProfitFactor'], default=None)
            if best_pf:
                results_summary += f"""
{best_pf['Variant']} | {best_pf['ExitPolicy']} | {best_pf['OvernightMode']} | RRR={best_pf['RRR']}
Net Profit: ${best_pf['NetProfit_USD']:,.2f} | Win Rate: {best_pf['WinRate%']}% | PF: {best_pf['ProfitFactor']} | Trades: {best_pf['Trades']}
"""

        results_summary += f"""

📁 SOUBOR: {excel_file}
📈 Kompletní výsledky jsou uloženy v Excel souboru s více sheety.

✅ MEGA TEST ÚSPĚŠNĚ DOKONČEN!
"""

        return results_summary, excel_file

    def generate_heatmap(self, excel_file):
        """Generuje HeatMap z výsledků GRID testu"""
        # Kontrola dostupnosti matplotlib a seaborn
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            matplotlib_available = True
        except ImportError:
            print("⚠️  Matplotlib/Seaborn není dostupný - HeatMap nebude vygenerován")
            matplotlib_available = False

        if not matplotlib_available:
            return

        try:

            # Načtení dat z Excel
            df = pd.read_excel(excel_file, sheet_name='All_Results')

            # Pivot tabulka pro HeatMap (RRR vs Variant, hodnota = NetProfit)
            pivot_data = df.pivot_table(
                values='NetProfit_USD',
                index='RRR',
                columns=['Variant', 'ExitPolicy', 'OvernightMode'],
                aggfunc='mean'
            )

            # Vytvoření HeatMap
            plt.figure(figsize=(16, 10))
            sns.heatmap(pivot_data, annot=True, fmt='.0f', cmap='RdYlGn', center=0,
                       cbar_kws={'label': 'Net Profit (USD)'})

            plt.title('GRID TEST - HeatMap Net Profit podle parametrů', fontsize=16, fontweight='bold')
            plt.xlabel('Parametry (Variant | ExitPolicy | OvernightMode)', fontsize=12)
            plt.ylabel('RRR (Risk/Reward Ratio)', fontsize=12)
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # Uložení
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            heatmap_file = f"GridTest_HeatMap_{timestamp}.png"
            plt.savefig(heatmap_file, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"📊 HeatMap uložena: {heatmap_file}")
            return heatmap_file

        except ImportError:
            # Fallback - textová HeatMap
            df = pd.read_excel(excel_file, sheet_name='All_Results')

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            heatmap_file = f"GridTest_HeatMap_{timestamp}.txt"

            with open(heatmap_file, 'w', encoding='utf-8') as f:
                f.write("🔥 GRID TEST - HEATMAP (TEXT FORMAT)\n")
                f.write("="*80 + "\n\n")

                # Seskupení podle RRR
                for rrr in sorted(df['RRR'].unique()):
                    f.write(f"RRR = {rrr}\n")
                    f.write("-" * 40 + "\n")

                    rrr_data = df[df['RRR'] == rrr].sort_values('NetProfit_USD', ascending=False)
                    for _, row in rrr_data.iterrows():
                        f.write(f"{row['Variant']:8} | {row['ExitPolicy']:12} | {row['OvernightMode']:15} | ${row['NetProfit_USD']:8.0f}\n")
                    f.write("\n")

            print(f"📊 Text HeatMap uložena: {heatmap_file}")
            return heatmap_file

        except Exception as e:
            raise Exception(f"Chyba při generování HeatMap: {str(e)}")

def install_openpyxl():
    """Pokus o instalaci openpyxl pokud není dostupné"""
    try:
        import openpyxl
        print("✅ openpyxl je dostupné")
        return True
    except ImportError:
        try:
            import subprocess
            import sys
            print("📦 Instaluji openpyxl pro Excel export...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "openpyxl"],
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            import openpyxl
            print("✅ openpyxl úspěšně nainstalováno")
            return True
        except Exception as e:
            print(f"❌ Nepodařilo se nainstalovat openpyxl: {e}")
            print("📝 Excel export nebude dostupný, použije se CSV")
            return False

def start_web_server():
    PORT = 8081

    print("="*80)
    print("🌐 TREND TAKER TEST 1.0 - WEB GUI")
    print("="*80)

    # Pokus o instalaci openpyxl
    install_openpyxl()

    print(f"Spouštím webový server na portu {PORT}...")
    print(f"Otevřete prohlížeč na: http://localhost:{PORT}")
    print("Pro ukončení stiskněte Ctrl+C")
    print("="*80)
    
    try:
        with socketserver.TCPServer(("", PORT), TrendTakerWebHandler) as httpd:
            # Otevření prohlížeče
            threading.Timer(1.0, lambda: webbrowser.open(f'http://localhost:{PORT}')).start()
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n\n🛑 Server ukončen uživatelem.")
    except Exception as e:
        print(f"\n❌ Chyba serveru: {e}")

if __name__ == "__main__":
    start_web_server()
