#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test konzervativní SL logiky
<PERSON>, že nová logika zabrání předčasnému SL hit
"""

import pandas as pd
import numpy as np

def test_conservative_sl():
    """Test konzervativní SL logiky"""
    
    print("🧪 TEST KONZERVATIVNÍ SL LOGIKY")
    print("=" * 80)
    
    # Stejná data jako <PERSON>, ale s novou logikou
    test_data = [
        {'Date': '2025-08-23', 'Open': 2556.58, 'High': 2569.35, 'Low': 2547.21, 'Close': 2567.37, 'Range': 22.14},
        {'Date': '2025-08-24', 'Open': 2568.40, 'High': 2583.77, 'Low': 2555.90, 'Close': 2573.46, 'Range': 27.87},
        {'Date': '2025-08-25', 'Open': 2578.14, 'High': 2603.45, 'Low': 2565.57, 'Close': 2591.15, 'Range': 37.88},
        {'Date': '2025-08-26', 'Open': 2593.98, 'High': 2618.17, 'Low': 2568.00, 'Close': 2580.00, 'Range': 50.17},
        {'Date': '2025-08-27', 'Open': 2608.61, 'High': 2629.43, 'Low': 2599.12, 'Close': 2625.16, 'Range': 30.31}
    ]
    
    df = pd.DataFrame(test_data)
    df['Date'] = pd.to_datetime(df['Date'])
    
    # Parametry
    entry = 2556.58
    initial_stop = 2528.47
    tp = 2725.26
    buy_limit_offset = 5
    
    O = df['Open'].values
    H = df['High'].values
    L = df['Low'].values
    C = df['Close'].values
    
    print("📊 TESTOVÁNÍ KONZERVATIVNÍ SL LOGIKY:")
    print("=" * 80)
    
    # Simulace s KONZERVATIVNÍ logikou
    current_day = 0
    current_entry = entry
    current_stop = initial_stop
    position_active = True
    segments = []
    
    while current_day < len(df) and position_active:
        hi, lo, cl = H[current_day], L[current_day], C[current_day]
        date_str = df.iloc[current_day]['Date'].strftime('%Y-%m-%d')
        
        print(f"📅 DEN {current_day+1} ({date_str}):")
        print(f"   Current Entry: {current_entry:.2f}")
        print(f"   Current SL: {current_stop:.2f}")
        print(f"   OHLC: {O[current_day]:.2f}/{hi:.2f}/{lo:.2f}/{cl:.2f}")
        
        # Kontrola TP/SL během dne
        hit_tp = hi >= tp
        hit_sl = lo <= current_stop
        
        print(f"   TP check: {hi:.2f} >= {tp:.2f} = {hit_tp}")
        print(f"   SL check: {lo:.2f} <= {current_stop:.2f} = {hit_sl}")
        
        if hit_tp and hit_sl:
            exit_px = tp
            reason = "TP (same-day)"
            position_active = False
            print(f"   ✅ TP a SL hit - exit na TP: {exit_px:.2f}")
        elif hit_tp:
            exit_px = tp
            reason = "TP"
            position_active = False
            print(f"   ✅ TP hit: {exit_px:.2f}")
        elif hit_sl:
            exit_px = current_stop
            reason = "SL"
            position_active = False
            print(f"   ❌ SL HIT! Exit na SL: {exit_px:.2f}")
        else:
            exit_px = cl
            if current_day == len(df) - 1:
                reason = "SEQ_END"
                position_active = False
                print(f"   📅 Konec sekvence - exit na Close: {exit_px:.2f}")
            else:
                reason = "EOD_CLOSE"
                print(f"   🌅 EOD Close: {exit_px:.2f}")
        
        segment_pnl_bod = exit_px - current_entry
        print(f"   💰 Segment P&L: {segment_pnl_bod:.2f} bodů")
        
        segments.append({
            "day": current_day + 1,
            "date": date_str,
            "entry": current_entry,
            "exit": exit_px,
            "stop": current_stop,
            "pnl_bod": segment_pnl_bod,
            "reason": reason
        })
        
        if position_active and current_day < len(df) - 1:
            next_day = current_day + 1
            if next_day < len(df):
                next_open = O[next_day]
                buy_limit_price = next_open - buy_limit_offset
                next_low = L[next_day]
                next_date = df.iloc[next_day]['Date'].strftime('%Y-%m-%d')
                
                print(f"   📋 Další den ({next_date}) Buy Limit setup:")
                print(f"      Next Open: {next_open:.2f}")
                print(f"      Buy Limit: {buy_limit_price:.2f}")
                print(f"      Next Low: {next_low:.2f}")
                
                if next_low <= buy_limit_price:
                    current_entry = buy_limit_price
                    current_day = next_day
                    
                    # KONZERVATIVNÍ SL LOGIKA: min(starý_SL, nový_SL)
                    if current_day > 0:
                        prev_range = H[current_day-1] - L[current_day-1]
                        new_sl = current_entry - (prev_range * 0.5)
                        old_stop = current_stop
                        current_stop = min(current_stop, new_sl)  # KONZERVATIVNÍ!
                        
                        print(f"      🔄 KONZERVATIVNÍ SL LOGIKA:")
                        print(f"         Předposlední range: {prev_range:.2f}")
                        print(f"         Nový SL výpočet: {new_sl:.2f}")
                        print(f"         Starý SL: {old_stop:.2f}")
                        print(f"         Finální SL: min({old_stop:.2f}, {new_sl:.2f}) = {current_stop:.2f}")
                        
                        if current_stop == old_stop:
                            print(f"         ✅ SL zůstal stejný")
                        elif current_stop < old_stop:
                            print(f"         ⬇️  SL se snížil o {old_stop - current_stop:.2f} bodů (bezpečnější)")
                        else:
                            print(f"         ⬆️  SL se zvýšil o {current_stop - old_stop:.2f} bodů")
                    
                    print(f"      ✅ Buy Limit naplněn na {buy_limit_price:.2f}")
                else:
                    position_active = False
                    print(f"      ❌ Buy Limit nenaplněn")
            else:
                position_active = False
        else:
            position_active = False
        
        print()
        
        if not position_active:
            break
    
    # Analýza výsledků
    print("🔍 ANALÝZA KONZERVATIVNÍ LOGIKY:")
    print("=" * 80)
    
    print("📋 DETAILNÍ SEGMENTY:")
    total_pnl = 0
    sl_hit_found = False
    
    for i, seg in enumerate(segments, 1):
        total_pnl += seg['pnl_bod']
        print(f"Segment {i}: {seg['date']}")
        print(f"  Entry: {seg['entry']:.2f}, Exit: {seg['exit']:.2f}")
        print(f"  SL used: {seg['stop']:.2f}")
        print(f"  P&L: {seg['pnl_bod']:.2f} bodů")
        print(f"  Reason: {seg['reason']}")
        
        if seg['reason'] == 'SL':
            sl_hit_found = True
            print(f"  🚨 SL HIT!")
        print()
    
    print(f"📊 CELKOVÉ VÝSLEDKY:")
    print(f"Celkem segmentů: {len(segments)}")
    print(f"Celkový P&L: {total_pnl:.2f} bodů")
    print(f"Poslední exit: {segments[-1]['date']} na {segments[-1]['reason']}")
    
    # Porovnání s původní logikou
    print(f"\n📈 POROVNÁNÍ LOGIK:")
    print("=" * 80)
    print("🔴 PŮVODNÍ LOGIKA (max):")
    print("   SL: 2528.47 → 2552.33 → 2559.20 → 2570.04")
    print("   Výsledek: SL hit na 26.08 (Low 2568.00 <= SL 2570.04)")
    print("   P&L: ~19.92 bodů (ukončeno předčasně)")
    print()
    print("🟢 KONZERVATIVNÍ LOGIKA (min):")
    sl_values = [seg['stop'] for seg in segments]
    print(f"   SL: {' → '.join([f'{sl:.2f}' for sl in sl_values])}")
    
    if sl_hit_found:
        print("   Výsledek: Stále SL hit")
        print(f"   P&L: {total_pnl:.2f} bodů")
    else:
        print("   Výsledek: Žádný SL hit!")
        print(f"   P&L: {total_pnl:.2f} bodů (kompletní sekvence)")
        print("   ✅ KONZERVATIVNÍ LOGIKA ZABRÁNILA PŘEDČASNÉMU EXIT!")
    
    print(f"\n🎯 ZÁVĚR:")
    print("=" * 80)
    if not sl_hit_found:
        print("✅ ÚSPĚCH: Konzervativní SL logika zabránila předčasnému SL hit")
        print("📈 Pozice pokračovala až do konce sekvence")
        print(f"💰 Lepší P&L: {total_pnl:.2f} bodů vs ~19.92 bodů")
        print("🔧 Doporučení: Použít konzervativní logiku pro LONG-FLAT-LIMIT")
    else:
        print("⚠️  SL hit se stále vyskytl i s konzervativní logikou")
        print("🔧 Možná potřeba další úpravy algoritmu")
    
    print(f"\n📊 EXCEL STRUKTURA S KONZERVATIVNÍ LOGIKOU:")
    print("=" * 80)
    print("📋 Sheet 'Obchody':")
    print(f"   Segments_Count: {len(segments)}")
    print(f"   Net_PnL: {total_pnl:.2f} bodů")
    print(f"   Exit_Reason: {segments[-1]['reason']}")
    print()
    print("📋 Sheet 'Detailní_Segmenty':")
    for i, seg in enumerate(segments, 1):
        print(f"   {i}. {seg['date']} | {seg['entry']:.2f} → {seg['exit']:.2f} | {seg['pnl_bod']:+.2f} | {seg['reason']}")

if __name__ == "__main__":
    test_conservative_sl()
