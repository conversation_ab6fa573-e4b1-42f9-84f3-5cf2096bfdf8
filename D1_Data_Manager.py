#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
D1 Data Manager - Utility pro správu D1 datových souborů
Automatická detekce a validace D1 souborů v adresáři C:\\Temp\\Trading\\DATA ICM
"""

import os
import glob
import pandas as pd
from datetime import datetime
import argparse

# Konstanty
DATA_DIRECTORY = r"C:\Temp\Trading\DATA ICM"
SUPPORTED_TIMEFRAMES = ['D1']

def is_d1_data_file(filename):
    """Detekuje zda je soubor D1 data podle názvu"""
    filename_upper = filename.upper()
    if not filename_upper.endswith('.CSV'):
        return False
    return ('_D1' in filename_upper or 
            'D1_' in filename_upper or 
            '_D1.' in filename_upper or
            'DAILY' in filename_upper)

def extract_symbol_from_filename(filename):
    """Extrahuje symbol z názvu souboru"""
    name_without_ext = filename.replace('.csv', '').replace('.CSV', '')
    parts = name_without_ext.split('_')
    if parts:
        return parts[0].upper()
    return name_without_ext.upper()

def validate_d1_data_file(file_path):
    """Validuje zda je soubor platný D1 datový soubor"""
    try:
        if not os.path.exists(file_path):
            return False, 'Soubor neexistuje'
        
        filename = os.path.basename(file_path)
        if not is_d1_data_file(filename):
            return False, 'Soubor není D1 data (musí obsahovat D1 v názvu)'
        
        if os.path.getsize(file_path) == 0:
            return False, 'Soubor je prázdný'
        
        # Základní kontrola CSV formátu
        try:
            df = pd.read_csv(file_path, nrows=5, encoding='utf-8')
            if len(df.columns) < 4:
                return False, 'Nedostatek sloupců v CSV (očekáváno alespoň 4)'
        except Exception as e:
            return False, f'Chyba při čtení CSV: {str(e)}'
        
        return True, 'OK'
        
    except Exception as e:
        return False, f'Chyba při validaci souboru: {str(e)}'

def get_file_info(file_path):
    """Získá detailní informace o datovém souboru"""
    try:
        df = pd.read_csv(file_path, encoding='utf-8')

        # Standardizace názvů sloupců
        column_mapping = {}
        for col in df.columns:
            col_lower = col.lower().strip()
            if col_lower in ['datum', 'date']:
                column_mapping[col] = 'Datum'
            elif col_lower in ['čas', 'cas', 'time']:
                column_mapping[col] = 'Čas'

        df = df.rename(columns=column_mapping)

        # Parsování datumu s podporou různých formátů
        def parse_datetime_row(row):
            try:
                datum_str = str(row['Datum']).strip()
                cas_str = str(row.get('Čas', '00:00')).strip()

                # Normalizace datumu
                if '.' in datum_str:
                    datum_str = datum_str.replace('.', '-')

                # Normalizace času
                if ':' in cas_str and len(cas_str.split(':')) == 3:
                    cas_str = cas_str[:5]

                return pd.to_datetime(f"{datum_str} {cas_str}", errors='coerce')
            except:
                return pd.NaT

        if 'Datum' in df.columns:
            df['Date'] = df.apply(parse_datetime_row, axis=1)
        else:
            # Pokus o první sloupec jako datum
            df['Date'] = pd.to_datetime(df.iloc[:, 0], errors='coerce')

        # Filtruje platná data
        valid_dates = df['Date'].dropna()

        if len(valid_dates) == 0:
            return None, 'Nenalezena platná data'

        return {
            'total_records': len(df),
            'valid_dates': len(valid_dates),
            'start_date': valid_dates.min(),
            'end_date': valid_dates.max(),
            'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
        }, None

    except Exception as e:
        return None, f'Chyba při analýze souboru: {str(e)}'

def scan_d1_files():
    """Naskenuje všechny D1 soubory v adresáři"""
    print(f"🔍 Skenuji D1 datové soubory v adresáři: {DATA_DIRECTORY}")
    print("=" * 80)
    
    if not os.path.exists(DATA_DIRECTORY):
        print(f"❌ Datový adresář neexistuje: {DATA_DIRECTORY}")
        return []
    
    csv_files = glob.glob(os.path.join(DATA_DIRECTORY, "*.csv"))
    all_files = []
    d1_files = []
    
    print(f"📁 Nalezeno {len(csv_files)} CSV souborů celkem")
    print()
    
    for file_path in csv_files:
        filename = os.path.basename(file_path)
        all_files.append(filename)
        
        if is_d1_data_file(filename):
            symbol = extract_symbol_from_filename(filename)
            is_valid, error_msg = validate_d1_data_file(file_path)
            
            file_info = {
                'filename': filename,
                'full_path': file_path,
                'symbol': symbol,
                'timeframe': 'D1',
                'is_valid': is_valid,
                'error': error_msg if not is_valid else None
            }
            
            if is_valid:
                info, error = get_file_info(file_path)
                if info:
                    file_info.update(info)
                else:
                    file_info['error'] = error
                    file_info['is_valid'] = False
            
            d1_files.append(file_info)
    
    # Seřadí podle symbolu
    d1_files.sort(key=lambda x: x['symbol'])
    
    return d1_files, all_files

def print_file_report(d1_files, all_files):
    """Vytiskne detailní report o souborech"""
    print("📊 REPORT D1 DATOVÝCH SOUBORŮ")
    print("=" * 80)
    
    valid_files = [f for f in d1_files if f['is_valid']]
    invalid_files = [f for f in d1_files if not f['is_valid']]
    
    print(f"📈 Celkem CSV souborů: {len(all_files)}")
    print(f"🎯 D1 souborů nalezeno: {len(d1_files)}")
    print(f"✅ Platných D1 souborů: {len(valid_files)}")
    print(f"❌ Neplatných D1 souborů: {len(invalid_files)}")
    print()
    
    if valid_files:
        print("✅ PLATNÉ D1 SOUBORY:")
        print("-" * 80)
        print(f"{'Symbol':<10} {'Soubor':<35} {'Záznamy':<10} {'Období':<25} {'Velikost'}")
        print("-" * 80)
        
        for file_info in valid_files:
            records = f"{file_info.get('total_records', 'N/A'):,}" if file_info.get('total_records') else 'N/A'
            
            if file_info.get('start_date') and file_info.get('end_date'):
                period = f"{file_info['start_date'].strftime('%Y-%m-%d')} - {file_info['end_date'].strftime('%Y-%m-%d')}"
            else:
                period = 'N/A'
            
            size_mb = f"{file_info.get('file_size_mb', 0):.1f} MB" if file_info.get('file_size_mb') else 'N/A'
            
            filename_short = file_info['filename'][:34] + '...' if len(file_info['filename']) > 35 else file_info['filename']
            
            print(f"{file_info['symbol']:<10} {filename_short:<35} {records:<10} {period:<25} {size_mb}")
        
        print()
    
    if invalid_files:
        print("❌ NEPLATNÉ D1 SOUBORY:")
        print("-" * 60)
        for file_info in invalid_files:
            print(f"   {file_info['filename']}")
            print(f"   └─ Chyba: {file_info['error']}")
            print()
    
    # Soubory které nejsou D1
    non_d1_files = [f for f in all_files if not any(d1['filename'] == f for d1 in d1_files)]
    if non_d1_files:
        print(f"ℹ️  OSTATNÍ CSV SOUBORY (nejsou D1): {len(non_d1_files)}")
        print("-" * 40)
        for filename in sorted(non_d1_files)[:10]:  # Zobrazí jen prvních 10
            print(f"   {filename}")
        if len(non_d1_files) > 10:
            print(f"   ... a dalších {len(non_d1_files) - 10} souborů")
        print()

def create_directory_if_not_exists():
    """Vytvoří datový adresář pokud neexistuje"""
    if not os.path.exists(DATA_DIRECTORY):
        try:
            os.makedirs(DATA_DIRECTORY, exist_ok=True)
            print(f"✅ Vytvořen datový adresář: {DATA_DIRECTORY}")
            return True
        except Exception as e:
            print(f"❌ Chyba při vytváření adresáře: {e}")
            return False
    return True

def main():
    parser = argparse.ArgumentParser(description='D1 Data Manager - Správa D1 datových souborů')
    parser.add_argument('--scan', action='store_true', help='Naskenuje a zobrazí všechny D1 soubory')
    parser.add_argument('--create-dir', action='store_true', help='Vytvoří datový adresář pokud neexistuje')
    parser.add_argument('--list-valid', action='store_true', help='Zobrazí pouze platné D1 soubory')
    
    args = parser.parse_args()
    
    print("🎯 D1 DATA MANAGER")
    print("=" * 50)
    print(f"📁 Datový adresář: {DATA_DIRECTORY}")
    print(f"🎯 Podporované timeframy: {', '.join(SUPPORTED_TIMEFRAMES)}")
    print()
    
    if args.create_dir:
        create_directory_if_not_exists()
        return
    
    if not os.path.exists(DATA_DIRECTORY):
        print(f"❌ Datový adresář neexistuje: {DATA_DIRECTORY}")
        print("💡 Použijte --create-dir pro vytvoření adresáře")
        return
    
    d1_files, all_files = scan_d1_files()
    
    if args.list_valid:
        valid_files = [f for f in d1_files if f['is_valid']]
        print("✅ PLATNÉ D1 SOUBORY:")
        for file_info in valid_files:
            print(f"   {file_info['symbol']} - {file_info['filename']}")
    else:
        print_file_report(d1_files, all_files)
    
    print()
    print("💡 Tip: Pro použití v aplikacích zkopírujte D1 CSV soubory do adresáře:")
    print(f"   {DATA_DIRECTORY}")
    print()
    print("📋 Požadavky na D1 soubory:")
    print("   • Název musí obsahovat 'D1' (např. EURUSD_D1.csv)")
    print("   • Formát CSV s alespoň 4 sloupci")
    print("   • Obsahuje platná data s datumy")

if __name__ == "__main__":
    main()
