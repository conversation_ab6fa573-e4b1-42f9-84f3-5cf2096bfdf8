#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug test pro toleranční logiku 4.9.2025
"""

import pandas as pd

# Testovací data kolem 4.9.2025
test_data = [
    # Date, Open, High, Low, Close, <PERSON>, <PERSON>_<PERSON>, ATR10_s1
    ("2025-09-01", 3445.648, 3489.595, 3436.548, 3476.225, True, True, 36.0608),   # <PERSON>
    ("2025-09-02", 3476.505, 3539.849, 3469.805, 3532.405, True, True, 37.8775),   # <PERSON>  
    ("2025-09-03", 3532.698, 3578.175, 3525.848, 3558.475, True, True, 41.8429),   # Green Marubozu - KLÍČOV<PERSON>
    ("2025-09-04", 3560.448, 3563.998, 3510.425, 3544.645, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, 43.1813), # Red Normal - TESTOVAN<PERSON>
    ("2025-09-05", 3545.935, 3599.905, 3539.715, 3585.195, True, True, 45.8399),   # <PERSON>
]

def simulate_tolerance_logic():
    """Simuluje toleranční logiku"""
    print("🔍 SIMULACE TOLERANČNÍ LOGIKY")
    print("=" * 60)
    
    # Vytvoření DataFrame
    df = pd.DataFrame(test_data, columns=['Date', 'Open', 'High', 'Low', 'Close', 'Bull', 'Is_Marubozu', 'ATR10_s1'])
    
    # Simulace logiky
    rows = []
    on = False
    dir_bull = None
    order = 0
    last_dir_idx = None
    pending_entry = False
    
    for i, row in df.iterrows():
        day_bull = bool(row["Bull"])
        mb = bool(row["Is_Marubozu"])
        series_order = 0
        tolerated = False
        
        print(f"\n📅 {row['Date']} - {'Green' if day_bull else 'Red'} {'Marubozu' if mb else 'Normal'}")
        print(f"   Close: {row['Close']:.3f}, Low: {row['Low']:.3f}")
        print(f"   Stav před: on={on}, dir_bull={dir_bull}, order={order}, last_dir_idx={last_dir_idx}, pending_entry={pending_entry}")
        
        if pending_entry:
            # Toto je den vstupu do obchodu (den po Marubozu)
            on = True
            order = 1
            series_order = 1
            last_dir_idx = i
            pending_entry = False
            print(f"   ✅ VSTUP DO OBCHODU - Series_Order=1")
        elif on:
            if dir_bull:  # UP sekvence
                if day_bull:
                    order += 1
                    last_dir_idx = i
                    series_order = order
                    print(f"   ✅ Pokračování UP sekvence - Series_Order={series_order}")
                else:
                    # Červená svíčka v UP sekvenci
                    if mb:
                        on = False
                        dir_bull = False
                        order = 0
                        last_dir_idx = None
                        pending_entry = True
                        print(f"   🔄 Red Marubozu - ukončuje UP, začíná DOWN")
                    else:
                        # Testování tolerance
                        if last_dir_idx is not None:
                            last_low = float(df.iloc[last_dir_idx]["Low"])
                            current_close = float(row["Close"])
                            print(f"   🧪 TOLERANCE TEST:")
                            print(f"      Last green Low (idx {last_dir_idx}): {last_low:.3f}")
                            print(f"      Current red Close: {current_close:.3f}")
                            print(f"      Test: {current_close:.3f} >= {last_low:.3f} = {current_close >= last_low}")
                            
                            if current_close >= last_low:
                                order += 1
                                series_order = order
                                tolerated = True
                                print(f"   ✅ TOLEROVÁNO - Series_Order={series_order}")
                            else:
                                on = False
                                dir_bull = None
                                order = 0
                                last_dir_idx = None
                                print(f"   ❌ NETOLEROVANO - sekvence končí")
                        else:
                            print(f"   ❌ last_dir_idx je None - sekvence končí")
                            on = False
                            dir_bull = None
                            order = 0
                            last_dir_idx = None
            else:  # DOWN sekvence
                if not day_bull:
                    order += 1
                    last_dir_idx = i
                    series_order = order
                    print(f"   ✅ Pokračování DOWN sekvence - Series_Order={series_order}")
                else:
                    # Zelená svíčka v DOWN sekvenci
                    if mb:
                        on = False
                        dir_bull = True
                        order = 0
                        last_dir_idx = None
                        pending_entry = True
                        print(f"   🔄 Green Marubozu - ukončuje DOWN, začíná UP")
                    else:
                        # Testování tolerance pro DOWN
                        if last_dir_idx is not None:
                            last_high = float(df.iloc[last_dir_idx]["High"])
                            current_close = float(row["Close"])
                            print(f"   🧪 TOLERANCE TEST (DOWN):")
                            print(f"      Last red High (idx {last_dir_idx}): {last_high:.3f}")
                            print(f"      Current green Close: {current_close:.3f}")
                            print(f"      Test: {current_close:.3f} <= {last_high:.3f} = {current_close <= last_high}")
                            
                            if current_close <= last_high:
                                order += 1
                                series_order = order
                                tolerated = True
                                print(f"   ✅ TOLEROVÁNO - Series_Order={series_order}")
                            else:
                                on = False
                                dir_bull = None
                                order = 0
                                last_dir_idx = None
                                print(f"   ❌ NETOLEROVANO - sekvence končí")
                        else:
                            print(f"   ❌ last_dir_idx je None - sekvence končí")
                            on = False
                            dir_bull = None
                            order = 0
                            last_dir_idx = None
        else:
            if mb:
                dir_bull = day_bull
                pending_entry = True
                print(f"   🎯 Marubozu signál - čekáme na vstup následující den")
        
        print(f"   Stav po: on={on}, dir_bull={dir_bull}, order={order}, last_dir_idx={last_dir_idx}, pending_entry={pending_entry}")
        
        rows.append({
            "Date": row["Date"],
            "Color": "Green" if day_bull else "Red",
            "Is_Marubozu": mb,
            "Series_Order": int(series_order),
            "Direction": "Up" if (dir_bull if on else day_bull) else "Down",
            "Tolerated_Opposite": tolerated,
            "Close": row["Close"],
            "Low": row["Low"],
            "High": row["High"]
        })
    
    return pd.DataFrame(rows)

def main():
    print("🎯 DEBUG TOLERANCE LOGIC - 4.9.2025")
    print("=" * 80)
    
    result_df = simulate_tolerance_logic()
    
    print(f"\n📊 VÝSLEDKY:")
    print("=" * 60)
    for _, row in result_df.iterrows():
        tolerated_str = "✅ TOLEROVÁNO" if row["Tolerated_Opposite"] else ""
        print(f"{row['Date']} | {row['Color']:5} | Marubozu: {row['Is_Marubozu']} | Series_Order: {row['Series_Order']} | {tolerated_str}")
    
    # Kontrola 4.9.2025
    sep_4_row = result_df[result_df['Date'] == '2025-09-04'].iloc[0]
    print(f"\n🎯 KONTROLA 4.9.2025:")
    print(f"Series_Order: {sep_4_row['Series_Order']}")
    print(f"Tolerated: {sep_4_row['Tolerated_Opposite']}")
    
    if sep_4_row['Tolerated_Opposite']:
        print("✅ 4.9.2025 byla správně tolerována!")
    else:
        print("❌ 4.9.2025 nebyla tolerována - CHYBA!")

if __name__ == "__main__":
    main()
