# 🚀 TrendTaker Next.js v2.0

**Pokročilá webová aplikace pro testování obchodních strategií založených na Marubozu svíčkách s LONG-FLAT-LIMIT logikou.**

## 📋 Obsah

- [🎯 <PERSON><PERSON><PERSON><PERSON>](#-p<PERSON>ehled)
- [✨ Nové funkce v2.0](#-nové-funkce-v20)
- [🛠️ Instalace](#️-instalace)
- [🚀 Spuštění](#-spuštění)
- [📊 Použití](#-použití)
- [🔧 Konfigurace](#-konfigurace)
- [📈 Strategie](#-strategie)
- [📋 API Reference](#-api-reference)
- [🧪 Testování](#-testování)
- [📁 Struktura projektu](#-struktura-projektu)

## 🎯 Přehled

TrendTaker Next.js v2.0 je moderní webová aplikace postavená na **Next.js 14** s **TypeScript** a **Tailwind CSS**. Aplikace implementuje pokročilé obchodní strategie s důrazem na **LONG-FLAT-LIMIT** logiku a **Advanced Stop Loss Methods**.

### Klíčové vlastnosti:
- 🎯 **Marubozu detekce** s konfigurovatelnou velikostí těla svíčky
- 🔄 **LONG-FLAT-LIMIT** strategie s denním uzavíráním LONG pozic
- 📊 **Advanced SL Methods**: Initial, BarsBack_Low, BarsBack_50pct
- 📈 **Multi-sheet Excel export** s detailní analýzou
- 🌐 **Moderní React UI** s real-time feedback
- 🔧 **Kompletní API** pro automatizaci testů

## ✨ Nové funkce v2.0

### 🎯 Advanced Stop Loss Methods
- **Initial**: Standardní SL = Entry - ATR10
- **BarsBack_Low**: SL = Low X barů zpětně od aktuální svíčky
- **BarsBack_50pct**: SL = (High + Low)/2 X barů zpětně
- **Konzervativní logika**: SL se nikdy nezvyšuje (`min(starý_SL, nový_SL)`)

### 📊 Enhanced Excel Export
- **Sheet 'Obchody'**: Hlavní obchody s konsolidovanými výsledky
- **Sheet 'Denní_Analýza'**: Rozšířená analýza s SL výpočty pro každý den
- **Sheet 'Sekvence'**: Detailní informace o sekvencích
- **Sheet 'Parametry'**: Kompletní seznam všech parametrů testu

### 🔍 Pokročilá Marubozu Detekce
- **Min Body Ratio**: Konfigurovatelná minimální velikost těla (default 60%)
- **Wick Ratio**: Maximální povolený stín (default 18%)
- **ATR Filter**: Svíčka musí být větší než ATR10

### 🌐 Smart Data Range
- **Custom Data**: Automaticky nastaví období od (aktuální datum - 1 měsíc) do konce datasetu
- **Full Dataset**: Použije celý dostupný rozsah dat
- **Auto-detection**: Automatické načtení rozsahu při změně souboru

## 🛠️ Instalace

### Požadavky
- **Node.js** 18.0 nebo vyšší
- **npm** nebo **yarn**
- **Git**

### Kroky instalace

```bash
# 1. Klonování repository
git clone https://github.com/Traderpoint/TrendTaker.git
cd TrendTaker/nextjs-app/trend-taker-nextjs

# 2. Instalace závislostí
npm install

# 3. Vytvoření adresáře pro data
mkdir -p public/data

# 4. Zkopírování D1 dat do public/data/
# Zkopírujte vaše CSV soubory s D1 daty do public/data/
```

### Závislosti
```json
{
  "next": "^14.2.32",
  "react": "^18.2.0",
  "typescript": "^5.0.0",
  "tailwindcss": "^3.3.0",
  "exceljs": "^4.4.0",
  "papaparse": "^5.4.1",
  "lucide-react": "^0.292.0"
}
```

## 🚀 Spuštění

### Development mode
```bash
npm run dev
```
Aplikace bude dostupná na: **http://localhost:3000**

### Production build
```bash
npm run build
npm start
```

### Linting
```bash
npm run lint
```

## 📊 Použití

### 1. Základní Single Test

1. **Vyberte datový soubor** z dropdown menu
2. **Nastavte rozsah dat**:
   - **Custom Data**: Automaticky nastaví posledních 30 dní
   - **Full Dataset**: Použije celý dostupný rozsah
3. **Konfigurace parametrů**:
   - **Varianta**: Strict/Tolerant
   - **RRR**: Risk/Reward ratio (doporučeno 6.5)
   - **Exit Policy**: Optimistic/Conservative
   - **Overnight Mode**: STANDARD/LONG-FLAT-LIMIT
4. **Spusťte test** tlačítkem "🚀 Spustit Test"
5. **Stáhněte Excel** s výsledky

### 2. LONG-FLAT-LIMIT Konfigurace

Pro aktivaci pokročilých funkcí:

1. **Overnight Mode**: Vyberte "LONG-FLAT-LIMIT"
2. **Buy Limit Offset**: Nastavte offset v bodech (default: 5)
3. **LONG LMT SL Method**: Vyberte metodu:
   - **Initial**: Standardní SL logika
   - **BarsBack_Low**: SL na Low X barů zpětně
   - **BarsBack_50pct**: SL na 50% X barů zpětně
4. **BarsBack**: Počet barů zpětně (zobrazí se automaticky)

### 3. Grid Test

1. **Test Mode**: Vyberte "GRID TEST"
2. **Rozsah dat**: Nastavte období pro testování
3. **Spusťte Grid Test** - otestuje všechny kombinace parametrů
4. **Analyzujte výsledky** v Excel souboru

## 🔧 Konfigurace

### Parametry aplikace

| Parametr | Popis | Default | Rozsah |
|----------|-------|---------|--------|
| **minBodyRatio** | Min. velikost těla svíčky (%) | 60 | 30-90 |
| **buyLimitOffset** | Buy Limit offset (body) | 5 | 1-20 |
| **longLmtSlMethod** | LONG LMT SL Method | Initial | Initial/BarsBack_Low/BarsBack_50pct |
| **barsBack** | BarsBack - X | 3 | 1-10 |
| **rrr** | Risk/Reward ratio | 6.5 | 1-20 |
| **riskPct** | Risk % per trade | 2.0 | 0.1-10 |
| **startEquity** | Počáteční kapitál | 10000 | 1000+ |

### Datové soubory

Aplikace očekává CSV soubory s následující strukturou:
```csv
Date,Open,High,Low,Close,Volume
2025-01-01,2650.50,2655.75,2648.25,2653.10,1250
```

**Požadované sloupce:**
- `Date`: Datum ve formátu YYYY-MM-DD
- `Open`: Otevírací cena
- `High`: Nejvyšší cena
- `Low`: Nejnižší cena
- `Close`: Zavírací cena
- `Volume`: Objem (volitelné)

## 📈 Strategie

### Marubozu Detekce

Svíčka je považována za **Marubozu**, pokud splňuje:

```typescript
const wickRatio = openWick / totalRange;
const bodyRatio = bodySize / totalRange;

const isMarubozu = 
  wickRatio < 0.18 && 
  bodyRatio >= (minBodyRatio / 100) && 
  totalRange > atr10;
```

### LONG-FLAT-LIMIT Logika

1. **UP sekvence** → **LONG pozice**:
   - Vstup na **Open** následujícího dne
   - **Denní uzavírání** na Close
   - **Buy Limit** na další den (Open - offset)
   - **Žádné swapy**

2. **DOWN sekvence** → **SHORT pozice**:
   - **Standardní logika** s overnight držením
   - **Swapy** se počítají

### Advanced SL Methods

#### Initial Method
```typescript
const stopLoss = entryPrice - atr10; // Pro LONG pozice
```

#### BarsBack_Low Method
```typescript
const barsBackIdx = Math.max(0, currentDay - barsBack);
const newSL = days[barsBackIdx].Low;
const stopLoss = Math.min(currentSL, newSL); // Konzervativní
```

#### BarsBack_50pct Method
```typescript
const barsBackIdx = Math.max(0, currentDay - barsBack);
const barsBackData = days[barsBackIdx];
const newSL = (barsBackData.High + barsBackData.Low) / 2;
const stopLoss = Math.min(currentSL, newSL); // Konzervativní
```

## 📋 API Reference

### Endpoints

#### POST /api/run-test
Spustí single test s danými parametry.

**Request Body:**
```json
{
  "dataFile": "XAUUSD_D1.csv",
  "startDate": "2025-08-01",
  "endDate": "2025-09-01",
  "variant": "Tolerant",
  "rrr": 6.5,
  "exitPolicy": "Optimistic",
  "overnightMode": "LONG-FLAT-LIMIT",
  "startEquity": 10000,
  "riskPct": 2.0,
  "minBodyRatio": 60,
  "buyLimitOffset": 5,
  "longLmtSlMethod": "BarsBack_50pct",
  "barsBack": 3
}
```

**Response:**
```json
{
  "success": true,
  "results": "🚀 Spouštím Trend Taker Test...\n...",
  "excel_file": "TrendTaker_NextJS_Tolerant_2025-08-01_2025-09-01_2025-09-18T20-30-45.xlsx"
}
```

#### GET /api/data-files
Vrátí seznam dostupných D1 datových souborů.

#### GET /api/dataset-range?filename=XAUUSD_D1.csv
Vrátí rozsah dat pro daný soubor.

#### POST /api/grid-test
Spustí grid test všech kombinací parametrů.

#### GET /api/download/[filename]
Stáhne vygenerovaný Excel soubor.

## 🧪 Testování

### Manuální testování

1. **Single Test**:
   ```bash
   # Spusťte aplikaci
   npm run dev

   # Otevřete http://localhost:3000
   # Vyberte XAUUSD soubor
   # Nastavte LONG-FLAT-LIMIT mode
   # Spusťte test
   ```

2. **Grid Test**:
   ```bash
   # Vyberte Grid Test mode
   # Nastavte rozsah dat
   # Spusťte grid test
   # Analyzujte výsledky
   ```

### Automatické testování

```bash
# Unit testy (pokud implementovány)
npm test

# E2E testy (pokud implementovány)
npm run test:e2e
```

### Validace výsledků

Porovnejte výsledky s Python WebGUI:
1. **Stejné parametry** v obou aplikacích
2. **Stejný datový soubor** a období
3. **Porovnání Excel exportů**
4. **Validace P&L** a počtu obchodů

## 📁 Struktura projektu

```
nextjs-app/trend-taker-nextjs/
├── app/                          # Next.js 14 App Router
│   ├── api/                      # API routes
│   │   ├── run-test/            # Single test endpoint
│   │   ├── grid-test/           # Grid test endpoint
│   │   ├── data-files/          # Data files listing
│   │   ├── dataset-range/       # Dataset range info
│   │   └── download/            # File download
│   ├── globals.css              # Global styles
│   ├── layout.tsx               # Root layout
│   └── page.tsx                 # Main page
├── components/                   # React components
│   ├── TestForm.tsx             # Main test form
│   └── ResultsDisplay.tsx       # Results display
├── lib/                         # Core logic
│   ├── trendTaker.ts            # Main trading engine
│   └── dataUtils.ts             # Data utilities
├── types/                       # TypeScript types
│   └── index.ts                 # Type definitions
├── public/                      # Static files
│   └── data/                    # D1 data files (CSV)
├── package.json                 # Dependencies
├── tailwind.config.js           # Tailwind configuration
├── tsconfig.json                # TypeScript configuration
└── README.md                    # This file
```

### Klíčové soubory

- **`lib/trendTaker.ts`**: Hlavní obchodní engine s LONG-FLAT-LIMIT logikou
- **`components/TestForm.tsx`**: Formulář s všemi parametry v2.0
- **`app/api/run-test/route.ts`**: API endpoint pro spuštění testů
- **`types/index.ts`**: TypeScript definice pro všechny datové struktury

### Technologie

- **Next.js 14** - React framework s App Router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **ExcelJS** - Generování Excel souborů
- **Papa Parse** - Parsování CSV souborů
- **Lucide React** - Moderní ikony

### Vývojové nástroje

- **ESLint** - Linting JavaScript/TypeScript
- **Prettier** - Code formatting
- **PostCSS** - CSS processing
- **Autoprefixer** - CSS vendor prefixes

## 🔄 Changelog v2.0

### ✨ Nové funkce
- **Advanced SL Methods**: Tři nové metody pro LONG pozice
- **Enhanced Excel Export**: Multi-sheet export s detailní analýzou
- **Smart Data Range**: Automatické nastavení Custom Data období
- **Pokročilá Marubozu detekce**: Konfigurovatelná velikost těla
- **LONG-FLAT-LIMIT logika**: Kompletní implementace s segmentací

### 🔧 Vylepšení
- **UI konzistence**: 100% shoda s Python WebGUI
- **TypeScript typy**: Kompletní typová bezpečnost
- **Error handling**: Robustní zpracování chyb
- **Performance**: Optimalizace pro velké datasety
- **Dokumentace**: Kompletní dokumentace v češtině

### 🐛 Opravy
- **Data range**: Opravena logika pro SINGLE test
- **Parameter validation**: Správná validace všech parametrů
- **Excel export**: Opraveny problémy s multi-sheet exportem
- **SL calculations**: Konzistentní výpočty s Python verzí

## 📞 Podpora

Pro technickou podporu nebo dotazy kontaktujte:
- **Email**: <EMAIL>
- **Repository**: https://github.com/Traderpoint/TrendTaker
- **Issues**: https://github.com/Traderpoint/TrendTaker/issues

## 📄 Licence

Tento projekt je proprietární software společnosti Traderpoint.

---

**🎯 TrendTaker Next.js v2.0 - Pokročilé testování obchodních strategií s moderním webovým rozhraním.**

*Vytvořeno s ❤️ pro profesionální tradery a kvantové analytiky.*
