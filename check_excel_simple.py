#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jednoduchá kontrola Excel souboru bez pandas
"""

import os
import openpyxl

def check_excel_simple():
    """Jednoduchá kontrola Excel souboru"""
    
    excel_file = "TrendTaker_WebGUI_Tolerant_2025-08-17_2025-09-18.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel soubor nenalezen: {excel_file}")
        return
    
    print(f"✅ Excel soubor nalezen: {excel_file}")
    
    try:
        # Načtení Excel souboru
        wb = openpyxl.load_workbook(excel_file)
        
        print(f"\n📊 Nalezené sheety:")
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            rows = ws.max_row
            print(f"   • {sheet_name}: {rows} řádků")
        
        # Kontrola obchodů
        if 'Obchody' in wb.sheetnames:
            ws = wb['Obchody']
            print(f"\n💹 OBCHODY:")
            
            if ws.max_row > 1:  # Má data kromě hlavičky
                print(f"   Celkem obchodů: {ws.max_row - 1}")
                
                # Hlavička
                headers = []
                for col in range(1, ws.max_column + 1):
                    header = ws.cell(row=1, column=col).value
                    if header:
                        headers.append(header)
                
                print(f"   Sloupce: {headers}")
                
                # První obchod
                if ws.max_row > 1:
                    print(f"\n   První obchod:")
                    for col, header in enumerate(headers, 1):
                        value = ws.cell(row=2, column=col).value
                        print(f"     {header}: {value}")
                        
                        # Speciální kontroly
                        if header == 'Exit_Reason':
                            if value and 'BUY_LIMIT' in str(value):
                                print(f"     ✅ BUY_LIMIT informace nalezena: {value}")
                            else:
                                print(f"     ⚠️  BUY_LIMIT informace nenalezena")
                        
                        if header == 'SL_Calc':
                            if value and str(value) != 'None':
                                print(f"     ✅ SL_Calc implementován: {str(value)[:50]}...")
                            else:
                                print(f"     ❌ SL_Calc chybí nebo je prázdný")
            else:
                print("   ❌ Žádné obchody")
        
        # Kontrola detailních segmentů
        if 'Detailní_Segmenty' in wb.sheetnames:
            ws = wb['Detailní_Segmenty']
            print(f"\n🔍 DETAILNÍ SEGMENTY:")
            print(f"   Celkem segmentů: {ws.max_row - 1}")
            
            if ws.max_row > 1:
                # Hlavička segmentů
                seg_headers = []
                for col in range(1, ws.max_column + 1):
                    header = ws.cell(row=1, column=col).value
                    if header:
                        seg_headers.append(header)
                
                print(f"   Sloupce segmentů: {seg_headers}")
                
                # Kontrola SL_Calc v segmentech
                if 'SL_Calc' in seg_headers:
                    print(f"   ✅ SL_Calc sloupec v segmentech nalezen")
                    
                    # První segment
                    sl_calc_col = seg_headers.index('SL_Calc') + 1
                    first_sl_calc = ws.cell(row=2, column=sl_calc_col).value
                    if first_sl_calc:
                        print(f"   První SL_Calc: {first_sl_calc}")
                else:
                    print(f"   ❌ SL_Calc sloupec v segmentech chybí")
        else:
            print(f"\n⚠️  Sheet 'Detailní_Segmenty' nenalezen")
        
        # Kontrola sekvencí
        if 'Sekvence' in wb.sheetnames:
            ws = wb['Sekvence']
            print(f"\n📈 SEKVENCE:")
            print(f"   Celkem sekvencí: {ws.max_row - 1}")
            
            if ws.max_row > 1:
                # Počítání UP/DOWN sekvencí
                up_count = 0
                down_count = 0
                
                # Najdeme sloupec Direction
                direction_col = None
                for col in range(1, ws.max_column + 1):
                    if ws.cell(row=1, column=col).value == 'Direction':
                        direction_col = col
                        break
                
                if direction_col:
                    for row in range(2, ws.max_row + 1):
                        direction = ws.cell(row=row, column=direction_col).value
                        if direction == 'Up':
                            up_count += 1
                        elif direction == 'Down':
                            down_count += 1
                
                print(f"   UP sekvence: {up_count}")
                print(f"   DOWN sekvence: {down_count}")
        
        wb.close()
        
        # Finální hodnocení
        print(f"\n🎯 FINÁLNÍ HODNOCENÍ:")
        print("=" * 80)
        
        success_checks = []
        
        # Check 1: Excel soubor existuje
        success_checks.append("✅ Excel soubor vytvořen")
        
        # Check 2: Obchody existují
        if 'Obchody' in wb.sheetnames:
            ws = wb['Obchody']
            if ws.max_row > 1:
                success_checks.append("✅ Obchody nalezeny")
            else:
                success_checks.append("❌ Žádné obchody")
        else:
            success_checks.append("❌ Sheet 'Obchody' chybí")
        
        # Check 3: BUY_LIMIT informace
        buy_limit_found = False
        if 'Obchody' in wb.sheetnames:
            ws = wb['Obchody']
            if ws.max_row > 1:
                # Najdeme Exit_Reason sloupec
                for col in range(1, ws.max_column + 1):
                    if ws.cell(row=1, column=col).value == 'Exit_Reason':
                        exit_reason = ws.cell(row=2, column=col).value
                        if exit_reason and 'BUY_LIMIT' in str(exit_reason):
                            buy_limit_found = True
                        break
        
        if buy_limit_found:
            success_checks.append("✅ BUY_LIMIT informace v Exit_Reason")
        else:
            success_checks.append("❌ BUY_LIMIT informace chybí")
        
        # Check 4: SL_Calc sloupec
        sl_calc_found = False
        if 'Obchody' in wb.sheetnames:
            ws = wb['Obchody']
            if ws.max_row > 1:
                # Najdeme SL_Calc sloupec
                for col in range(1, ws.max_column + 1):
                    if ws.cell(row=1, column=col).value == 'SL_Calc':
                        sl_calc = ws.cell(row=2, column=col).value
                        if sl_calc and str(sl_calc) != 'None':
                            sl_calc_found = True
                        break
        
        if sl_calc_found:
            success_checks.append("✅ SL_Calc sloupec implementován")
        else:
            success_checks.append("❌ SL_Calc sloupec chybí")
        
        # Check 5: Detailní segmenty
        if 'Detailní_Segmenty' in wb.sheetnames:
            ws = wb['Detailní_Segmenty']
            if ws.max_row > 1:
                success_checks.append("✅ Detailní segmenty nalezeny")
            else:
                success_checks.append("❌ Žádné detailní segmenty")
        else:
            success_checks.append("❌ Sheet 'Detailní_Segmenty' chybí")
        
        # Výsledek
        success_count = len([check for check in success_checks if check.startswith("✅")])
        total_checks = len(success_checks)
        
        print(f"📊 VÝSLEDKY:")
        for check in success_checks:
            print(f"   {check}")
        
        print(f"\n📈 CELKOVÉ SKÓRE: {success_count}/{total_checks} ({success_count/total_checks*100:.0f}%)")
        
        if success_count == total_checks:
            print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
            print("✅ BUY_LIMIT_NOT_FILLED a SL_Calc jsou implementovány")
            print("🚀 WEBGUI JE PŘIPRAVENO K POUŽITÍ!")
        elif success_count >= total_checks * 0.8:
            print("✅ VĚTŠINA TESTŮ ÚSPĚŠNÁ")
            print("⚠️  Drobné problémy k dořešení")
        else:
            print("❌ VÝZNAMNÉ PROBLÉMY")
            print("🔧 Nutné další opravy")
    
    except Exception as e:
        print(f"❌ Chyba při analýze Excel souboru: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_excel_simple()
