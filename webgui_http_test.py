#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTTP test pro WebGUI - simuluje kliknutí na tlačítko "Spustit test"
"""

import requests
import json
import time
import os

def test_webgui_via_http():
    """Test WebGUI přes HTTP POST request"""
    
    print("🌐 HTTP TEST WEBGUI")
    print("=" * 80)
    
    # URL WebGUI serveru
    base_url = "http://localhost:8081"
    test_url = f"{base_url}/run_test"
    
    # Parametry testu (stejné jako ve WebGUI formuláři)
    test_data = {
        "dataFile": "XAUUSD_GMT+2_US-DST_D1.csv",
        "dataRange": "CUSTOM",
        "startDate": "2025-08-19",
        "endDate": "2025-09-18",
        "variant": "Tolerant",
        "rrr": "8.0",
        "exitPolicy": "cons",
        "startEquity": "10000",
        "riskPct": "0.02",
        "overnightMode": "LONG-FLAT-LIMIT",
        "buyLimitOffset": "5",
        "minBodyPct": "5.0",
        "longLmtSlMethod": "BarsBack_50pct",
        "barsBack": "2"
    }
    
    print("📊 TESTOVACÍ PARAMETRY:")
    for key, value in test_data.items():
        print(f"   {key}: {value}")
    print()
    
    try:
        print("🚀 Odesílám HTTP POST request...")
        
        # Odeslání POST requestu
        response = requests.post(
            test_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=120  # 2 minuty timeout
        )
        
        print(f"📡 HTTP Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Request úspěšný!")
            
            # Parsování odpovědi
            try:
                result = response.json()
                print(f"📋 Odpověď serveru:")
                print(f"   Success: {result.get('success', False)}")
                
                if 'results' in result:
                    print(f"   Výsledky: {len(result['results'])} zpráv")
                    
                    # Výpis prvních několik zpráv
                    for i, msg in enumerate(result['results'][:10], 1):
                        print(f"   {i}. {msg.strip()}")
                    
                    if len(result['results']) > 10:
                        print(f"   ... a dalších {len(result['results']) - 10} zpráv")
                
                if 'excel_file' in result:
                    excel_file = result['excel_file']
                    print(f"   Excel soubor: {excel_file}")
                    
                    # Kontrola Excel souboru
                    if excel_file and os.path.exists(excel_file):
                        print(f"   ✅ Excel soubor existuje")
                        
                        # Analýza Excel souboru
                        analyze_excel_file(excel_file)
                    else:
                        print(f"   ❌ Excel soubor nenalezen")
                
            except json.JSONDecodeError as e:
                print(f"❌ Chyba při parsování JSON odpovědi: {e}")
                print(f"Raw odpověď: {response.text[:500]}...")
        
        else:
            print(f"❌ HTTP chyba: {response.status_code}")
            print(f"Odpověď: {response.text[:500]}...")
    
    except requests.exceptions.ConnectionError:
        print("❌ Nelze se připojit k WebGUI serveru!")
        print("💡 Ujistěte se, že WebGUI běží na http://localhost:8081")
        print("💡 Spusťte: py \"python-webgui/Trend Taker Web GUI.py\"")
        return False
    
    except requests.exceptions.Timeout:
        print("❌ Timeout - test trval příliš dlouho")
        return False
    
    except Exception as e:
        print(f"❌ Neočekávaná chyba: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def analyze_excel_file(excel_file):
    """Analýza Excel souboru"""
    
    print(f"\n📊 ANALÝZA EXCEL SOUBORU:")
    print("=" * 80)
    
    try:
        import openpyxl
        wb = openpyxl.load_workbook(excel_file)
        
        print(f"📋 Sheety: {wb.sheetnames}")
        
        # Analýza obchodů
        if 'Obchody' in wb.sheetnames:
            ws = wb['Obchody']
            print(f"💹 Obchody: {ws.max_row - 1} řádků")
            
            if ws.max_row > 1:
                # Získání headers
                headers = {}
                for col in range(1, ws.max_column + 1):
                    header = ws.cell(row=1, column=col).value
                    if header:
                        headers[header] = col
                
                print(f"📋 Sloupce: {list(headers.keys())}")
                
                # Kontrola klíčových sloupců
                buy_limit_found = False
                sl_calc_found = False
                
                for row in range(2, ws.max_row + 1):
                    print(f"\n🔹 OBCHOD {row-1}:")
                    
                    # Exit_Reason
                    if 'Exit_Reason' in headers:
                        exit_reason = ws.cell(row=row, column=headers['Exit_Reason']).value
                        print(f"   Exit_Reason: {exit_reason}")
                        
                        if exit_reason:
                            if 'BUY_LIMIT_NOT_FILLED' in str(exit_reason):
                                print(f"   ✅ BUY_LIMIT_NOT_FILLED nalezen!")
                                buy_limit_found = True
                            elif 'BUY_LIMIT_FILLED' in str(exit_reason):
                                print(f"   ✅ BUY_LIMIT_FILLED nalezen!")
                                buy_limit_found = True
                            elif 'BUY_LIMIT' in str(exit_reason):
                                print(f"   ⚠️  Jiná BUY_LIMIT varianta")
                                buy_limit_found = True
                            else:
                                print(f"   ❌ Žádná BUY_LIMIT informace")
                    
                    # SL_Calc
                    if 'SL_Calc' in headers:
                        sl_calc = ws.cell(row=row, column=headers['SL_Calc']).value
                        if sl_calc and str(sl_calc).strip():
                            print(f"   ✅ SL_Calc: {str(sl_calc)[:50]}...")
                            sl_calc_found = True
                        else:
                            print(f"   ❌ SL_Calc prázdný")
                    else:
                        print(f"   ❌ SL_Calc sloupec chybí")
                    
                    # Základní info
                    if 'Sequence_ID' in headers:
                        seq_id = ws.cell(row=row, column=headers['Sequence_ID']).value
                        print(f"   Sequence_ID: {seq_id}")
                    
                    if 'Net_PnL' in headers:
                        pnl = ws.cell(row=row, column=headers['Net_PnL']).value
                        print(f"   P&L: ${pnl:.2f}" if pnl else "   P&L: N/A")
                
                # Finální hodnocení
                print(f"\n🎯 HODNOCENÍ EXCEL:")
                print("=" * 40)
                
                checks = []
                if buy_limit_found:
                    checks.append("✅ BUY_LIMIT informace nalezena")
                else:
                    checks.append("❌ BUY_LIMIT informace chybí")
                
                if sl_calc_found:
                    checks.append("✅ SL_Calc implementován")
                else:
                    checks.append("❌ SL_Calc chybí")
                
                if 'Detailní_Segmenty' in wb.sheetnames:
                    checks.append("✅ Detailní segmenty přítomny")
                else:
                    checks.append("❌ Detailní segmenty chybí")

                # Kontrola Denní_Analýza
                if 'Denní_Analýza' in wb.sheetnames:
                    daily_ws = wb['Denní_Analýza']
                    daily_headers = []
                    for col in range(1, daily_ws.max_column + 1):
                        header = daily_ws.cell(row=1, column=col).value
                        if header:
                            daily_headers.append(header)

                    print(f"   📊 Denní_Analýza sloupce: {daily_headers}")

                    if 'LONG_LMT_SL_Method' in daily_headers and 'Calculated_SL' in daily_headers:
                        checks.append("✅ Denní analýza rozšířena")

                        # Ukázka prvního řádku
                        if daily_ws.max_row > 1:
                            method_col = daily_headers.index('LONG_LMT_SL_Method') + 1
                            sl_col = daily_headers.index('Calculated_SL') + 1
                            method_val = daily_ws.cell(row=2, column=method_col).value
                            sl_val = daily_ws.cell(row=2, column=sl_col).value
                            print(f"   📈 První den: Method={method_val}, SL={sl_val}")
                    else:
                        checks.append("❌ Denní analýza nerozšířena")
                else:
                    checks.append("❌ Denní_Analýza sheet chybí")
                
                for check in checks:
                    print(f"   {check}")
                
                success_count = len([c for c in checks if c.startswith("✅")])
                total_count = len(checks)
                
                print(f"\n📈 SKÓRE: {success_count}/{total_count} ({success_count/total_count*100:.0f}%)")
                
                if success_count == total_count:
                    print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
                elif success_count >= total_count * 0.7:
                    print("✅ VĚTŠINA TESTŮ ÚSPĚŠNÁ")
                else:
                    print("❌ PROBLÉMY K DOŘEŠENÍ")
            
            else:
                print("❌ Žádné obchody v Excel")
        else:
            print("❌ Sheet 'Obchody' nenalezen")
        
        wb.close()
        
    except Exception as e:
        print(f"❌ Chyba při analýze Excel: {e}")

if __name__ == "__main__":
    print("🌐 WEBGUI HTTP TEST")
    print("=" * 80)
    print("💡 WebGUI by měl běžet na http://localhost:8081")
    print()

    success = test_webgui_via_http()
    
    if success:
        print("\n🎉 TEST DOKONČEN!")
    else:
        print("\n❌ TEST SELHAL!")
