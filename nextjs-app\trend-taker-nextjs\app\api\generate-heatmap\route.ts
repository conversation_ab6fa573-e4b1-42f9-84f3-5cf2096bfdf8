import { NextRequest, NextResponse } from 'next/server';
import * as fs from 'fs';
import * as path from 'path';
import ExcelJS from 'exceljs';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { excel_file } = await request.json();
    
    if (!excel_file) {
      return NextResponse.json({
        success: false,
        error: 'Excel soubor není specifikován'
      });
    }

    const excelPath = path.join(process.cwd(), 'public', excel_file);
    
    if (!fs.existsSync(excelPath)) {
      return NextResponse.json({
        success: false,
        error: 'Excel soubor nenalezen'
      });
    }

    // Načtení Excel souboru
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(excelPath);
    
    // Hledání listu s výsledky grid testu
    let resultsSheet = workbook.getWorksheet('Grid_Results');
    if (!resultsSheet) {
      // Pokus o alternativní n<PERSON>zvy
      resultsSheet = workbook.getWorksheet('Results') || 
                    workbook.getWorksheet('GridTest') ||
                    workbook.getWorksheet(1); // První list
    }
    
    if (!resultsSheet) {
      return NextResponse.json({
        success: false,
        error: 'Nenalezen list s výsledky grid testu'
      });
    }

    // Načtení dat z Excel listu
    const results: any[] = [];
    const headerRow = resultsSheet.getRow(1);
    const headers: string[] = [];
    
    // Získání hlaviček
    headerRow.eachCell((cell, colNumber) => {
      headers[colNumber - 1] = cell.value?.toString() || '';
    });
    
    // Načtení dat
    resultsSheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return; // Přeskočit hlavičku
      
      const rowData: any = {};
      row.eachCell((cell, colNumber) => {
        const header = headers[colNumber - 1];
        if (header) {
          rowData[header] = cell.value;
        }
      });
      
      if (Object.keys(rowData).length > 0) {
        results.push(rowData);
      }
    });

    if (results.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Žádná data nalezena v Excel souboru'
      });
    }

    // Generování HeatMap
    const heatmapLines: string[] = [];
    heatmapLines.push('🔥 TREND TAKER - GRID TEST HEATMAP\n');
    heatmapLines.push('=' .repeat(50) + '\n');
    heatmapLines.push(`📊 Celkem kombinací: ${results.length}\n`);
    heatmapLines.push(`📅 Generováno: ${new Date().toLocaleString('cs-CZ')}\n\n`);

    // Seskupení podle RRR
    const rrr_groups: { [key: string]: any[] } = {};
    results.forEach(result => {
      const rrr = result.RRR || result.rrr || 'N/A';
      const key = `RRR_${rrr}`;
      if (!rrr_groups[key]) {
        rrr_groups[key] = [];
      }
      rrr_groups[key].push(result);
    });

    // HeatMap podle RRR
    heatmapLines.push('📈 HEATMAP PODLE RRR:\n');
    heatmapLines.push('-'.repeat(30) + '\n');
    
    Object.keys(rrr_groups).sort().forEach(rrrKey => {
      const rrr = rrrKey.replace('RRR_', '');
      const group = rrr_groups[rrrKey];
      
      heatmapLines.push(`\n🎯 RRR = ${rrr} (${group.length} kombinací):\n`);
      
      // Seřazení podle Net Profit
      const sortedByProfit = group.sort((a, b) => {
        const profitA = parseFloat(a.Net_Profit || a.net_profit || 0);
        const profitB = parseFloat(b.Net_Profit || b.net_profit || 0);
        return profitB - profitA;
      });
      
      // Top 3 pro toto RRR
      for (let i = 0; i < Math.min(3, sortedByProfit.length); i++) {
        const result = sortedByProfit[i];
        const variant = result.Variant || result.variant || 'N/A';
        const exitPolicy = result.Exit_Policy || result.exit_policy || 'N/A';
        const overnightMode = result.Overnight_Mode || result.overnight_mode || 'N/A';
        const netProfit = parseFloat(result.Net_Profit || result.net_profit || 0);
        const profitFactor = parseFloat(result.Profit_Factor || result.profit_factor || 0);
        const winRate = parseFloat(result.Win_Rate || result.win_rate || 0);
        
        heatmapLines.push(`   ${i + 1}. ${variant} | ${exitPolicy} | ${overnightMode}\n`);
        heatmapLines.push(`      💰 P&L: $${netProfit.toFixed(2)} | PF: ${profitFactor.toFixed(2)} | WR: ${winRate.toFixed(1)}%\n`);
      }
    });

    // Celkové TOP výsledky
    heatmapLines.push('\n🏆 CELKOVÉ TOP VÝSLEDKY:\n');
    heatmapLines.push('='.repeat(30) + '\n');
    
    // TOP podle Net Profit
    const topByProfit = results.sort((a, b) => {
      const profitA = parseFloat(a.Net_Profit || a.net_profit || 0);
      const profitB = parseFloat(b.Net_Profit || b.net_profit || 0);
      return profitB - profitA;
    }).slice(0, 5);
    
    heatmapLines.push('\n💰 TOP 5 podle Net Profit:\n');
    topByProfit.forEach((result, index) => {
      const variant = result.Variant || result.variant || 'N/A';
      const rrr = result.RRR || result.rrr || 'N/A';
      const exitPolicy = result.Exit_Policy || result.exit_policy || 'N/A';
      const overnightMode = result.Overnight_Mode || result.overnight_mode || 'N/A';
      const netProfit = parseFloat(result.Net_Profit || result.net_profit || 0);
      const profitFactor = parseFloat(result.Profit_Factor || result.profit_factor || 0);
      
      heatmapLines.push(`${index + 1}. ${variant} RRR=${rrr} ${exitPolicy} ${overnightMode}\n`);
      heatmapLines.push(`   💰 $${netProfit.toFixed(2)} | PF: ${profitFactor.toFixed(2)}\n`);
    });
    
    // TOP podle Profit Factor
    const topByPF = results.sort((a, b) => {
      const pfA = parseFloat(a.Profit_Factor || a.profit_factor || 0);
      const pfB = parseFloat(b.Profit_Factor || b.profit_factor || 0);
      return pfB - pfA;
    }).slice(0, 5);
    
    heatmapLines.push('\n📊 TOP 5 podle Profit Factor:\n');
    topByPF.forEach((result, index) => {
      const variant = result.Variant || result.variant || 'N/A';
      const rrr = result.RRR || result.rrr || 'N/A';
      const exitPolicy = result.Exit_Policy || result.exit_policy || 'N/A';
      const overnightMode = result.Overnight_Mode || result.overnight_mode || 'N/A';
      const netProfit = parseFloat(result.Net_Profit || result.net_profit || 0);
      const profitFactor = parseFloat(result.Profit_Factor || result.profit_factor || 0);
      
      heatmapLines.push(`${index + 1}. ${variant} RRR=${rrr} ${exitPolicy} ${overnightMode}\n`);
      heatmapLines.push(`   PF: ${profitFactor.toFixed(2)} | 💰 $${netProfit.toFixed(2)}\n`);
    });

    // Statistiky
    heatmapLines.push('\n📈 STATISTIKY:\n');
    heatmapLines.push('-'.repeat(20) + '\n');
    
    const profits = results.map(r => parseFloat(r.Net_Profit || r.net_profit || 0));
    const profitFactors = results.map(r => parseFloat(r.Profit_Factor || r.profit_factor || 0));
    
    const avgProfit = profits.reduce((sum, p) => sum + p, 0) / profits.length;
    const maxProfit = Math.max(...profits);
    const minProfit = Math.min(...profits);
    const avgPF = profitFactors.reduce((sum, pf) => sum + pf, 0) / profitFactors.length;
    const maxPF = Math.max(...profitFactors);
    
    heatmapLines.push(`💰 Průměrný P&L: $${avgProfit.toFixed(2)}\n`);
    heatmapLines.push(`💰 Maximální P&L: $${maxProfit.toFixed(2)}\n`);
    heatmapLines.push(`💰 Minimální P&L: $${minProfit.toFixed(2)}\n`);
    heatmapLines.push(`📊 Průměrný PF: ${avgPF.toFixed(2)}\n`);
    heatmapLines.push(`📊 Maximální PF: ${maxPF.toFixed(2)}\n`);

    // Uložení HeatMap do souboru
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const heatmapFilename = `HeatMap_${timestamp}.txt`;
    const heatmapPath = path.join(process.cwd(), 'public', heatmapFilename);
    
    fs.writeFileSync(heatmapPath, heatmapLines.join(''), 'utf-8');

    return NextResponse.json({
      success: true,
      heatmap_file: heatmapFilename,
      heatmap_content: heatmapLines.join('')
    });

  } catch (error) {
    console.error('Error generating heatmap:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Neznámá chyba při generování heatmap'
    });
  }
}
