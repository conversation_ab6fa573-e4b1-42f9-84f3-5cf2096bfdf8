import { NextRequest, NextResponse } from 'next/server';
import { TrendTakerEngine } from '@/lib/trendTaker';
import { validateD1DataFile } from '@/lib/dataUtils';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const params = await request.json();
    
    // Validace parametrů
    const {
      dataFile,
      startDate = '2020-01-01',
      endDate = '2025-09-05',
      variant = 'Strict',
      rrr = 6.5,
      exitPolicy = 'Optimistic',
      overnightMode = 'STANDARD',
      startEquity = 10000,
      riskPct = 2.0,
      // v2.0 New Parameters
      buyLimitOffset = 5,
      minBodyRatio = 60,
      longLmtSlMethod = 'Initial',
      barsBack = 3
    } = params;

    if (!dataFile) {
      return NextResponse.json({
        success: false,
        error: 'Datový soubor není specifikován'
      });
    }

    const results: string[] = [];
    results.push('🚀 Spouštím Trend Taker Test...\n');
    
    // Parametry testu
    results.push('📊 Parametry testu:\n');
    results.push(`   • Soubor: ${dataFile}\n`);
    results.push(`   • Období: ${startDate} - ${endDate}\n`);
    results.push(`   • Varianta: ${variant}\n`);
    results.push(`   • RRR: ${rrr}\n`);
    results.push(`   • Exit Policy: ${exitPolicy}\n`);
    results.push(`   • Overnight Mode: ${overnightMode}\n`);
    results.push(`   • Kapitál: $${startEquity}\n`);
    results.push(`   • Risk: ${riskPct}%\n`);

    // v2.0 New Parameters
    results.push(`   • Min Body Ratio: ${minBodyRatio}%\n`);
    if (overnightMode === 'LONG-FLAT-LIMIT') {
      results.push(`   • Buy Limit Offset: ${buyLimitOffset} bodů\n`);
      results.push(`   • LONG LMT SL Method: ${longLmtSlMethod}\n`);
      if (longLmtSlMethod !== 'Initial') {
        results.push(`   • BarsBack: ${barsBack}\n`);
      }
    }
    if (variant === 'Tolerant') {
      results.push(`   • Tolerant logika: Pokročilá tolerance opačných svíček\n`);
    }
    results.push('\n');

    // Inicializace engine
    const engine = new TrendTakerEngine();
    
    // Načtení dat z C:\Temp\Trading\DATA ICM
    results.push('📈 Načítám D1 data...\n');

    const candleData = await engine.loadData(dataFile);
    results.push(`✅ Načteno ${candleData.length} barů\n`);

    // CRITICAL FIX: Výpočet ATR a Marubozu na CELÉM datasetu (jako Python WebGUI)
    results.push('🔍 Počítám ATR a detekuji Marubozu na celém datasetu...\n');
    const processedDataAll = engine.calculateATRAndMarubozu(candleData, minBodyRatio);

    // Filtrace podle datumů AŽ PO výpočtu ATR (jako Python WebGUI)
    const startDt = new Date(startDate);
    const endDt = new Date(endDate);
    const filteredData = processedDataAll.filter(candle =>
      candle.Date >= startDt && candle.Date <= endDt
    );
    results.push(`📅 Filtrováno na ${filteredData.length} barů pro období ${startDate} - ${endDate}\n`);

    if (filteredData.length === 0) {
      throw new Error('Žádná data v zadaném období!');
    }

    // Marubozu count na filtrovaných datech
    const marubozu_count = filteredData.filter(candle => candle.Is_Marubozu).length;
    results.push(`🎯 Nalezeno ${marubozu_count} Marubozu signálů (${(marubozu_count/filteredData.length*100).toFixed(1)}%)\n`);
    
    // CRITICAL FIX: Sekvenční logika na celém datasetu, pak filtrace (jako Python WebGUI)
    results.push(`🔄 Aplikuji ${variant} sekvenční logiku na celém datasetu...\n`);
    const daysDataAll = variant === 'Strict'
      ? engine.buildDaysStrict(processedDataAll)
      : engine.buildDaysTolerant(processedDataAll);

    // Filtrace denních dat na požadované období
    const daysData = daysDataAll.filter(day =>
      day.Date >= startDt && day.Date <= endDt
    );
    
    // Vytvoření sekvencí
    results.push('📊 Vytvářím sekvence...\n');
    const sequences = engine.buildSequences(daysData);
    results.push(`📊 Nalezeno ${sequences.length} sekvencí v období\n`);
    
    // Simulace obchodů
    results.push('💹 Simulujem obchody...\n');
    const trades = engine.simulateTrades(
      daysData,
      sequences,
      rrr,
      exitPolicy,
      startEquity,
      riskPct / 100,
      overnightMode,
      buyLimitOffset,
      longLmtSlMethod,
      barsBack
    );
    
    // Výsledky
    if (trades.length > 0) {
      const finalEquity = trades[trades.length - 1].Equity_After;
      const totalPnL = finalEquity - startEquity;
      const winTrades = trades.filter(t => t.Net_PnL > 0).length;
      const lossTrades = trades.filter(t => t.Net_PnL < 0).length;
      const winRate = trades.length > 0 ? (winTrades / trades.length * 100) : 0;
      
      results.push('\n📈 VÝSLEDKY:\n');
      results.push(`💰 Celkový P&L: $${totalPnL.toFixed(2)}\n`);
      results.push(`📊 Počet obchodů: ${trades.length}\n`);
      results.push(`✅ Výherní: ${winTrades} (${winRate.toFixed(1)}%)\n`);
      results.push(`❌ Prohrané: ${lossTrades}\n`);
      results.push(`💵 Finální kapitál: $${finalEquity.toFixed(2)}\n`);
    } else {
      results.push('\n⚠️ Žádné obchody nebyly provedeny.\n');
    }

    // Uložení do Excel
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const excelFilename = `TrendTaker_NextJS_${variant}_${startDate}_${endDate}_${timestamp}.xlsx`;
    
    results.push('\n💾 Ukládám do Excel...\n');
    const excelCreated = await engine.saveToExcel(
      trades, 
      daysData, 
      sequences, 
      params, 
      excelFilename
    );
    
    if (excelCreated) {
      results.push(`✅ Excel soubor vytvořen: ${excelFilename}\n`);
    }

    return NextResponse.json({
      success: true,
      results: results.join(''),
      excel_file: excelCreated ? excelFilename : undefined
    });

  } catch (error) {
    console.error('Error in run-test:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Neznámá chyba'
    });
  }
}
