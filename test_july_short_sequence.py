#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pro ověření SHORT sekvence 23.7. - 31.7.2025
Podle výsledků: SHORT sekvence skončila 31.7. kv<PERSON><PERSON> SEQ_END
"""

import pandas as pd
import numpy as np

# Testovací data z XAUUSD kolem 23.7.2025 - 31.7.2025 + několik dní navíc
test_data = [
    # Datum, Open, High, Low, Close
    ("2025.07.21", 3349.898, 3401.339, 3344.798, 3396.535),  # <PERSON><PERSON><PERSON>
    ("2025.07.22", 3396.895, 3433.279, 3383.115, 3430.855),  # <PERSON><PERSON><PERSON>
    ("2025.07.23", 3431.605, 3438.655, 3381.215, 3386.615),  # <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> za<PERSON>átek SHORT?
    ("2025.07.24", 3387.698, 3393.165, 3351.168, 3367.905),  # <PERSON><PERSON><PERSON>
    ("2025.07.25", 3367.695, 3373.295, 3324.805, 3335.575),  # <PERSON><PERSON><PERSON>
    ("2025.07.28", 3316.938, 3345.145, 3301.465, 3312.075),  # <PERSON><PERSON><PERSON>
    ("2025.07.29", 3314.255, 3333.908, 3307.815, 3325.575),  # <PERSON><PERSON><PERSON> - to<PERSON><PERSON>na?
    ("2025.07.30", 3326.498, 3333.815, 3267.955, 3274.775),  # <PERSON><PERSON><PERSON>
    ("2025.07.31", 3275.298, 3314.705, 3273.505, 3289.275),  # Zelená - ukončila sekvenci?
    ("2025.08.01", 3289.665, 3363.425, 3281.435, 3362.425),  # Zelená
]

def analyze_candles():
    """Analyzuje svíčky a určí barvy"""
    print("🔍 ANALÝZA SVÍČEK:")
    print("=" * 80)
    
    for i, (date, o, h, l, c) in enumerate(test_data):
        is_green = c >= o
        color = "🟢 ZELENÁ" if is_green else "🔴 ČERVENÁ"
        range_val = h - l
        print(f"{i+1:2d}. {date}: O={o:7.3f} H={h:7.3f} L={l:7.3f} C={c:7.3f} R={range_val:5.1f} → {color}")
    
    print()

def test_tolerant_logic():
    """Testuje Tolerant logiku pro SHORT sekvenci"""
    print("🧪 TEST TOLERANT LOGIKY - SHORT SEKVENCE:")
    print("=" * 80)
    
    # Simulace Tolerant logiky
    on = False
    dir_bull = None
    order = 0
    last_dir_idx = None
    
    results = []
    
    for i, (date, o, h, l, c) in enumerate(test_data):
        day_bull = c >= o
        series_order = 0
        tolerated = False
        
        if on:
            if not dir_bull:  # DOWN sekvence
                if not day_bull:
                    # Červená svíčka v DOWN sekvenci - pokračuje
                    order += 1
                    last_dir_idx = i
                    series_order = order
                    print(f"  {i+1:2d}. {date}: 🔴 DOWN sekvence pokračuje (order={order})")
                else:
                    # Zelená svíčka v DOWN sekvenci - kontrola tolerance
                    if last_dir_idx is not None:
                        last_high = test_data[last_dir_idx][2]  # High předchozí červené (index 2 = High)
                        current_close = c
                        
                        print(f"  {i+1:2d}. {date}: 🟢 Zelená v DOWN sekvenci")
                        print(f"      Předchozí červená High: {last_high:.3f}")
                        print(f"      Aktuální zelená Close: {current_close:.3f}")
                        print(f"      Kontrola: {current_close:.3f} <= {last_high:.3f} ?")
                        
                        if current_close <= last_high:
                            # Tolerováno
                            order += 1
                            series_order = order
                            tolerated = True
                            print(f"      ✅ TOLEROVÁNO - sekvence pokračuje (order={order})")
                        else:
                            # Ukončeno
                            on = False
                            dir_bull = None
                            order = 0
                            last_dir_idx = None
                            print(f"      ❌ UKONČENO - prolomila předchozí high")
                    else:
                        on = False
                        dir_bull = None
                        order = 0
                        last_dir_idx = None
                        print(f"  {i+1:2d}. {date}: 🟢 Ukončeno (no last_dir_idx)")
        else:
            # Žádná sekvence - hledáme začátek
            if not day_bull:
                print(f"  {i+1:2d}. {date}: 🔴 Možný začátek DOWN sekvence")
                # V reálné implementaci by se kontrolovalo is_marubozu
                # Pro test předpokládáme, že 23.7. je Marubozu
                if date == "2025.07.23":
                    on = True
                    dir_bull = False
                    order = 1
                    last_dir_idx = i
                    series_order = 1
                    print(f"      ✅ Začátek DOWN sekvence (předpokládáme Marubozu)")
            else:
                print(f"  {i+1:2d}. {date}: 🟢 Zelená - žádná sekvence")
        
        results.append({
            'date': date,
            'color': '🟢' if day_bull else '🔴',
            'on': on,
            'dir_bull': dir_bull,
            'series_order': series_order,
            'tolerated': tolerated
        })
    
    return results

def main():
    print("🎯 TEST SHORT SEKVENCE - Scénář 23.7.2025 - 31.7.2025")
    print("=" * 80)
    print()
    
    analyze_candles()
    results = test_tolerant_logic()
    
    print()
    print("📊 SHRNUTÍ VÝSLEDKŮ:")
    print("=" * 80)
    
    for r in results:
        status = f"Order={r['series_order']}" if r['series_order'] > 0 else "Mimo sekvenci"
        tolerance = " (TOLEROVÁNO)" if r['tolerated'] else ""
        direction = "DOWN" if r['on'] and not r['dir_bull'] else "UP" if r['on'] and r['dir_bull'] else "NONE"
        print(f"{r['date']}: {r['color']} - {direction} {status}{tolerance}")
    
    print()
    print("🔍 KLÍČOVÉ KONTROLY:")
    
    # 29.7. - zelená v DOWN sekvenci
    print("Zelená svíčka 29.7.2025:")
    print(f"  Close: 3325.575")
    print(f"  Předchozí červená (28.7.) High: 3345.145")
    print(f"  Kontrola: 3325.575 <= 3345.145 ? → {'✅ ANO' if 3325.575 <= 3345.145 else '❌ NE'}")
    
    # 31.7. - zelená v DOWN sekvenci
    print("\nZelená svíčka 31.7.2025:")
    print(f"  Close: 3289.275")
    print(f"  Předchozí červená (30.7.) High: 3333.815")
    print(f"  Kontrola: 3289.275 <= 3333.815 ? → {'✅ ANO' if 3289.275 <= 3333.815 else '❌ NE'}")
    
    print()
    print("🎯 ZÁVĚR:")
    print("Pokud obě zelené svíčky (29.7. a 31.7.) byly tolerovány,")
    print("pak sekvence skončila kvůli SEQ_END (konec sekvence), ne kvůli prolomení tolerance.")

if __name__ == "__main__":
    main()
