'use client';

import { useState } from 'react';
import TestForm from '@/components/TestForm';
import ResultsDisplay from '@/components/ResultsDisplay';
import { TestParams, TestResult } from '@/types';

export default function Home() {
  const [results, setResults] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [lastExcelFile, setLastExcelFile] = useState<string>('');
  const [showDownload, setShowDownload] = useState<boolean>(false);
  const [showHeatmap, setShowHeatmap] = useState<boolean>(false);

  const handleTestSubmit = async (params: TestParams) => {
    setLoading(true);
    setError('');
    setResults('Spouštím test...\n');
    setShowDownload(false);
    setShowHeatmap(false);

    try {
      const endpoint = params.testMode === 'GRID' ? '/api/grid-test' : '/api/run-test';
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const data: TestResult = await response.json();

      if (data.success) {
        setResults(data.results || '');
        if (data.excel_file) {
          setLastExcelFile(data.excel_file);
          setShowDownload(true);
          if (params.testMode === 'GRID') {
            setShowHeatmap(true);
          }
        }
        setError('');
      } else {
        setError(data.error || 'Neznámá chyba');
        setResults('');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Chyba při komunikaci se serverem');
      setResults('');
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setResults('Výsledky vymazány.\n\nPřipraven k testování...');
    setError('');
    setShowDownload(false);
    setShowHeatmap(false);
    setLastExcelFile('');
  };

  const handleDownload = () => {
    if (lastExcelFile) {
      window.open(`/api/download/${lastExcelFile}`, '_blank');
    } else {
      alert('Žádný Excel soubor k stažení!');
    }
  };

  const handleHeatmap = async () => {
    if (!lastExcelFile) {
      alert('Nejdříve spusťte GRID TEST!');
      return;
    }

    try {
      const response = await fetch('/api/generate-heatmap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ excel_file: lastExcelFile }),
      });

      const data = await response.json();

      if (data.success) {
        alert('HeatMap vygenerována: ' + data.heatmap_file);
        window.open(`/api/download/${data.heatmap_file}`, '_blank');
      } else {
        alert('Chyba při generování HeatMap: ' + data.error);
      }
    } catch (error) {
      alert('Chyba při generování HeatMap: ' + error);
    }
  };

  return (
    <main className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="space-y-8">
          <TestForm
            onSubmit={handleTestSubmit}
            loading={loading}
            onClear={handleClear}
            onDownload={handleDownload}
            onHeatmap={handleHeatmap}
            showDownload={showDownload}
            showHeatmap={showHeatmap}
          />
          
          <ResultsDisplay
            results={results}
            loading={loading}
            error={error}
          />
        </div>
      </div>
    </main>
  );
}
