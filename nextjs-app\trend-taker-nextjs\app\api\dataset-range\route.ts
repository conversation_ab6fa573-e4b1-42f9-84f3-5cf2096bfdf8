import { NextRequest, NextResponse } from 'next/server';
import { getDataFileInfo, getDataFilePath, validateD1DataFile } from '@/lib/dataUtils';

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const filename = searchParams.get('filename');

    if (!filename) {
      return NextResponse.json({
        success: false,
        error: 'Název souboru není specifikován. Použijte ?filename=nazev_souboru.csv'
      });
    }

    const dataFilePath = getDataFilePath(filename);

    // Validace D1 souboru
    const validation = validateD1DataFile(dataFilePath);
    if (!validation.isValid) {
      return NextResponse.json({
        success: false,
        error: `Neplatný D1 datový soubor: ${validation.error}`
      });
    }

    // Získá informace o datovém souboru
    const fileInfo = await getDataFileInfo(dataFilePath);

    if (!fileInfo.success) {
      return NextResponse.json({
        success: false,
        error: fileInfo.error || 'Chyba při načítání informací o souboru'
      });
    }

    return NextResponse.json({
      success: true,
      start_date: fileInfo.startDate,
      end_date: fileInfo.endDate,
      total_records: fileInfo.totalRecords,
      symbol: fileInfo.symbol,
      filename: filename,
      file_path: dataFilePath
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
