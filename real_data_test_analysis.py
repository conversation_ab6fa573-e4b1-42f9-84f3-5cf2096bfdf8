#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analýza testu na reálných datech 19.08.2025 - 18.09.2025
O<PERSON><PERSON><PERSON><PERSON><PERSON>, že všechny opravy fungují správně
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def analyze_real_data_test():
    """Analýza testu na reálných datech"""
    
    print("📊 ANALÝZA TESTU NA REÁLNÝCH DATECH")
    print("=" * 80)
    print("Období: 19.08.2025 - 18.09.2025")
    print("Varianta: Tolerant")
    print("Overnight Mode: LONG-FLAT-LIMIT")
    print("Buy Limit offset: 5 bodů")
    print()
    
    # Kontrola existence Excel souboru
    excel_file = "TrendTaker_WebGUI_Tolerant_2025-08-17_2025-09-18.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel soubor nenalezen: {excel_file}")
        print("🔧 Spusťte nejdříve test ve WebGUI")
        return
    
    print(f"✅ Excel soubor nalezen: {excel_file}")
    print()
    
    try:
        # Načtení Excel souborů
        print("📋 NAČÍTÁNÍ EXCEL SOUBORŮ:")
        print("=" * 80)
        
        # Načtení všech sheetů
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        print(f"📊 Nalezené sheety:")
        for sheet_name in excel_data.keys():
            rows = len(excel_data[sheet_name])
            print(f"   • {sheet_name}: {rows} řádků")
        print()
        
        # Analýza obchodů
        if 'Obchody' in excel_data:
            trades_df = excel_data['Obchody']
            print("💹 ANALÝZA OBCHODŮ:")
            print("=" * 80)
            
            if len(trades_df) == 0:
                print("❌ Žádné obchody nenalezeny!")
                print("🔧 Možné příčiny:")
                print("   • Žádné sekvence v daném období")
                print("   • Chyba v position sizing")
                print("   • Problém s LONG-FLAT-LIMIT logikou")
                return
            
            print(f"📊 Celkem obchodů: {len(trades_df)}")
            print()
            
            # Detailní analýza každého obchodu
            total_pnl = 0
            long_trades = 0
            short_trades = 0
            
            for i, trade in trades_df.iterrows():
                print(f"🔹 OBCHOD {i+1}:")
                print(f"   Sequence_ID: {trade.get('Sequence_ID', 'N/A')}")
                print(f"   Směr: {trade.get('Direction', 'N/A')}")
                print(f"   Období: {trade.get('Start_Date', 'N/A')} - {trade.get('End_Date', 'N/A')}")
                print(f"   Entry: {trade.get('Entry_Price', 0):.2f}")
                print(f"   Exit: {trade.get('Exit_Price', 0):.2f}")
                print(f"   SL: {trade.get('Stop_Loss', 0):.2f}")
                print(f"   TP: {trade.get('Take_Profit', 0):.2f}")
                print(f"   Exit Reason: {trade.get('Exit_Reason', 'N/A')}")
                print(f"   P&L: ${trade.get('Net_PnL', 0):.2f}")
                print(f"   Segments: {trade.get('Segments_Count', 'N/A')}")
                print(f"   Swaps: ${trade.get('Swaps_USD', 0):.2f}")
                
                # Kontrola LONG-FLAT-LIMIT specifik
                if trade.get('Direction') == 'Long':
                    long_trades += 1
                    if trade.get('Swaps_USD', 0) != 0:
                        print(f"   ⚠️  PROBLÉM: Long pozice má swaps {trade.get('Swaps_USD', 0):.2f} (mělo by být 0.0)")
                    else:
                        print(f"   ✅ Swaps správně 0.0 pro Long pozici")
                    
                    # Kontrola Exit_Reason pro LONG-FLAT-LIMIT
                    exit_reason = trade.get('Exit_Reason', '')
                    if 'BUY_LIMIT' in exit_reason:
                        print(f"   ✅ Exit_Reason obsahuje LONG-FLAT-LIMIT info")
                    elif exit_reason in ['SEQ_END', 'TP', 'SL']:
                        print(f"   ✅ Standardní exit reason: {exit_reason}")
                    else:
                        print(f"   ⚠️  Neočekávaný exit reason: {exit_reason}")
                
                elif trade.get('Direction') == 'SHORT':
                    short_trades += 1
                    print(f"   ✅ SHORT pozice se standardními swapy")
                
                total_pnl += trade.get('Net_PnL', 0)
                print()
            
            print(f"📊 CELKOVÉ STATISTIKY:")
            print(f"   Long obchody: {long_trades}")
            print(f"   Short obchody: {short_trades}")
            print(f"   Celkový P&L: ${total_pnl:.2f}")
            print()
        
        # Analýza detailních segmentů
        if 'Detailní_Segmenty' in excel_data:
            segments_df = excel_data['Detailní_Segmenty']
            print("🔍 ANALÝZA DETAILNÍCH SEGMENTŮ:")
            print("=" * 80)
            
            if len(segments_df) == 0:
                print("❌ Žádné detailní segmenty nenalezeny!")
                print("🔧 Možná příčina: Žádné LONG-FLAT-LIMIT obchody")
            else:
                print(f"📊 Celkem segmentů: {len(segments_df)}")
                print()
                
                # Analýza podle sekvencí
                sequences = segments_df['Sequence_ID'].unique()
                for seq_id in sequences:
                    seq_segments = segments_df[segments_df['Sequence_ID'] == seq_id]
                    print(f"🔹 {seq_id}:")
                    print(f"   Segmentů: {len(seq_segments)}")
                    
                    total_seq_pnl = seq_segments['PnL_Bod'].sum()
                    print(f"   Celkový P&L: {total_seq_pnl:.2f} bodů")
                    
                    # Detaily segmentů
                    for _, seg in seq_segments.iterrows():
                        print(f"     {seg.get('Date', 'N/A')}: {seg.get('Entry_Price', 0):.2f} → {seg.get('Exit_Price', 0):.2f} "
                              f"({seg.get('PnL_Bod', 0):+.2f} bodů, {seg.get('Exit_Reason', 'N/A')})")
                    print()
        else:
            print("⚠️  Sheet 'Detailní_Segmenty' nenalezen")
            print("🔧 Možná příčina: Žádné LONG-FLAT-LIMIT obchody s více segmenty")
            print()
        
        # Analýza sekvencí
        if 'Sekvence' in excel_data:
            sequences_df = excel_data['Sekvence']
            print("📈 ANALÝZA SEKVENCÍ:")
            print("=" * 80)
            
            print(f"📊 Celkem sekvencí: {len(sequences_df)}")
            
            up_sequences = len(sequences_df[sequences_df['Direction'] == 'Up'])
            down_sequences = len(sequences_df[sequences_df['Direction'] == 'Down'])
            
            print(f"   UP sekvence: {up_sequences}")
            print(f"   DOWN sekvence: {down_sequences}")
            print()
            
            # Detaily sekvencí
            for _, seq in sequences_df.iterrows():
                print(f"🔹 {seq.get('Direction', 'N/A')} sekvence:")
                print(f"   Období: {seq.get('Start_Date', 'N/A')} - {seq.get('End_Date', 'N/A')}")
                print(f"   Délka: {seq.get('Length', 'N/A')} dní")
                print()
        
        # Kontrola parametrů
        if 'Parametry' in excel_data:
            params_df = excel_data['Parametry']
            print("⚙️  KONTROLA PARAMETRŮ:")
            print("=" * 80)
            
            for _, param in params_df.iterrows():
                param_name = param.get('Parametr', 'N/A')
                param_value = param.get('Hodnota', 'N/A')
                print(f"   {param_name}: {param_value}")
                
                # Kontrola klíčových parametrů
                if param_name == 'Overnight Mode' and param_value != 'LONG-FLAT-LIMIT':
                    print(f"   ⚠️  Očekáván LONG-FLAT-LIMIT, nalezen: {param_value}")
                elif param_name == 'Buy Limit offset (body)' and param_value != 5:
                    print(f"   ⚠️  Očekáván offset 5, nalezen: {param_value}")
            print()
        
        # Finální hodnocení
        print("🎯 FINÁLNÍ HODNOCENÍ:")
        print("=" * 80)
        
        success_count = 0
        total_checks = 6
        
        # Check 1: Excel soubor existuje
        print("✅ 1. Excel soubor vytvořen a stažen")
        success_count += 1
        
        # Check 2: Obchody existují
        if 'Obchody' in excel_data and len(excel_data['Obchody']) > 0:
            print("✅ 2. Obchody nalezeny v Excel")
            success_count += 1
        else:
            print("❌ 2. Žádné obchody v Excel")
        
        # Check 3: Sequence_ID přítomno
        if 'Obchody' in excel_data and 'Sequence_ID' in excel_data['Obchody'].columns:
            print("✅ 3. Sequence_ID implementováno")
            success_count += 1
        else:
            print("❌ 3. Sequence_ID chybí")
        
        # Check 4: LONG pozice mají swaps = 0
        if 'Obchody' in excel_data:
            long_trades_df = excel_data['Obchody'][excel_data['Obchody']['Direction'] == 'Long']
            if len(long_trades_df) > 0:
                zero_swaps = (long_trades_df['Swaps_USD'] == 0).all()
                if zero_swaps:
                    print("✅ 4. Long pozice mají swaps = 0.0")
                    success_count += 1
                else:
                    print("❌ 4. Long pozice mají nenulové swaps")
            else:
                print("⚠️  4. Žádné Long pozice k ověření")
        
        # Check 5: Detailní segmenty (pokud existují LONG obchody)
        if 'Detailní_Segmenty' in excel_data and len(excel_data['Detailní_Segmenty']) > 0:
            print("✅ 5. Detailní segmenty implementovány")
            success_count += 1
        elif 'Obchody' in excel_data and len(excel_data['Obchody'][excel_data['Obchody']['Direction'] == 'Long']) == 0:
            print("⚠️  5. Žádné Long obchody = žádné detailní segmenty (OK)")
            success_count += 1
        else:
            print("❌ 5. Detailní segmenty chybí")
        
        # Check 6: Parametry správně nastaveny
        if 'Parametry' in excel_data:
            params_dict = dict(zip(excel_data['Parametry']['Parametr'], excel_data['Parametry']['Hodnota']))
            if params_dict.get('Overnight Mode') == 'LONG-FLAT-LIMIT':
                print("✅ 6. Parametry správně nastaveny")
                success_count += 1
            else:
                print("❌ 6. Parametry nesprávně nastaveny")
        
        print()
        print(f"📊 CELKOVÉ SKÓRE: {success_count}/{total_checks} ({success_count/total_checks*100:.0f}%)")
        
        if success_count == total_checks:
            print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
            print("✅ LONG-FLAT-LIMIT implementace je plně funkční")
        elif success_count >= total_checks * 0.8:
            print("✅ VĚTŠINA TESTŮ ÚSPĚŠNÁ")
            print("⚠️  Drobné problémy k dořešení")
        else:
            print("❌ VÝZNAMNÉ PROBLÉMY NALEZENY")
            print("🔧 Nutné další opravy")
    
    except Exception as e:
        print(f"❌ Chyba při analýze Excel souboru: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_real_data_test()
