import { NextRequest, NextResponse } from 'next/server';
import { TrendTakerEngine } from '@/lib/trendTaker';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const params = await request.json();
    
    const {
      dataFile,
      dataRange = 'CUSTOM',
      startDate = '2020-01-01',
      endDate = '2025-09-05',
      startEquity = 10000,
      riskPct = 2.0
    } = params;

    if (!dataFile) {
      return NextResponse.json({
        success: false,
        error: 'Datový soubor není specifikován'
      });
    }

    const results: string[] = [];
    results.push('🔥 Spouštím GRID TEST...\n');
    
    // RRR grid: 5.0..8.0 step 0.5
    const RRR_values = [];
    for (let rrr = 5.0; rrr <= 8.0; rrr += 0.5) {
      RRR_values.push(Math.round(rrr * 10) / 10);
    }
    
    const variants = ['Strict', 'Tolerant'];
    const exitPolicies = ['Optimistic', 'Conservative'];
    const overnightModes = ['STANDARD', 'LONG-FLAT-LIMIT'];
    
    results.push(`📊 Grid parametry:\n`);
    results.push(`   • RRR hodnoty: ${RRR_values.join(', ')}\n`);
    results.push(`   • Varianty: ${variants.join(', ')}\n`);
    results.push(`   • Exit Policy: ${exitPolicies.join(', ')}\n`);
    results.push(`   • Overnight Mode: ${overnightModes.join(', ')}\n`);
    results.push(`   • Období: ${startDate} - ${endDate}\n`);
    results.push(`   • Kapitál: $${startEquity}\n`);
    results.push(`   • Risk: ${riskPct}%\n\n`);

    // Inicializace engine
    const engine = new TrendTakerEngine();
    
    // Načtení dat z C:\Temp\Trading\DATA ICM
    results.push('📈 Načítám D1 data...\n');

    const candleData = await engine.loadData(dataFile);
    results.push(`✅ Načteno ${candleData.length} barů\n`);

    // CRITICAL FIX: Výpočet ATR a Marubozu na CELÉM datasetu (jako Python WebGUI)
    results.push('🔍 Počítám ATR a detekuji Marubozu na celém datasetu...\n');
    const processedDataAll = engine.calculateATRAndMarubozu(candleData);

    // CRITICAL FIX: Sekvenční logika na celém datasetu (jako Python WebGUI)
    results.push('🔄 Aplikuji sekvenční logiku na celém datasetu...\n');
    const daysStrictAll = engine.buildDaysStrict(processedDataAll);
    const daysTolerantAll = engine.buildDaysTolerant(processedDataAll);

    // Filtrace podle datumů AŽ PO výpočtu ATR a sekvencí
    let daysStrict = daysStrictAll;
    let daysTolerant = daysTolerantAll;

    if (dataRange === 'CUSTOM') {
      const startDt = new Date(startDate);
      const endDt = new Date(endDate);
      daysStrict = daysStrictAll.filter(day => day.Date >= startDt && day.Date <= endDt);
      daysTolerant = daysTolerantAll.filter(day => day.Date >= startDt && day.Date <= endDt);
      results.push(`📅 Filtrováno na ${daysStrict.length} barů pro období ${startDate} - ${endDate}\n`);
    } else {
      results.push(`📅 Použit celý dataset: ${daysStrict.length} barů\n`);
    }

    if (daysStrict.length === 0) {
      throw new Error('Žádná data v zadaném období!');
    }
    
    // Vytvoření sekvencí
    results.push('📊 Vytvářím sekvence...\n');
    const seqsStrict = engine.buildSequences(daysStrict);
    const seqsTolerant = engine.buildSequences(daysTolerant);
    
    results.push(`📊 Sekvence Strict: ${seqsStrict.length}\n`);
    results.push(`📊 Sekvence Tolerant: ${seqsTolerant.length}\n\n`);

    // Spuštění všech kombinací
    const allResults: any[] = [];
    const totalCombinations = RRR_values.length * variants.length * exitPolicies.length * overnightModes.length;
    let current = 0;
    
    results.push(`🚀 Spouštím ${totalCombinations} kombinací...\n\n`);

    for (const variant of variants) {
      const daysData = variant === 'Strict' ? daysStrict : daysTolerant;
      const sequences = variant === 'Strict' ? seqsStrict : seqsTolerant;
      
      for (const rrr of RRR_values) {
        for (const exitPolicy of exitPolicies) {
          for (const overnightMode of overnightModes) {
            current++;
            
            try {
              // Simulace obchodů
              const trades = engine.simulateTrades(
                daysData,
                sequences,
                rrr,
                exitPolicy,
                startEquity,
                riskPct / 100,
                overnightMode
              );
              
              // Výpočet statistik
              const finalEquity = trades.length > 0 ? trades[trades.length - 1].Equity_After : startEquity;
              const totalPnL = finalEquity - startEquity;
              const winTrades = trades.filter(t => t.Net_PnL > 0).length;
              const lossTrades = trades.filter(t => t.Net_PnL < 0).length;
              const winRate = trades.length > 0 ? (winTrades / trades.length * 100) : 0;
              
              const grossProfit = trades.filter(t => t.Net_PnL > 0).reduce((sum, t) => sum + t.Net_PnL, 0);
              const grossLoss = Math.abs(trades.filter(t => t.Net_PnL < 0).reduce((sum, t) => sum + t.Net_PnL, 0));
              const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : (grossProfit > 0 ? 999 : 0);
              
              allResults.push({
                Variant: variant,
                RRR: rrr,
                Exit_Policy: exitPolicy,
                Overnight_Mode: overnightMode,
                Trades_Count: trades.length,
                Win_Trades: winTrades,
                Loss_Trades: lossTrades,
                Win_Rate: winRate,
                Net_Profit: totalPnL,
                Gross_Profit: grossProfit,
                Gross_Loss: grossLoss,
                Profit_Factor: profitFactor,
                Final_Equity: finalEquity
              });
              
              results.push(`✅ ${current}/${totalCombinations}: ${variant} RRR=${rrr} ${exitPolicy} ${overnightMode} → P&L: $${totalPnL.toFixed(2)}\n`);
              
            } catch (error) {
              results.push(`❌ ${current}/${totalCombinations}: ${variant} RRR=${rrr} ${exitPolicy} ${overnightMode} → CHYBA: ${error}\n`);
            }
          }
        }
      }
    }

    // Uložení do Excel
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const excelFilename = `GridTest_Results_${timestamp}.xlsx`;
    
    results.push('\n💾 Ukládám výsledky do Excel...\n');
    const excelCreated = await engine.saveGridTestToExcel(allResults, params, excelFilename);
    
    if (excelCreated) {
      results.push(`✅ Excel soubor vytvořen: ${excelFilename}\n`);
    }

    // Souhrn nejlepších výsledků
    if (allResults.length > 0) {
      const sortedByProfit = [...allResults].sort((a, b) => b.Net_Profit - a.Net_Profit);
      const sortedByProfitFactor = [...allResults].sort((a, b) => b.Profit_Factor - a.Profit_Factor);
      
      results.push('\n🏆 TOP 3 podle Net Profit:\n');
      for (let i = 0; i < Math.min(3, sortedByProfit.length); i++) {
        const r = sortedByProfit[i];
        results.push(`${i+1}. ${r.Variant} RRR=${r.RRR} ${r.Exit_Policy} ${r.Overnight_Mode} → $${r.Net_Profit.toFixed(2)}\n`);
      }
      
      results.push('\n🏆 TOP 3 podle Profit Factor:\n');
      for (let i = 0; i < Math.min(3, sortedByProfitFactor.length); i++) {
        const r = sortedByProfitFactor[i];
        results.push(`${i+1}. ${r.Variant} RRR=${r.RRR} ${r.Exit_Policy} ${r.Overnight_Mode} → PF=${r.Profit_Factor.toFixed(2)}\n`);
      }
    }

    return NextResponse.json({
      success: true,
      results: results.join(''),
      excel_file: excelCreated ? excelFilename : undefined
    });

  } catch (error) {
    console.error('Error in grid-test:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Neznámá chyba'
    });
  }
}
