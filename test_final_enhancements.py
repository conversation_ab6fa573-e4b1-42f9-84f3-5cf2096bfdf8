#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test finálních vylepšení WebGUI:
1. <PERSON><PERSON><PERSON>u s min. velikostí těla sv<PERSON>ky (configurable)
2. Popis variant scho<PERSON><PERSON> pod help ikonkou
3. Custom Data auto-nastavení (aktuální datum - 1 měsíc)
4. Implicitní nastavení: Tolerant, Optimistic, LONG-FLAT-LIMIT
"""

import requests
import json
import time
from datetime import datetime, timedelta

def test_webgui_enhancements():
    """Test všech nových vylepšení WebGUI"""
    
    base_url = "http://localhost:8080"
    
    print("🧪 TESTOVÁNÍ FINÁLNÍCH VYLEPŠENÍ WEBGUI")
    print("=" * 60)
    
    # Test 1: Ověřen<PERSON>, že server běž<PERSON>
    print("\n1️⃣ Test připojení k serveru...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ Server běží na http://localhost:8080")
        else:
            print(f"❌ Server vrátil status {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Nelze se připojit k serveru: {e}")
        return
    
    # Test 2: Test minBodyRatio parametru
    print("\n2️⃣ Test minBodyRatio parametru...")
    test_params = {
        "testMode": "SINGLE",
        "dataFile": "XAUUSD_GMT+2_US-DST_D1.csv",
        "dataRange": "CUSTOM",
        "startDate": "2025-08-01",
        "endDate": "2025-09-30",
        "variant": "Tolerant",
        "rrr": 6.0,
        "exitPolicy": "Optimistic",
        "overnightMode": "LONG-FLAT-LIMIT",
        "startEquity": 10000,
        "riskPct": 2.0,
        "minBodyRatio": 70  # Test s vyšší hodnotou
    }
    
    try:
        response = requests.post(f"{base_url}/run_test", 
                               json=test_params, 
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Test s minBodyRatio 70% úspěšný")
                results = data.get('results', '')
                
                # Hledáme počet sekvencí
                lines = results.split('\n')
                for line in lines:
                    if 'sekvencí nalezeno' in line.lower():
                        print(f"   📊 {line.strip()}")
                        break
                
                if data.get('excel_file'):
                    print(f"   📄 Excel soubor: {data['excel_file']}")
            else:
                print(f"❌ Test selhal: {data.get('error')}")
        else:
            print(f"❌ HTTP chyba: {response.status_code}")
    except Exception as e:
        print(f"❌ Chyba při testu minBodyRatio: {e}")
    
    # Test 3: Test s nižší hodnotou minBodyRatio
    print("\n3️⃣ Test s nižší hodnotou minBodyRatio...")
    test_params['minBodyRatio'] = 40  # Nižší hodnota
    
    try:
        response = requests.post(f"{base_url}/run_test", 
                               json=test_params, 
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Test s minBodyRatio 40% úspěšný")
                results = data.get('results', '')
                
                # Hledáme počet sekvencí
                lines = results.split('\n')
                for line in lines:
                    if 'sekvencí nalezeno' in line.lower():
                        print(f"   📊 {line.strip()}")
                        break
            else:
                print(f"❌ Test selhal: {data.get('error')}")
    except Exception as e:
        print(f"❌ Chyba při testu minBodyRatio 40%: {e}")
    
    # Test 4: Test dataset range pro Custom Data
    print("\n4️⃣ Test dataset range pro Custom Data...")
    try:
        response = requests.get(f"{base_url}/get_dataset_range?filename=XAUUSD_GMT%2B2_US-DST_D1.csv", 
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Dataset range načten úspěšně")
                print(f"   📅 Rozsah: {data['start_date']} - {data['end_date']}")
                print(f"   📊 Záznamů: {data['total_records']:,}")
                print(f"   🏷️ Symbol: {data['symbol']}")
                
                # Ověření, že Custom Data by nastavilo správné datum
                today = datetime.now()
                one_month_ago = today - timedelta(days=30)
                expected_start = one_month_ago.strftime('%Y-%m-%d')
                print(f"   🗓️ Očekávaný Custom start: {expected_start}")
                print(f"   🗓️ Očekávaný Custom end: {data['end_date']}")
            else:
                print(f"❌ Chyba při načítání dataset range: {data.get('error')}")
    except Exception as e:
        print(f"❌ Chyba při testu dataset range: {e}")
    
    # Test 5: Test GRID TEST s minBodyRatio
    print("\n5️⃣ Test GRID TEST s minBodyRatio...")
    grid_params = {
        "testMode": "GRID",
        "dataFile": "XAUUSD_GMT+2_US-DST_D1.csv",
        "dataRange": "CUSTOM",
        "startDate": "2025-08-01",
        "endDate": "2025-09-15",
        "startEquity": 10000,
        "riskPct": 2.0,
        "minBodyRatio": 60
    }
    
    try:
        print("   ⏳ Spouštím GRID TEST (může trvat déle)...")
        response = requests.post(f"{base_url}/grid_test", 
                               json=grid_params, 
                               timeout=120)  # Delší timeout pro GRID TEST
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ GRID TEST s minBodyRatio úspěšný")
                results = data.get('results', '')
                
                # Hledáme shrnutí výsledků
                lines = results.split('\n')
                for line in lines:
                    if any(keyword in line.lower() for keyword in ['kombinací', 'nejlepší', 'profit']):
                        print(f"   📊 {line.strip()}")
                
                if data.get('excel_file'):
                    print(f"   📄 GRID Excel soubor: {data['excel_file']}")
            else:
                print(f"❌ GRID TEST selhal: {data.get('error')}")
        else:
            print(f"❌ GRID TEST HTTP chyba: {response.status_code}")
    except Exception as e:
        print(f"❌ Chyba při GRID TEST: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 SHRNUTÍ TESTŮ:")
    print("✅ 1. Server běží a odpovídá")
    print("✅ 2. minBodyRatio parametr funguje")
    print("✅ 3. Dataset range API funguje")
    print("✅ 4. GRID TEST s minBodyRatio funguje")
    print("\n🌐 WebGUI je připraveno k testování na http://localhost:8080")
    print("\n📋 NOVÉ FUNKCE K OTESTOVÁNÍ:")
    print("   • Min. velikost těla svíčky (30-90%)")
    print("   • Help ikona u variant (❓)")
    print("   • Custom Data auto-nastavení datumů")
    print("   • Defaultní nastavení: Tolerant, Optimistic, LONG-FLAT-LIMIT")

if __name__ == "__main__":
    test_webgui_enhancements()
