# 🐍 Trend Taker - Python Console

Konzolová verze Trend Taker aplikace pro testování Marubozu sekvenční strategie.

## 📋 Popis

Tato verze poskytuje přímé spuštění testů z příkazové řádky s možností konfigurace parametrů přímo v kódu.

## 🚀 Spuštění

```bash
python "Trend Taker Test 1.0.py"
```

## 📊 Funkce

- ✅ Single test s konfigurovatelními parametry
- ✅ Marubozu detekce s ATR filtrem
- ✅ Strict a Tolerant sekvenční logika
- ✅ Position sizing podle risk managementu
- ✅ Swap kalkulace pro různé overnight módy
- ✅ Excel export výsledků
- ✅ Detailní konzolový výstup

## ⚙️ Konfigurace

Parametry se nastavují přímo v kódu:

```python
# Hlavní parametry
VARIANT = "Strict"  # nebo "Tolerant"
RRR = 6.5
EXIT_POLICY = "Optimistic"  # nebo "Conservative"
OVERNIGHT_MODE = "STANDARD"  # nebo "LONG-FLAT-LIMIT"
START_EQUITY = 10000
RISK_PCT = 0.02  # 2%
```

## 📁 Požadavky

- Python 3.7+
- pandas
- numpy  
- openpyxl (pro Excel export)
- Datový soubor: `XAUUSD_GMT+2_US-DST_D1.csv`

## 📈 Výstup

- Konzolový výpis s detailními statistikami
- Excel soubor s výsledky: `TrendTaker_Console_YYYY-MM-DD.xlsx`

### Excel obsahuje:
- **Obchody** - Seznam všech obchodů
- **Denní data** - Analýza po dnech
- **Sekvence** - Nalezené sekvence
- **Parametry** - Nastavení testu

## 🎯 Použití

Ideální pro:
- Rychlé testování parametrů
- Automatizované skripty
- Batch processing
- Vývoj a debugging

## 📝 Poznámky

- Parametry se mění přímo v kódu
- Výsledky jsou kompatibilní s ostatními verzemi
- Nejrychlejší spuštění ze všech verzí
