#!/bin/bash

echo "================================================================================"
echo "🎯 TREND TAKER TEST 1.0 - Next.js Web GUI"
echo "================================================================================"
echo

# Kontrola Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js není nainstalován!"
    echo "📥 Stáhněte a nainstalujte Node.js z https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js je dostupný ($(node --version))"
echo

# Kontrola datového souboru
if [ ! -f "XAUUSD_GMT+2_US-DST_D1.csv" ]; then
    echo "❌ Datový soubor XAUUSD_GMT+2_US-DST_D1.csv nebyl nalezen!"
    echo "📁 Zkopírujte datový soubor do této složky"
    exit 1
fi

echo "✅ Datový soubor nalezen"
echo

# Kontrola node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 Instaluji závislosti..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Chyba při instalaci závislostí!"
        exit 1
    fi
    echo "✅ Závislosti nainstalovány"
    echo
fi

# Vytvoření public složky
if [ ! -d "public" ]; then
    mkdir -p public
    echo "✅ Vytvořena public složka"
fi

echo "🚀 Spouštím Next.js server..."
echo "🌐 Aplikace bude dostupná na: http://localhost:3000"
echo "🛑 Pro ukončení stiskněte Ctrl+C"
echo
echo "================================================================================"

# Spuštění serveru
npm run dev
