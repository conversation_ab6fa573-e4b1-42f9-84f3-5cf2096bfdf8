#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug test pro BUY_LIMIT_NOT_FILLED
"""

import os

def test_buy_limit_debug():
    """Debug test pro BUY_LIMIT_NOT_FILLED"""
    
    print("🔍 DEBUG TEST - BUY_LIMIT_NOT_FILLED")
    print("=" * 80)
    
    # Kontrola Excel souboru
    excel_file = "TrendTaker_WebGUI_Tolerant_2025-08-17_2025-09-18.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel soubor nenalezen: {excel_file}")
        return
    
    print(f"✅ Excel soubor nalezen: {excel_file}")
    
    try:
        import openpyxl
        
        # Načtení Excel souboru
        wb = openpyxl.load_workbook(excel_file)
        
        # Kontrola obchodů
        if 'Obchody' in wb.sheetnames:
            ws = wb['Obchody']
            print(f"\n💹 ANALÝZA OBCHODŮ:")
            
            if ws.max_row > 1:
                print(f"   Celk<PERSON> obchodů: {ws.max_row - 1}")
                
                # Najdeme Exit_Reason sloupec
                exit_reason_col = None
                for col in range(1, ws.max_column + 1):
                    header = ws.cell(row=1, column=col).value
                    if header == 'Exit_Reason':
                        exit_reason_col = col
                        break
                
                if exit_reason_col:
                    print(f"   ✅ Exit_Reason sloupec nalezen na pozici {exit_reason_col}")
                    
                    # Kontrola všech obchodů
                    for row in range(2, ws.max_row + 1):
                        exit_reason = ws.cell(row=row, column=exit_reason_col).value
                        print(f"   Obchod {row-1}: Exit_Reason = '{exit_reason}'")
                        
                        if exit_reason:
                            if 'BUY_LIMIT_NOT_FILLED' in str(exit_reason):
                                print(f"     ✅ BUY_LIMIT_NOT_FILLED nalezen!")
                            elif 'BUY_LIMIT_FILLED' in str(exit_reason):
                                print(f"     ✅ BUY_LIMIT_FILLED nalezen!")
                            elif 'BUY_LIMIT' in str(exit_reason):
                                print(f"     ⚠️  Jiná BUY_LIMIT varianta: {exit_reason}")
                            else:
                                print(f"     ❌ Žádná BUY_LIMIT informace")
                        else:
                            print(f"     ❌ Exit_Reason je prázdný")
                else:
                    print(f"   ❌ Exit_Reason sloupec nenalezen")
            else:
                print(f"   ❌ Žádné obchody v Excel")
        else:
            print(f"   ❌ Sheet 'Obchody' nenalezen")
        
        wb.close()
        
        # Kontrola kódu
        print(f"\n🔍 KONTROLA KÓDU:")
        print("=" * 80)
        
        code_file = "python-webgui/Trend Taker Web GUI.py"
        if os.path.exists(code_file):
            with open(code_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                if 'BUY_LIMIT_NOT_FILLED' in content:
                    print(f"   ✅ BUY_LIMIT_NOT_FILLED nalezen v kódu")
                    
                    # Počet výskytů
                    count = content.count('BUY_LIMIT_NOT_FILLED')
                    print(f"   📊 Počet výskytů: {count}")
                    
                    # Najdeme řádky s BUY_LIMIT_NOT_FILLED
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if 'BUY_LIMIT_NOT_FILLED' in line:
                            print(f"   Řádek {i}: {line.strip()}")
                else:
                    print(f"   ❌ BUY_LIMIT_NOT_FILLED nenalezen v kódu")
                
                if 'BUY_LIMIT_FILLED' in content:
                    print(f"   ✅ BUY_LIMIT_FILLED nalezen v kódu")
                    count = content.count('BUY_LIMIT_FILLED')
                    print(f"   📊 Počet výskytů: {count}")
        else:
            print(f"   ❌ Soubor kódu nenalezen: {code_file}")
        
        # Doporučení
        print(f"\n💡 DOPORUČENÍ:")
        print("=" * 80)
        print("1. 🔄 Spusťte nový test ve WebGUI")
        print("2. 📊 Zkontrolujte, zda se vytváří LONG pozice")
        print("3. 🔍 Ověřte, že Buy Limit se skutečně nenaplňuje")
        print("4. 📝 Zkontrolujte logiku v simulate_long_flat_limit_position")
        
    except Exception as e:
        print(f"❌ Chyba při analýze: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_buy_limit_debug()
