#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test různých RRR scénářů pro LONG sekvenci 20.8.2025 - 3.9.2025
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že WebGUI správně používá zadané RRR, ne hardcoded hodnoty
"""

import pandas as pd
import numpy as np

# Data z obchodu s RRR 10
entry_price = 3349.1
atr10 = 34.66
exit_price_seq_end = 3558.48  # Skutečný exit (SEQ_END)
take_profit_rrr10 = 3695.66  # TP pro RRR 10 z obchodního záznamu

# Testovací data z XAUUSD kolem 20.8.2025 - 5.9.2025
test_data = [
    # Datum, Open, High, Low, Close
    ("2025.08.20", 3315.465, 3350.198, 3311.255, 3347.335),  # <PERSON>elená - za<PERSON><PERSON><PERSON>k LONG
    ("2025.08.21", 3349.098, 3351.935, 3324.948, 3338.935),  # <PERSON>erve<PERSON> - Entry den
    ("2025.08.22", 3338.405, 3378.585, 3321.045, 3371.235),  # <PERSON><PERSON><PERSON>
    ("2025.08.25", 3370.835, 3376.135, 3359.495, 3365.605),  # Červe<PERSON>
    ("2025.08.26", 3365.135, 3393.495, 3351.175, 3393.465),  # Zelená
    ("2025.08.27", 3392.648, 3398.569, 3373.665, 3396.495),  # Zelená
    ("2025.08.28", 3397.835, 3423.069, 3384.445, 3415.795),  # Zelená
    ("2025.08.29", 3416.965, 3453.565, 3404.185, 3446.805),  # Zelená
    ("2025.09.01", 3445.648, 3489.595, 3436.548, 3476.225),  # Zelená
    ("2025.09.02", 3476.505, 3539.849, 3469.805, 3532.405),  # Zelená
    ("2025.09.03", 3532.698, 3578.175, 3525.848, 3558.475),  # Zelená - konec sekvence
    ("2025.09.04", 3560.448, 3563.998, 3510.425, 3544.645),  # Červená
    ("2025.09.05", 3545.935, 3599.905, 3539.715, 3585.195),  # Zelená
]

def verify_rrr_calculation():
    """Ověří správnost RRR kalkulace"""
    print("🔍 OVĚŘENÍ RRR KALKULACE:")
    print("=" * 60)
    
    print(f"Entry Price: {entry_price}")
    print(f"ATR10: {atr10}")
    print(f"Stop Loss: {entry_price - atr10:.2f}")
    print()
    
    # Ověření TP z obchodního záznamu
    calculated_tp_rrr10 = entry_price + (10 * atr10)
    print(f"RRR 10 TP (kalkulace): {calculated_tp_rrr10:.2f}")
    print(f"RRR 10 TP (obchod):    {take_profit_rrr10:.2f}")
    print(f"Rozdíl: {abs(calculated_tp_rrr10 - take_profit_rrr10):.2f}")
    
    if abs(calculated_tp_rrr10 - take_profit_rrr10) < 1.0:
        print("✅ RRR kalkulace je správná!")
    else:
        print("❌ RRR kalkulace nesouhlasí!")
    
    print()

def test_different_rrr_scenarios():
    """Testuje různé RRR scénáře"""
    print("🎯 TEST RŮZNÝCH RRR SCÉNÁŘŮ:")
    print("=" * 60)
    
    # Najdeme nejvyšší High během sekvence
    max_high = 0
    max_high_date = ""
    for date, o, h, l, c in test_data:
        if h > max_high:
            max_high = h
            max_high_date = date
    
    print(f"Nejvyšší High během sekvence: {max_high:.2f} ({max_high_date})")
    print()
    
    # Test různých RRR hodnot
    rrr_scenarios = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 15, 20]
    
    print("RRR | TP Price | Dosaženo? | Exit Reason")
    print("-" * 50)
    
    for rrr in rrr_scenarios:
        tp_price = entry_price + (rrr * atr10)
        
        if max_high >= tp_price:
            # TP bylo dosaženo
            exit_reason = "TP"
            exit_price = tp_price
            status = "✅ TP"
        else:
            # TP nebylo dosaženo - SEQ_END
            exit_reason = "SEQ_END"
            exit_price = exit_price_seq_end
            status = "❌ SEQ_END"
        
        profit = exit_price - entry_price
        profit_usd = profit * 100  # $100 per bod per lot
        
        print(f"{rrr:2d}  | {tp_price:7.2f} | {status:9} | {exit_reason} (${profit_usd:.0f})")

def analyze_optimal_rrr():
    """Analyzuje optimální RRR pro tuto sekvenci"""
    print("\n💡 ANALÝZA OPTIMÁLNÍHO RRR:")
    print("=" * 50)
    
    # Najdeme nejvyšší dosažitelné RRR
    max_high = max(h for _, _, h, _, _ in test_data)
    max_achievable_profit = max_high - entry_price
    max_achievable_rrr = max_achievable_profit / atr10
    
    print(f"Nejvyšší High: {max_high:.2f}")
    print(f"Maximální profit: {max_achievable_profit:.2f} bodů")
    print(f"Maximální RRR: {max_achievable_rrr:.2f}")
    print()
    
    # Optimální RRR (celé číslo)
    optimal_rrr = int(max_achievable_rrr)
    optimal_tp = entry_price + (optimal_rrr * atr10)
    optimal_profit = optimal_tp - entry_price
    optimal_profit_usd = optimal_profit * 100
    
    print(f"Optimální RRR: {optimal_rrr}")
    print(f"Optimální TP: {optimal_tp:.2f}")
    print(f"Optimální profit: ${optimal_profit_usd:.0f}")
    
    # Porovnání se skutečným výsledkem
    actual_profit = exit_price_seq_end - entry_price
    actual_profit_usd = actual_profit * 100
    improvement = optimal_profit_usd / actual_profit_usd
    
    print()
    print(f"Skutečný profit (SEQ_END): ${actual_profit_usd:.0f}")
    print(f"Zlepšení s optimálním RRR: {improvement:.1f}x")

def test_webgui_scenarios():
    """Navrhne testy pro WebGUI"""
    print("\n🌐 DOPORUČENÉ TESTY PRO WEBGUI:")
    print("=" * 50)
    
    print("Pro ověření, že WebGUI správně používá zadané RRR:")
    print()
    
    test_scenarios = [
        (6, "Původní default - měl by skončit SEQ_END"),
        (7, "Vyšší než dosaženo - měl by skončit SEQ_END"),
        (10, "Váš test - měl by skončit SEQ_END (TP nedosaženo)"),
        (5, "Nižší RRR - měl by skončit TP"),
        (4, "Ještě nižší - měl by skončit TP"),
        (1, "Velmi nízký - měl by skončit TP rychle")
    ]
    
    for rrr, description in test_scenarios:
        tp_price = entry_price + (rrr * atr10)
        print(f"RRR {rrr:2d}: TP {tp_price:7.2f} - {description}")
    
    print()
    print("🔧 Postup testování:")
    print("1. Otevřete http://localhost:8080")
    print("2. Nastavte období: 2025-08-20 až 2025-09-05")
    print("3. Vyberte Tolerant variantu")
    print("4. Testujte různé RRR hodnoty")
    print("5. Ověřte Exit_Reason a Exit_Price v výsledcích")

def main():
    print("🎯 TEST RRR SCÉNÁŘŮ - LONG sekvence 20.8.-3.9.2025")
    print("=" * 80)
    print("Ověření správného použití RRR parametru ve WebGUI")
    print()
    
    verify_rrr_calculation()
    test_different_rrr_scenarios()
    analyze_optimal_rrr()
    test_webgui_scenarios()
    
    print(f"\n🎯 ZÁVĚR:")
    print("=" * 30)
    print("• RRR 10 TP (3695.66) NEBYLO dosaženo")
    print("• Sekvence skončila SEQ_END na 3558.48")
    print("• Optimální RRR pro tuto sekvenci: 7")
    print("• WebGUI by měl správně používat zadané RRR")
    print("• Testujte různé RRR hodnoty pro ověření!")

if __name__ == "__main__":
    main()
