#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pro ověření Take Profit kalkulace pro LONG sekvenci 20.8.2025 - 3.9.2025
<PERSON><PERSON><PERSON><PERSON>, zda nebyl dosažen TP s vysokým RRR (10-20)
"""

import pandas as pd
import numpy as np

# Data z obchodu
entry_price = 3349.1  # Entry price z obchodního záznamu
exit_price = 3558.48  # Exit price z obchodního záznamu
atr10 = 34.66  # ATR10 z obchodního záznamu

# Testovací data z XAUUSD kolem 20.8.2025 - 5.9.2025
test_data = [
    # Datum, Open, High, Low, Close
    ("2025.08.19", 3333.098, 3345.165, 3314.775, 3316.035),  # Červená
    ("2025.08.20", 3315.465, 3350.198, 3311.255, 3347.335),  # <PERSON>elená - za<PERSON><PERSON><PERSON>k LONG
    ("2025.08.21", 3349.098, 3351.935, 3324.948, 3338.935),  # Červená - Entry den
    ("2025.08.22", 3338.405, 3378.585, 3321.045, 3371.235),  # <PERSON><PERSON><PERSON>
    ("2025.08.25", 3370.835, 3376.135, 3359.495, 3365.605),  # Červená
    ("2025.08.26", 3365.135, 3393.495, 3351.175, 3393.465),  # Zelená
    ("2025.08.27", 3392.648, 3398.569, 3373.665, 3396.495),  # Zelená
    ("2025.08.28", 3397.835, 3423.069, 3384.445, 3415.795),  # Zelená
    ("2025.08.29", 3416.965, 3453.565, 3404.185, 3446.805),  # Zelená
    ("2025.09.01", 3445.648, 3489.595, 3436.548, 3476.225),  # Zelená
    ("2025.09.02", 3476.505, 3539.849, 3469.805, 3532.405),  # Zelená
    ("2025.09.03", 3532.698, 3578.175, 3525.848, 3558.475),  # Zelená - konec sekvence
    ("2025.09.04", 3560.448, 3563.998, 3510.425, 3544.645),  # Červená
    ("2025.09.05", 3545.935, 3599.905, 3539.715, 3585.195),  # Zelená
]

def calculate_tp_levels():
    """Vypočítá TP úrovně pro různé RRR"""
    print("🎯 KALKULACE TAKE PROFIT ÚROVNÍ:")
    print("=" * 80)
    print(f"Entry Price: {entry_price}")
    print(f"ATR10: {atr10}")
    print(f"Stop Loss: {entry_price - atr10:.2f}")
    print()
    
    tp_levels = {}
    for rrr in range(1, 21):  # RRR 1-20
        tp_price = entry_price + (rrr * atr10)
        tp_levels[rrr] = tp_price
        print(f"RRR {rrr:2d}: TP = {tp_price:7.2f}")
    
    return tp_levels

def check_tp_hits(tp_levels):
    """Kontroluje, zda bylo dosaženo TP během sekvence"""
    print("\n🔍 KONTROLA DOSAŽENÍ TP BĚHEM SEKVENCE:")
    print("=" * 80)
    
    tp_hits = []
    
    # Začínáme od 21.8. (entry den)
    for i, (date, o, h, l, c) in enumerate(test_data[2:], 2):  # Skip first 2 days
        print(f"\n📅 {date}: High = {h:.2f}")
        
        for rrr in range(1, 21):
            tp_price = tp_levels[rrr]
            if h >= tp_price:
                if rrr not in [hit[1] for hit in tp_hits]:  # Avoid duplicates
                    tp_hits.append((date, rrr, tp_price, h))
                    print(f"  ✅ RRR {rrr:2d} HIT! TP {tp_price:.2f} <= High {h:.2f}")
    
    return tp_hits

def analyze_exit_reason():
    """Analyzuje důvod ukončení obchodu"""
    print("\n🎯 ANALÝZA DŮVODU UKONČENÍ:")
    print("=" * 80)
    
    # Skutečný exit
    print(f"Skutečný Exit Price: {exit_price}")
    print(f"Exit Date: 2025-09-03")
    print(f"Exit Reason: SEQ_END")
    
    # Kalkulace RRR pro skutečný exit
    actual_profit = exit_price - entry_price
    actual_rrr = actual_profit / atr10
    
    print(f"\nSkutečný profit: {actual_profit:.2f} bodů")
    print(f"Skutečný RRR: {actual_rrr:.2f}")
    
    # Kontrola, zda byl dosažen nějaký TP
    tp_levels = {}
    for rrr in range(1, 21):
        tp_levels[rrr] = entry_price + (rrr * atr10)
    
    print(f"\nKontrola TP úrovní:")
    for rrr in range(1, 21):
        tp_price = tp_levels[rrr]
        if abs(exit_price - tp_price) < 1.0:  # Tolerance 1 bod
            print(f"  🎯 MOŽNÝ TP HIT! RRR {rrr}: {tp_price:.2f} ≈ Exit {exit_price:.2f}")
    
    # Kontrola High 3.9.
    sep_03_high = 3578.175
    print(f"\nHigh 3.9.2025: {sep_03_high}")
    
    for rrr in range(1, 21):
        tp_price = tp_levels[rrr]
        if sep_03_high >= tp_price and tp_price > exit_price:
            print(f"  ⚠️  RRR {rrr} ({tp_price:.2f}) byl dosažen během dne, ale exit byl na Close!")

def main():
    print("🎯 ANALÝZA TAKE PROFIT - LONG sekvence 20.8.-3.9.2025")
    print("=" * 80)
    print("Kontrola, zda sekvence skončila kvůli dosažení TP s vysokým RRR")
    print()
    
    tp_levels = calculate_tp_levels()
    tp_hits = check_tp_hits(tp_levels)
    
    print(f"\n📊 SHRNUTÍ TP HITŮ:")
    print("=" * 50)
    if tp_hits:
        for date, rrr, tp_price, high in tp_hits:
            print(f"{date}: RRR {rrr:2d} ({tp_price:7.2f}) - High {high:.2f}")
    else:
        print("Žádné TP nebylo dosaženo během sekvence")
    
    analyze_exit_reason()
    
    print(f"\n🎯 ZÁVĚR:")
    print("Pokud byl dosažen vysoký RRR (10+), pak obchod skončil kvůli TP, ne SEQ_END.")
    print("Pokud nebyl dosažen žádný TP, pak obchod skutečně skončil SEQ_END.")

if __name__ == "__main__":
    main()
