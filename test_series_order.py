#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test správného Series_Order - Series_Order=1 by mě<PERSON> b<PERSON><PERSON> na entry sví<PERSON><PERSON>, ne Marubozu
"""

import urllib.request
import json
import time

def test_series_order():
    """Testuje správný Series_Order pro LONG sekvenci 20.8.-3.9.2025"""
    
    print("🎯 TEST SERIES_ORDER")
    print("=" * 80)
    print("Očekáváno:")
    print("20.8.2025: Green Marubozu - Series_Order=0 (signál)")
    print("21.8.2025: Entry den - Series_Order=1 (vstup do obchodu)")
    print("22.8.2025: Pokračování - Series_Order=2")
    print()
    
    # Parametry pro test - kratší období pro lepší čitelnost
    params = {
        'dataFile': 'XAUUSD_GMT+2_US-DST_D1.csv',
        'dataRange': 'CUSTOM',
        'startDate': '2025-08-20',
        'endDate': '2025-08-25',
        'variant': 'Tolerant',
        'rrr': 6,
        'exitPolicy': 'Optimistic',
        'overnightMode': 'STANDARD',
        'startEquity': 10000,
        'riskPct': 2.0
    }
    
    # POST požadavek
    url = 'http://localhost:8080/run_test'
    data = json.dumps(params).encode('utf-8')
    
    try:
        print("🚀 Spouštím test...")
        req = urllib.request.Request(url, data=data, headers={'Content-Type': 'application/json'})
        with urllib.request.urlopen(req, timeout=60) as response:
            content = response.read().decode('utf-8')
            
            # Parsování JSON odpovědi
            response_data = json.loads(content)
            if response_data.get('success'):
                results_text = response_data.get('results', '')
                
                print("✅ Test dokončen")
                print("📥 Stáhněte Excel soubor z WebGUI pro detailní kontrolu Series_Order")
                print()
                
                # Hledání informací o Excel souboru
                lines = results_text.split('\n')
                for line in lines:
                    if 'Excel soubor vytvořen' in line:
                        print(f"📊 {line.strip()}")
                    elif 'Stáhnout Excel' in line:
                        print(f"📥 {line.strip()}")
                
                print()
                print("🔍 V Excel souboru zkontrolujte:")
                print("Sheet 'Denní_Analýza' - sloupec 'Series_Order':")
                print("2025-08-20: Series_Order = 0 (Marubozu signál)")
                print("2025-08-21: Series_Order = 1 (Entry den) ← KLÍČOVÉ")
                print("2025-08-22: Series_Order = 2 (Pokračování)")
                print()
                
                # Hledání obchodních výsledků
                found_trade = False
                for line in lines:
                    if 'LONG' in line and '2025-08-21' in line:
                        print(f"✅ Nalezen obchod začínající 21.8.: {line.strip()}")
                        found_trade = True
                        break
                
                if not found_trade:
                    print("⚠️  Obchod začínající 21.8. nebyl nalezen v TOP5")
                    
            else:
                print(f"❌ Test selhal: {response_data.get('error', 'Neznámá chyba')}")
                
    except Exception as e:
        print(f"❌ Chyba při HTTP požadavku: {e}")

def main():
    print("🎯 TEST SPRÁVNÉHO SERIES_ORDER")
    print("=" * 80)
    print("Testuje, že Series_Order=1 je na entry svíčce, ne na Marubozu signálu")
    print()
    
    test_series_order()
    
    print(f"\n🎯 NÁVOD PRO OVĚŘENÍ:")
    print("1. Otevřete http://localhost:8080")
    print("2. Nastavte období: 2025-08-20 až 2025-08-25")
    print("3. Spusťte Tolerant test")
    print("4. Stáhněte Excel soubor")
    print("5. Zkontrolujte Sheet 'Denní_Analýza' - Series_Order sloupec")
    print()
    print("✅ SPRÁVNĚ: 20.8.=0, 21.8.=1, 22.8.=2")
    print("❌ CHYBNĚ: 20.8.=1, 21.8.=2, 22.8.=3")

if __name__ == "__main__":
    main()
