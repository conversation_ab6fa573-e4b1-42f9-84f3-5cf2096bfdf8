#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Kontrola Denní_Analýza sheetu
"""

import openpyxl
import os

def check_daily_analysis():
    excel_file = "TrendTaker_WebGUI_Tolerant_2025-08-19_2025-09-18.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel soubor nenalezen: {excel_file}")
        return
    
    print(f"✅ Excel soubor nalezen: {excel_file}")
    
    wb = openpyxl.load_workbook(excel_file)
    
    if 'Denní_Analýza' in wb.sheetnames:
        ws = wb['Denní_Analýza']
        print(f"📊 Denní_Analýza: {ws.max_row - 1} řádků")
        
        if ws.max_row > 1:
            # Headers
            headers = []
            for col in range(1, ws.max_column + 1):
                header = ws.cell(row=1, column=col).value
                if header:
                    headers.append(header)
            
            print(f"📋 Sloupce: {headers}")
            
            # Kontrola nových sloupců
            new_columns = ['LONG_LMT_SL_Method', 'BarsBack', 'Calculated_SL']
            for col_name in new_columns:
                if col_name in headers:
                    print(f"✅ {col_name}: Přítomen")
                else:
                    print(f"❌ {col_name}: Chybí")
            
            # Ukázka prvních 5 řádků
            print(f"\n📈 UKÁZKA PRVNÍCH 5 ŘÁDKŮ:")
            print("=" * 80)
            
            # Najdeme indexy nových sloupců
            method_idx = headers.index('LONG_LMT_SL_Method') + 1 if 'LONG_LMT_SL_Method' in headers else None
            bars_idx = headers.index('BarsBack') + 1 if 'BarsBack' in headers else None
            sl_idx = headers.index('Calculated_SL') + 1 if 'Calculated_SL' in headers else None
            date_idx = headers.index('Date') + 1 if 'Date' in headers else None
            direction_idx = headers.index('Direction') + 1 if 'Direction' in headers else None
            
            for row in range(2, min(7, ws.max_row + 1)):  # Prvních 5 řádků
                date_val = ws.cell(row=row, column=date_idx).value if date_idx else 'N/A'
                direction_val = ws.cell(row=row, column=direction_idx).value if direction_idx else 'N/A'
                method_val = ws.cell(row=row, column=method_idx).value if method_idx else 'N/A'
                bars_val = ws.cell(row=row, column=bars_idx).value if bars_idx else 'N/A'
                sl_val = ws.cell(row=row, column=sl_idx).value if sl_idx else 'N/A'

                print(f"Řádek {row-1}: {date_val} | Direction='{direction_val}' | {method_val} | {bars_val} | {sl_val}")
    
    wb.close()

if __name__ == "__main__":
    check_daily_analysis()
