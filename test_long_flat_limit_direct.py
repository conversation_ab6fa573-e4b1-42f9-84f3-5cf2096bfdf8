#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Přímý test LONG-FLAT-LIMIT logiky bez WebGUI
Test sekvence 19.08 - 18.09.2025 pro ověření SL a počtu obchodů
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
from math import floor

class DirectLongFlatLimitTest:
    def __init__(self):
        self.USD_PER_BOD_PER_LOT = 100.0
        self.SPREAD_BOD = 0.15
        self.SWAP_LONG_BOD = -0.55237
        self.SWAP_SHORT_BOD = 0.29425
        self.DATA_DIRECTORY = r"C:\Temp\Trading\DATA ICM"
    
    def load_data(self, filename, start_date, end_date):
        """Načte data ze souboru"""
        filepath = os.path.join(self.DATA_DIRECTORY, filename)
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Soubor nenalezen: {filepath}")
        
        print(f"📁 Načítám data z: {filepath}")
        
        # Načtení CSV
        raw = pd.read_csv(filepath, sep=';')
        print(f"📊 Načteno {len(raw)} řádků")
        print(f"📋 Sloupce: {list(raw.columns)}")
        print(f"📋 První řádek: {raw.iloc[0].to_dict()}")

        # Automatická detekce sloupců
        cols = list(raw.columns)
        if len(cols) >= 6:
            # Standardní formát: Date, Time, Open, High, Low, Close
            raw.columns = ["Date", "Time", "Open", "High", "Low", "Close"] + cols[6:]
        else:
            raise ValueError(f"Neočekávaný počet sloupců: {len(cols)}")

        # Parsování datumu
        def parse_datetime(row):
            try:
                date_str = str(row["Date"])
                time_str = str(row["Time"]) if "Time" in row else "00:00:00"
                return pd.to_datetime(f"{date_str} {time_str}")
            except:
                return pd.NaT

        raw["dt"] = raw.apply(parse_datetime, axis=1)

        # Konverze numerických hodnot
        for col in ["Open","High","Low","Close"]:
            if col in raw.columns:
                raw[col] = pd.to_numeric(raw[col], errors="coerce")

        # Vyčištění a validace
        raw = raw.dropna(subset=["dt","Open","High","Low","Close"]).copy()
        raw = raw.sort_values("dt").reset_index(drop=True)
        
        # Filtrování podle datumu
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        mask = (raw["dt"] >= start_dt) & (raw["dt"] <= end_dt)
        filtered = raw[mask].copy().reset_index(drop=True)
        
        print(f"📅 Filtrováno na období {start_date} - {end_date}: {len(filtered)} řádků")
        
        return filtered
    
    def calculate_indicators(self, df):
        """Vypočítá indikátory"""
        print("📊 Počítám indikátory...")
        
        o = df["Open"].to_numpy(float)
        h = df["High"].to_numpy(float)
        l = df["Low"].to_numpy(float)
        c = df["Close"].to_numpy(float)
        
        # ATR10
        tr = np.maximum(h - l, np.maximum(np.abs(h - np.roll(c, 1)), np.abs(l - np.roll(c, 1))))
        tr[0] = h[0] - l[0]  # První den
        
        atr10 = np.zeros_like(tr)
        for i in range(len(tr)):
            if i < 10:
                atr10[i] = np.mean(tr[:i+1])
            else:
                atr10[i] = np.mean(tr[i-9:i+1])
        
        # Základní indikátory
        df["Bull"] = c >= o
        df["Range"] = h - l
        
        # Marubozu detekce
        wick_ratios = np.zeros_like(o)
        body_ratios = np.zeros_like(o)
        
        for i in range(len(o)):
            range_val = h[i] - l[i]
            if range_val > 0:
                if c[i] >= o[i]:  # Bullish
                    open_wick = o[i] - l[i]
                else:  # Bearish
                    open_wick = h[i] - o[i]
                
                wick_ratios[i] = open_wick / range_val
                body_ratios[i] = abs(c[i] - o[i]) / range_val
        
        df["Wick_Ratio"] = wick_ratios
        df["Body_Ratio"] = body_ratios
        df["ATR10"] = atr10
        df["ATR10_s1"] = np.roll(atr10, 1)  # ATR10 předchozího dne
        df["ATR10_s1"].iloc[0] = atr10[0]
        
        # Marubozu podmínky (wick < 18%, body >= 60%, range > ATR10_s1)
        df["Is_Marubozu"] = (
            (wick_ratios < 0.18) & 
            (body_ratios >= 0.60) & 
            (df["Range"] > df["ATR10_s1"])
        )
        
        print(f"✅ Nalezeno {df['Is_Marubozu'].sum()} Marubozu svíček")
        
        return df
    
    def build_sequences_tolerant(self, df):
        """Vytvoří sekvence - Tolerant varianta"""
        print("🔍 Hledám sekvence (Tolerant)...")
        
        rows = []
        on = False
        dir_bull = None
        order = 0
        
        for i, row in df.iterrows():
            day_bull = bool(row["Bull"])
            mb = bool(row["Is_Marubozu"])
            series_order = 0
            
            if on:
                if day_bull == dir_bull:
                    order += 1
                    series_order = order
                else:
                    # Tolerant: Povolíme 1 opačnou svíčku
                    if order == 1:
                        # První opačná svíčka - tolerujeme
                        order += 1
                        series_order = order
                        rows[-1]["Tolerated_Opposite"] = True
                    else:
                        # Konec sekvence
                        on = False
                        order = 0
                        if mb:
                            on = True
                            dir_bull = day_bull
                            order = 1
                            series_order = order
            else:
                if mb:
                    on = True
                    dir_bull = day_bull
                    order = 1
                    series_order = order
            
            rows.append({
                "Date": row["dt"],
                "Open": row["Open"],
                "High": row["High"],
                "Low": row["Low"],
                "Close": row["Close"],
                "Bull": day_bull,
                "Is_Marubozu": mb,
                "Series_Order": series_order,
                "Direction": "Up" if dir_bull else "Down" if dir_bull is not None else None,
                "Color": "Green" if day_bull else "Red",
                "Tolerated_Opposite": False,
                "ATR10_s1": row["ATR10_s1"]
            })
        
        return pd.DataFrame(rows)
    
    def extract_sequences(self, days_df):
        """Extrahuje sekvence z denních dat"""
        seqs = []
        current_seq = None
        
        for i, row in days_df.iterrows():
            if row["Series_Order"] == 1:
                # Začátek nové sekvence
                if current_seq is not None:
                    # Dokončení předchozí sekvence
                    current_seq["End_Idx"] = i - 1
                    current_seq["End_Date"] = days_df.iloc[i-1]["Date"]
                    current_seq["Length"] = current_seq["End_Idx"] - current_seq["Start_Idx"] + 1
                    seqs.append(current_seq)
                
                # Nová sekvence
                current_seq = {
                    "Start_Idx": i,
                    "Start_Date": row["Date"],
                    "Direction": row["Direction"]
                }
            elif row["Series_Order"] == 0 and current_seq is not None:
                # Konec sekvence
                current_seq["End_Idx"] = i - 1
                current_seq["End_Date"] = days_df.iloc[i-1]["Date"]
                current_seq["Length"] = current_seq["End_Idx"] - current_seq["Start_Idx"] + 1
                seqs.append(current_seq)
                current_seq = None
        
        # Dokončení poslední sekvence
        if current_seq is not None:
            current_seq["End_Idx"] = len(days_df) - 1
            current_seq["End_Date"] = days_df.iloc[-1]["Date"]
            current_seq["Length"] = current_seq["End_Idx"] - current_seq["Start_Idx"] + 1
            seqs.append(current_seq)
        
        seqs_df = pd.DataFrame(seqs)
        print(f"📊 Nalezeno {len(seqs_df)} sekvencí")
        
        for i, seq in seqs_df.iterrows():
            print(f"   Sekvence {i+1}: {seq['Direction']} {seq['Start_Date'].strftime('%Y-%m-%d')} - {seq['End_Date'].strftime('%Y-%m-%d')} ({seq['Length']} dní)")
        
        return seqs_df
    
    def simulate_long_flat_limit_position(self, s, e, entry, stop, tp, lots, spread, atr10, rrr, 
                                        buy_limit_offset, O, H, L, C, D, ATR):
        """Simuluje LONG pozici s LONG-FLAT-LIMIT logikou"""
        
        print(f"\n🔍 === LONG-FLAT-LIMIT SIMULACE ===")
        print(f"Sekvence: den {s} - {e}")
        print(f"Entry: {entry:.2f}, Stop: {stop:.2f}, TP: {tp:.2f}")
        print(f"Lots: {lots}, Buy Limit offset: {buy_limit_offset}")
        
        current_day = s
        current_entry = entry
        position_active = True
        total_pnl_bod = 0
        total_pnl_usd = 0
        total_spread_cost = 0
        segments = []
        final_exit_reason = ""
        final_exit_price = 0
        final_exit_date = ""
        segment_count = 0
        
        while current_day <= e and position_active:
            segment_count += 1
            hi, lo, cl, dt = H[current_day], L[current_day], C[current_day], D[current_day]
            
            print(f"\n📅 Segment {segment_count} - Den {current_day} ({pd.to_datetime(dt).strftime('%Y-%m-%d')}):")
            print(f"   Entry: {current_entry:.2f}, OHLC: {O[current_day]:.2f}/{hi:.2f}/{lo:.2f}/{cl:.2f}")
            print(f"   SL: {stop:.2f}, TP: {tp:.2f}")
            
            # Kontrola TP/SL během dne
            hit_tp = hi >= tp
            hit_sl = lo <= stop
            
            if hit_tp and hit_sl:
                exit_px = tp  # Optimistic
                reason = "TP (same-day)"
                position_active = False
                print(f"   ✅ TP a SL hit - exit na TP: {exit_px:.2f}")
            elif hit_tp:
                exit_px = tp
                reason = "TP"
                position_active = False
                print(f"   ✅ TP hit: {exit_px:.2f}")
            elif hit_sl:
                exit_px = stop
                reason = "SL"
                position_active = False
                print(f"   ❌ SL hit: {exit_px:.2f}")
            else:
                # Pozice přežila den - uzavřeme na Close
                exit_px = cl
                if current_day == e:
                    reason = "SEQ_END"
                    position_active = False
                    print(f"   📅 Konec sekvence - exit na Close: {exit_px:.2f}")
                else:
                    reason = "EOD_CLOSE"
                    print(f"   🌅 EOD Close: {exit_px:.2f}")
            
            # Výpočet P&L pro tento segment
            segment_pnl_bod = exit_px - current_entry
            segment_pnl_usd = segment_pnl_bod * self.USD_PER_BOD_PER_LOT * lots
            
            total_pnl_bod += segment_pnl_bod
            total_pnl_usd += segment_pnl_usd
            total_spread_cost += spread
            
            print(f"   💰 Segment P&L: {segment_pnl_bod:.2f} bodů = ${segment_pnl_usd:.2f}")
            
            segments.append({
                "day": current_day,
                "entry": current_entry,
                "exit": exit_px,
                "pnl_bod": segment_pnl_bod,
                "reason": reason
            })
            
            final_exit_price = exit_px
            final_exit_date = pd.to_datetime(dt).strftime("%Y-%m-%d")
            final_exit_reason = reason
            
            if position_active and current_day < e:
                # Pokračujeme další den s Buy Limit
                next_day = current_day + 1
                if next_day <= e:
                    next_open = O[next_day]
                    buy_limit_price = next_open - buy_limit_offset
                    next_low = L[next_day]
                    
                    print(f"   📋 Další den Buy Limit: {buy_limit_price:.2f} (Open: {next_open:.2f}, Low: {next_low:.2f})")
                    
                    if next_low <= buy_limit_price:
                        # Buy Limit se naplnil
                        current_entry = buy_limit_price
                        current_day = next_day
                        
                        # Aktualizace SL na 50% předposlední svíčky
                        if current_day > 0:
                            prev_range = H[current_day-1] - L[current_day-1]
                            old_stop = stop
                            stop = current_entry - (prev_range * 0.5)
                            print(f"   🔄 SL aktualizace: {old_stop:.2f} → {stop:.2f} (50% z {prev_range:.2f})")
                        
                        final_exit_reason += " -> BUY_LIMIT_FILLED"
                        print(f"   ✅ Buy Limit naplněn na {buy_limit_price:.2f}")
                    else:
                        # Buy Limit se nenaplnil
                        final_exit_reason += " -> BUY_LIMIT_NOT_FILLED"
                        position_active = False
                        print(f"   ❌ Buy Limit nenaplněn (Low {next_low:.2f} > Limit {buy_limit_price:.2f})")
                else:
                    position_active = False
            else:
                position_active = False
            
            if not position_active:
                break
        
        # Vytvoření konsolidovaného trade záznamu
        net_pnl = total_pnl_usd - total_spread_cost
        
        print(f"\n📊 === KONSOLIDOVANÝ VÝSLEDEK ===")
        print(f"Celkem segmentů: {len(segments)}")
        print(f"Celkový P&L: {total_pnl_bod:.2f} bodů = ${total_pnl_usd:.2f}")
        print(f"Spread cost: ${total_spread_cost:.2f}")
        print(f"Net P&L: ${net_pnl:.2f}")
        print(f"Finální exit reason: {final_exit_reason}")
        
        trade = {
            "Start_Date": pd.to_datetime(D[s]).strftime("%Y-%m-%d"),
            "End_Date": final_exit_date,
            "Direction": "Long",
            "Lots": round(lots, 2),
            "ATR10": round(atr10, 2),
            "Entry_Price": round(entry, 2),
            "Exit_Price": round(final_exit_price, 2),
            "Stop_Loss": round(stop, 2),
            "Take_Profit": round(tp, 2),
            "Exit_Reason": final_exit_reason,
            "Spread_Cost": round(total_spread_cost, 2),
            "Swaps_USD": 0.0,
            "PnL_Bod": round(total_pnl_bod, 2),
            "PnL_USD": round(total_pnl_usd, 2),
            "Net_PnL": round(net_pnl, 2),
            "Segments": len(segments)
        }
        
        return [trade]

def test_august_september_direct():
    """Přímý test sekvence 19.08 - 18.09.2025"""
    
    print("🧪 PŘÍMÝ TEST LONG-FLAT-LIMIT - 19.08-18.09.2025")
    print("=" * 80)
    
    tester = DirectLongFlatLimitTest()
    
    try:
        # Načtení dat
        df = tester.load_data("XAUUSD_GMT+2_US-DST_D1.csv", "2025-08-19", "2025-09-18")
        
        # Výpočet indikátorů
        df = tester.calculate_indicators(df)
        
        # Vytvoření sekvencí
        days_df = tester.build_sequences_tolerant(df)
        seqs_df = tester.extract_sequences(days_df)
        
        if len(seqs_df) == 0:
            print("❌ Žádné sekvence nenalezeny!")
            return
        
        # Simulace obchodů
        print(f"\n💹 SIMULACE OBCHODŮ")
        print("=" * 80)
        
        # Parametry
        start_equity = 10000.0
        risk_pct = 0.02  # 2%
        rrr = 6.0
        buy_limit_offset = 5
        spread_usd_per_lot = tester.SPREAD_BOD * tester.USD_PER_BOD_PER_LOT
        
        O = days_df["Open"].to_numpy(float)
        H = days_df["High"].to_numpy(float)
        L = days_df["Low"].to_numpy(float)
        C = days_df["Close"].to_numpy(float)
        D = pd.to_datetime(days_df["Date"]).to_numpy()
        ATR = days_df["ATR10_s1"].to_numpy(float)
        
        trades = []
        equity = start_equity
        
        for seq_idx, (_, seq) in enumerate(seqs_df.iterrows()):
            s = int(seq["Start_Idx"])
            e = int(seq["End_Idx"])
            is_up = (seq["Direction"] == "Up")
            atr10 = ATR[s]
            
            print(f"\n🔹 SEKVENCE {seq_idx+1}: {seq['Direction']} ({seq['Start_Date'].strftime('%Y-%m-%d')} - {seq['End_Date'].strftime('%Y-%m-%d')})")
            print(f"   Start_Idx: {s}, End_Idx: {e}, ATR10: {atr10:.2f}")
            
            if not np.isfinite(atr10) or atr10 <= 0:
                print(f"   ❌ Přeskočeno - neplatné ATR: {atr10}")
                continue
            
            # Position sizing
            risk_usd = equity * risk_pct
            risk_per_lot = atr10 * tester.USD_PER_BOD_PER_LOT
            lots = risk_usd / risk_per_lot
            lots = max(0.01, min(100.0, round(lots, 2)))
            
            print(f"   💰 Position sizing: equity=${equity:.0f}, risk={risk_pct*100}%, lots={lots}")
            
            if lots <= 0:
                print(f"   ❌ Přeskočeno - lots <= 0")
                continue
            
            spread = spread_usd_per_lot * lots
            entry = O[s]
            stop = (entry - atr10) if is_up else (entry + atr10)
            tp = entry + (rrr * atr10) if is_up else entry - (rrr * atr10)
            
            if is_up:
                # LONG pozice - LONG-FLAT-LIMIT
                print(f"   📈 LONG pozice - LONG-FLAT-LIMIT")
                trades_from_seq = tester.simulate_long_flat_limit_position(
                    s, e, entry, stop, tp, lots, spread, atr10, rrr,
                    buy_limit_offset, O, H, L, C, D, ATR
                )
                
                for trade in trades_from_seq:
                    equity += trade["Net_PnL"]
                    trade["Equity_After"] = round(equity, 2)
                    trades.append(trade)
            else:
                # SHORT pozice - standardní (pro porovnání)
                print(f"   📉 SHORT pozice - standardní logika")
                # Zde by byla standardní SHORT logika
                print(f"   ⏭️  SHORT pozice přeskočena pro tento test")
        
        # Výsledky
        print(f"\n🎯 FINÁLNÍ VÝSLEDKY")
        print("=" * 80)
        print(f"Počet sekvencí: {len(seqs_df)}")
        print(f"Počet obchodů: {len(trades)}")
        print(f"Počáteční kapitál: ${start_equity:,.0f}")
        print(f"Finální kapitál: ${equity:,.0f}")
        
        if trades:
            total_pnl = sum(t["Net_PnL"] for t in trades)
            print(f"Celkový P&L: ${total_pnl:,.0f}")
            
            print(f"\n📋 DETAILNÍ SEZNAM OBCHODŮ:")
            for i, trade in enumerate(trades, 1):
                print(f"{i}. {trade['Direction']} {trade['Start_Date']} - {trade['End_Date']}")
                print(f"   Entry: {trade['Entry_Price']:.2f}, Exit: {trade['Exit_Price']:.2f}")
                print(f"   SL: {trade['Stop_Loss']:.2f}, TP: {trade['Take_Profit']:.2f}")
                print(f"   Exit Reason: {trade['Exit_Reason']}")
                print(f"   P&L: ${trade['Net_PnL']:.2f}, Segments: {trade.get('Segments', 1)}")
                print(f"   Equity After: ${trade['Equity_After']:.2f}")
                print()
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_august_september_direct()
