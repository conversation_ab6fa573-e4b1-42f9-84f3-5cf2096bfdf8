#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test nové LONG-FLAT-LIMIT funkcionality:
1. Long pozice se uzavírají na Close každý den
2. Následující den se staví Buy Limit na Open - X bodů
3. <PERSON><PERSON><PERSON><PERSON>, zda se Buy Limit naplnil
4. SL se nastaví na 50% předposlední sv<PERSON>
5. Žádné swapy pro Long pozice
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

# Simulace WebGUI třídy pro testování
class TestLongFlatLimit:
    def __init__(self):
        self.USD_PER_BOD_PER_LOT = 100.0
        self.SPREAD_BOD = 0.15
        self.SWAP_LONG_BOD = -0.55237
        self.SWAP_SHORT_BOD = 0.29425
    
    def simulate_long_flat_limit_simple(self, open_prices, high_prices, low_prices, close_prices, 
                                      dates, entry_day, seq_end_day, rrr, buy_limit_offset):
        """Zjednodušená simulace LONG-FLAT-LIMIT pro testování"""
        
        trades = []
        current_day = entry_day
        current_entry = open_prices[entry_day]
        position_active = True
        
        # Počáteční TP/SL
        atr10 = 50.0  # Simulovaná ATR hodnota
        tp = current_entry + (rrr * atr10)
        stop = current_entry - atr10
        
        print(f"📊 LONG-FLAT-LIMIT simulace:")
        print(f"   Entry: {current_entry:.2f} na {dates[entry_day]}")
        print(f"   TP: {tp:.2f}, SL: {stop:.2f}")
        print(f"   Buy Limit offset: {buy_limit_offset} bodů")
        print(f"   Sekvence končí: {dates[seq_end_day]}")
        print()
        
        day_counter = 1
        
        while current_day <= seq_end_day and position_active:
            hi = high_prices[current_day]
            lo = low_prices[current_day]
            cl = close_prices[current_day]
            dt = dates[current_day]
            
            print(f"Den {day_counter} ({dt}):")
            print(f"   Entry: {current_entry:.2f}, OHLC: {open_prices[current_day]:.2f}/{hi:.2f}/{lo:.2f}/{cl:.2f}")
            
            # Kontrola TP/SL během dne
            hit_tp = hi >= tp
            hit_sl = lo <= stop
            
            if hit_tp and hit_sl:
                exit_px = tp  # Optimistic
                reason = "TP (same-day)"
                position_active = False
                print(f"   ✅ TP a SL hit - exit na TP: {exit_px:.2f}")
            elif hit_tp:
                exit_px = tp
                reason = "TP"
                position_active = False
                print(f"   ✅ TP hit: {exit_px:.2f}")
            elif hit_sl:
                exit_px = stop
                reason = "SL"
                position_active = False
                print(f"   ❌ SL hit: {exit_px:.2f}")
            else:
                # Pozice přežila den - uzavřeme na Close
                exit_px = cl
                if current_day == seq_end_day:
                    reason = "SEQ_END"
                    position_active = False
                    print(f"   📅 Konec sekvence - exit na Close: {exit_px:.2f}")
                else:
                    reason = "EOD_CLOSE"
                    print(f"   🌅 EOD Close: {exit_px:.2f}")
            
            # Výpočet P&L pro tento segment
            pnl_bod = exit_px - current_entry
            pnl_usd = pnl_bod * self.USD_PER_BOD_PER_LOT
            spread_cost = self.SPREAD_BOD * self.USD_PER_BOD_PER_LOT
            net_pnl = pnl_usd - spread_cost  # Žádné swapy
            
            print(f"   💰 P&L: {pnl_bod:.2f} bodů = ${pnl_usd:.2f} (net: ${net_pnl:.2f})")
            
            # Vytvoření trade záznamu
            trade = {
                "day": day_counter,
                "date": dt,
                "entry_price": current_entry,
                "exit_price": exit_px,
                "pnl_bod": pnl_bod,
                "pnl_usd": pnl_usd,
                "net_pnl": net_pnl,
                "exit_reason": reason,
                "swaps": 0.0
            }
            trades.append(trade)
            
            if position_active and current_day < seq_end_day:
                # Pokračujeme další den s Buy Limit
                next_day = current_day + 1
                if next_day <= seq_end_day:
                    next_open = open_prices[next_day]
                    buy_limit_price = next_open - buy_limit_offset
                    next_low = low_prices[next_day]
                    
                    print(f"   📋 Další den Buy Limit: {buy_limit_price:.2f} (Open: {next_open:.2f}, Low: {next_low:.2f})")
                    
                    if next_low <= buy_limit_price:
                        # Buy Limit se naplnil
                        current_entry = buy_limit_price
                        current_day = next_day
                        
                        # Aktualizace SL na 50% předposlední svíčky
                        if current_day > 0:
                            prev_range = high_prices[current_day-1] - low_prices[current_day-1]
                            stop = current_entry - (prev_range * 0.5)
                        
                        trade["exit_reason"] += " -> BUY_LIMIT_FILLED"
                        print(f"   ✅ Buy Limit naplněn na {buy_limit_price:.2f}, nový SL: {stop:.2f}")
                        day_counter += 1
                    else:
                        # Buy Limit se nenaplnil
                        trade["exit_reason"] += " -> BUY_LIMIT_NOT_FILLED"
                        position_active = False
                        print(f"   ❌ Buy Limit nenaplněn (Low {next_low:.2f} > Limit {buy_limit_price:.2f})")
                else:
                    position_active = False
            else:
                position_active = False
            
            print()
            
            if not position_active:
                break
        
        return trades

def test_long_flat_limit():
    """Test LONG-FLAT-LIMIT logiky"""
    
    print("🧪 TEST LONG-FLAT-LIMIT FUNKCIONALITY")
    print("=" * 60)
    
    # Simulovaná data pro testování
    test_data = [
        # Date, Open, High, Low, Close
        ("2025-08-20", 3315.47, 3350.20, 3311.26, 3347.34),  # Entry den - Bullish
        ("2025-08-21", 3349.10, 3365.85, 3340.15, 3358.45),  # Den 2
        ("2025-08-22", 3360.20, 3375.90, 3355.10, 3370.25),  # Den 3
        ("2025-08-23", 3372.15, 3385.60, 3365.80, 3380.90),  # Den 4
        ("2025-08-24", 3382.50, 3395.20, 3375.30, 3388.75),  # Den 5 - konec sekvence
    ]
    
    dates = [item[0] for item in test_data]
    opens = [item[1] for item in test_data]
    highs = [item[2] for item in test_data]
    lows = [item[3] for item in test_data]
    closes = [item[4] for item in test_data]
    
    print(f"📊 Testovací data ({len(test_data)} dní):")
    for i, (date, o, h, l, c) in enumerate(test_data):
        print(f"   {i}: {date} OHLC: {o}/{h}/{l}/{c}")
    print()
    
    # Test různých scénářů
    simulator = TestLongFlatLimit()
    
    # Scénář 1: Buy Limit offset 5 bodů
    print("🔹 SCÉNÁŘ 1: Buy Limit offset 5 bodů")
    print("-" * 40)
    trades1 = simulator.simulate_long_flat_limit_simple(
        opens, highs, lows, closes, dates, 
        entry_day=0, seq_end_day=4, rrr=6.0, buy_limit_offset=5
    )
    
    total_pnl1 = sum(t["net_pnl"] for t in trades1)
    print(f"📈 Celkový výsledek: ${total_pnl1:.2f}")
    print(f"📊 Počet segmentů: {len(trades1)}")
    print()
    
    # Scénář 2: Buy Limit offset 10 bodů (větší offset)
    print("🔹 SCÉNÁŘ 2: Buy Limit offset 10 bodů")
    print("-" * 40)
    trades2 = simulator.simulate_long_flat_limit_simple(
        opens, highs, lows, closes, dates, 
        entry_day=0, seq_end_day=4, rrr=6.0, buy_limit_offset=10
    )
    
    total_pnl2 = sum(t["net_pnl"] for t in trades2)
    print(f"📈 Celkový výsledek: ${total_pnl2:.2f}")
    print(f"📊 Počet segmentů: {len(trades2)}")
    print()
    
    # Scénář 3: Krátká sekvence (2 dny)
    print("🔹 SCÉNÁŘ 3: Krátká sekvence (2 dny)")
    print("-" * 40)
    trades3 = simulator.simulate_long_flat_limit_simple(
        opens, highs, lows, closes, dates, 
        entry_day=0, seq_end_day=1, rrr=6.0, buy_limit_offset=5
    )
    
    total_pnl3 = sum(t["net_pnl"] for t in trades3)
    print(f"📈 Celkový výsledek: ${total_pnl3:.2f}")
    print(f"📊 Počet segmentů: {len(trades3)}")
    print()
    
    print("🎯 SHRNUTÍ TESTŮ:")
    print("=" * 60)
    print(f"✅ Scénář 1 (offset 5): ${total_pnl1:.2f} ({len(trades1)} segmentů)")
    print(f"✅ Scénář 2 (offset 10): ${total_pnl2:.2f} ({len(trades2)} segmentů)")
    print(f"✅ Scénář 3 (2 dny): ${total_pnl3:.2f} ({len(trades3)} segmentů)")
    print()
    print("💡 KLÍČOVÉ FUNKCE TESTOVÁNY:")
    print("   • Uzavření Long pozic na Close každý den")
    print("   • Buy Limit na Open - offset bodů")
    print("   • Kontrola naplnění Buy Limit podle Low")
    print("   • SL na 50% předposlední svíčky")
    print("   • Žádné swapy pro Long pozice")
    print("   • Správné P&L výpočty")

if __name__ == "__main__":
    test_long_flat_limit()
