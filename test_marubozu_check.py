#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Marubozu detekce pro 4.9.2025
"""

# Data pro 4.9.2025
open_price = 3560.448
high_price = 3563.998
low_price = 3510.425
close_price = 3544.645
atr10 = 43.1813  # Z Excel reportu

def is_marubozu_test(open_val, high_val, low_val, close_val, atr10_val):
    """Test Marubozu detekce podle WebGUI logiky"""

    print(f"📊 MARUBOZU TEST - 4.9.2025 (WebGUI logika)")
    print(f"=" * 60)
    print(f"Open:  {open_val:.3f}")
    print(f"High:  {high_val:.3f}")
    print(f"Low:   {low_val:.3f}")
    print(f"Close: {close_val:.3f}")
    print(f"ATR10: {atr10_val:.4f}")
    print()

    # WebGUI logika
    is_bull = close_val >= open_val
    range_val = high_val - low_val

    print(f"Bullish: {is_bull}")
    print(f"Range: {range_val:.3f}")
    print(f"ATR10: {atr10_val:.4f}")
    print(f"Range > ATR10: {range_val > atr10_val}")
    print()

    if range_val <= atr10_val:
        print("❌ Range <= ATR10 → NENÍ Marubozu")
        return False

    # WebGUI wick ratio logika
    if is_bull:
        # Bullish: open_wick = open - low
        open_wick = open_val - low_val
    else:
        # Bearish: open_wick = high - open
        open_wick = high_val - open_val

    wick_ratio = open_wick / range_val
    wick_ratio_max = 0.18

    print(f"🔍 WEBGUI WICK RATIO TEST:")
    print(f"   Is bullish: {is_bull}")
    if is_bull:
        print(f"   Open wick (Open - Low): {open_wick:.3f}")
    else:
        print(f"   Open wick (High - Open): {open_wick:.3f}")
    print(f"   Wick ratio: {wick_ratio:.4f}")
    print(f"   Wick ratio < 0.18: {wick_ratio < wick_ratio_max}")
    print()

    # WebGUI podmínky
    condition1 = wick_ratio < wick_ratio_max
    condition2 = range_val > atr10_val

    is_marubozu = condition1 and condition2

    print(f"📋 PODMÍNKY:")
    print(f"   1. Wick ratio < 0.18: {condition1}")
    print(f"   2. Range > ATR10: {condition2}")
    print(f"   Výsledek: {is_marubozu}")

    print()
    if is_marubozu:
        print("✅ JE Marubozu (podle WebGUI)")
    else:
        print("❌ NENÍ Marubozu (podle WebGUI)")

    return is_marubozu

def main():
    print("🎯 MARUBOZU CHECK - 4.9.2025")
    print("=" * 80)
    
    result = is_marubozu_test(open_price, high_price, low_price, close_price, atr10)
    
    print(f"\n🎯 ZÁVĚR:")
    print(f"4.9.2025 {'JE' if result else 'NENÍ'} Marubozu")
    
    if not result:
        print("\n💡 Pokud NENÍ Marubozu, měla by být testována tolerance!")
        print("   Předchozí zelená (3.9.) Low: 3525.848")
        print("   Současná červená (4.9.) Close: 3544.645")
        print(f"   Test: 3544.645 >= 3525.848 = {3544.645 >= 3525.848}")
        if 3544.645 >= 3525.848:
            print("   ✅ MĚLA BY BÝT TOLEROVÁNA!")
        else:
            print("   ❌ Neměla by být tolerována")

if __name__ == "__main__":
    main()
