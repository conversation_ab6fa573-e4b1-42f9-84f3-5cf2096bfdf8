#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug test pro LONG-FLAT-LIMIT logiku
Testuje konkrétní sekvenci 19.08 - 18.09.2025 s debug výstupem
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def debug_test_august_september():
    """Debug test sekvence 19.08 - 18.09.2025"""
    
    print("🧪 DEBUG TEST LONG-FLAT-LIMIT - 19.08-18.09.2025")
    print("=" * 80)
    
    # Simulace dat pro testování
    print("📊 SIMULACE TESTOVACÍCH DAT:")
    print("=" * 80)
    
    # Vytvoříme testovací data pro období 19.08 - 18.09.2025
    dates = pd.date_range('2025-08-19', '2025-09-18', freq='D')
    
    # Simulace OHLC dat (Gold ceny kolem 2500-2600)
    np.random.seed(42)  # Pro reprodukovatelnost
    base_price = 2550.0
    
    test_data = []
    current_price = base_price
    
    for i, date in enumerate(dates):
        # Simulace denního pohybu
        daily_change = np.random.normal(0, 20)  # Průměrná volatilita 20 bodů
        
        open_price = current_price
        high_price = open_price + abs(np.random.normal(15, 10))
        low_price = open_price - abs(np.random.normal(15, 10))
        close_price = open_price + daily_change
        
        # Zajistíme, že High >= max(Open, Close) a Low <= min(Open, Close)
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        test_data.append({
            'Date': date,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Bull': close_price >= open_price,
            'Range': round(high_price - low_price, 2)
        })
        
        current_price = close_price
    
    df = pd.DataFrame(test_data)
    
    print(f"Vytvořeno {len(df)} testovacích dní")
    print(f"Cenové rozpětí: {df['Low'].min():.2f} - {df['High'].max():.2f}")
    print()
    
    # Výpočet ATR10
    print("📈 VÝPOČET ATR10:")
    print("=" * 80)
    
    ranges = df['Range'].values
    atr10 = np.zeros_like(ranges)
    
    for i in range(len(ranges)):
        if i < 10:
            atr10[i] = np.mean(ranges[:i+1])
        else:
            atr10[i] = np.mean(ranges[i-9:i+1])
    
    df['ATR10'] = atr10
    
    print(f"ATR10 rozpětí: {atr10.min():.2f} - {atr10.max():.2f}")
    print(f"Průměrné ATR10: {atr10.mean():.2f}")
    print()
    
    # Simulace Marubozu detekce
    print("🕯️ MARUBOZU DETEKCE:")
    print("=" * 80)
    
    # Jednoduchá Marubozu detekce - každý 5. den je Marubozu
    df['Is_Marubozu'] = False
    marubozu_days = [4, 9, 14, 19, 24]  # Indexy Marubozu dní
    
    for idx in marubozu_days:
        if idx < len(df):
            df.loc[idx, 'Is_Marubozu'] = True
    
    marubozu_count = df['Is_Marubozu'].sum()
    print(f"Nalezeno {marubozu_count} Marubozu svíček na dnech: {[i for i, x in enumerate(df['Is_Marubozu']) if x]}")
    print()
    
    # Simulace sekvencí
    print("🔍 SIMULACE SEKVENCÍ:")
    print("=" * 80)
    
    # Vytvoříme 2 testovací sekvence:
    # 1. UP sekvence: dny 4-8 (5 dní)
    # 2. DOWN sekvence: dny 14-18 (5 dní)
    
    sequences = [
        {
            'Start_Idx': 4,
            'End_Idx': 8,
            'Direction': 'Up',
            'Start_Date': df.iloc[4]['Date'],
            'End_Date': df.iloc[8]['Date'],
            'Length': 5
        },
        {
            'Start_Idx': 14,
            'End_Idx': 18,
            'Direction': 'Down',
            'Start_Date': df.iloc[14]['Date'],
            'End_Date': df.iloc[18]['Date'],
            'Length': 5
        }
    ]
    
    print(f"Vytvořeno {len(sequences)} testovacích sekvencí:")
    for i, seq in enumerate(sequences, 1):
        print(f"  {i}. {seq['Direction']} sekvence: den {seq['Start_Idx']}-{seq['End_Idx']} "
              f"({seq['Start_Date'].strftime('%Y-%m-%d')} - {seq['End_Date'].strftime('%Y-%m-%d')})")
    print()
    
    # Test LONG-FLAT-LIMIT logiky
    print("💹 TEST LONG-FLAT-LIMIT LOGIKY:")
    print("=" * 80)
    
    # Parametry
    start_equity = 10000.0
    risk_pct = 0.02  # 2%
    rrr = 6.0
    buy_limit_offset = 5
    USD_PER_BOD_PER_LOT = 100.0
    
    O = df['Open'].values
    H = df['High'].values
    L = df['Low'].values
    C = df['Close'].values
    ATR = df['ATR10'].values
    
    trades = []
    equity = start_equity
    
    for seq_idx, seq in enumerate(sequences):
        s = seq['Start_Idx']
        e = seq['End_Idx']
        is_up = (seq['Direction'] == 'Up')
        atr10 = ATR[s]
        
        print(f"\n🔹 SEKVENCE {seq_idx+1}: {seq['Direction']} ({seq['Start_Date'].strftime('%Y-%m-%d')} - {seq['End_Date'].strftime('%Y-%m-%d')})")
        print(f"   Start_Idx: {s}, End_Idx: {e}, ATR10: {atr10:.2f}")
        
        # Position sizing
        risk_usd = equity * risk_pct
        risk_per_lot = atr10 * USD_PER_BOD_PER_LOT
        lots = risk_usd / risk_per_lot
        lots = max(0.01, min(100.0, round(lots, 2)))
        
        print(f"   💰 Position sizing: equity=${equity:.0f}, risk={risk_pct*100}%, lots={lots}")
        
        entry = O[s]
        stop = (entry - atr10) if is_up else (entry + atr10)
        tp = entry + (rrr * atr10) if is_up else entry - (rrr * atr10)
        
        print(f"   📊 Entry: {entry:.2f}, SL: {stop:.2f}, TP: {tp:.2f}")
        
        if is_up:
            # LONG pozice - LONG-FLAT-LIMIT
            print(f"   📈 LONG pozice - LONG-FLAT-LIMIT simulace")
            
            # Simulace LONG-FLAT-LIMIT logiky
            current_day = s
            current_entry = entry
            position_active = True
            total_pnl_bod = 0
            segments = []
            final_exit_reason = ""
            
            while current_day <= e and position_active:
                hi, lo, cl = H[current_day], L[current_day], C[current_day]
                
                print(f"      📅 Den {current_day} ({df.iloc[current_day]['Date'].strftime('%Y-%m-%d')}):")
                print(f"         Entry: {current_entry:.2f}, OHLC: {O[current_day]:.2f}/{hi:.2f}/{lo:.2f}/{cl:.2f}")
                print(f"         SL: {stop:.2f}, TP: {tp:.2f}")
                
                # Kontrola TP/SL během dne
                hit_tp = hi >= tp
                hit_sl = lo <= stop
                
                if hit_tp and hit_sl:
                    exit_px = tp  # Optimistic
                    reason = "TP (same-day)"
                    position_active = False
                    print(f"         ✅ TP a SL hit - exit na TP: {exit_px:.2f}")
                elif hit_tp:
                    exit_px = tp
                    reason = "TP"
                    position_active = False
                    print(f"         ✅ TP hit: {exit_px:.2f}")
                elif hit_sl:
                    exit_px = stop
                    reason = "SL"
                    position_active = False
                    print(f"         ❌ SL hit: {exit_px:.2f}")
                else:
                    # Pozice přežila den - uzavřeme na Close
                    exit_px = cl
                    if current_day == e:
                        reason = "SEQ_END"
                        position_active = False
                        print(f"         📅 Konec sekvence - exit na Close: {exit_px:.2f}")
                    else:
                        reason = "EOD_CLOSE"
                        print(f"         🌅 EOD Close: {exit_px:.2f}")
                
                # Výpočet P&L pro tento segment
                segment_pnl_bod = exit_px - current_entry
                total_pnl_bod += segment_pnl_bod
                
                print(f"         💰 Segment P&L: {segment_pnl_bod:.2f} bodů")
                
                segments.append({
                    "day": current_day,
                    "entry": current_entry,
                    "exit": exit_px,
                    "pnl_bod": segment_pnl_bod,
                    "reason": reason
                })
                
                final_exit_reason = reason
                
                if position_active and current_day < e:
                    # Pokračujeme další den s Buy Limit
                    next_day = current_day + 1
                    if next_day <= e:
                        next_open = O[next_day]
                        buy_limit_price = next_open - buy_limit_offset
                        next_low = L[next_day]
                        
                        print(f"         📋 Další den Buy Limit: {buy_limit_price:.2f} (Open: {next_open:.2f}, Low: {next_low:.2f})")
                        
                        if next_low <= buy_limit_price:
                            # Buy Limit se naplnil
                            current_entry = buy_limit_price
                            current_day = next_day
                            
                            # Aktualizace SL na 50% předposlední svíčky
                            if current_day > 0:
                                prev_range = H[current_day-1] - L[current_day-1]
                                old_stop = stop
                                stop = current_entry - (prev_range * 0.5)
                                print(f"         🔄 SL aktualizace: {old_stop:.2f} → {stop:.2f} (50% z {prev_range:.2f})")
                            
                            final_exit_reason += " -> BUY_LIMIT_FILLED"
                            print(f"         ✅ Buy Limit naplněn na {buy_limit_price:.2f}")
                        else:
                            # Buy Limit se nenaplnil
                            final_exit_reason += " -> BUY_LIMIT_NOT_FILLED"
                            position_active = False
                            print(f"         ❌ Buy Limit nenaplněn (Low {next_low:.2f} > Limit {buy_limit_price:.2f})")
                    else:
                        position_active = False
                else:
                    position_active = False
            
            # Vytvoření konsolidovaného trade záznamu
            total_pnl_usd = total_pnl_bod * USD_PER_BOD_PER_LOT * lots
            net_pnl = total_pnl_usd  # Bez swapů pro LONG-FLAT-LIMIT
            
            print(f"\n      📊 KONSOLIDOVANÝ VÝSLEDEK:")
            print(f"         Celkem segmentů: {len(segments)}")
            print(f"         Celkový P&L: {total_pnl_bod:.2f} bodů = ${total_pnl_usd:.2f}")
            print(f"         Net P&L: ${net_pnl:.2f}")
            print(f"         Finální exit reason: {final_exit_reason}")
            
            trade = {
                "Start_Date": seq['Start_Date'].strftime("%Y-%m-%d"),
                "End_Date": seq['End_Date'].strftime("%Y-%m-%d"),
                "Direction": "Long",
                "Lots": lots,
                "Entry_Price": entry,
                "Exit_Price": segments[-1]["exit"] if segments else entry,
                "Stop_Loss": stop,
                "Take_Profit": tp,
                "Exit_Reason": final_exit_reason,
                "PnL_Bod": total_pnl_bod,
                "PnL_USD": total_pnl_usd,
                "Net_PnL": net_pnl,
                "Segments": len(segments)
            }
            
            equity += net_pnl
            trade["Equity_After"] = equity
            trades.append(trade)
            
        else:
            # SHORT pozice - standardní logika (pro porovnání)
            print(f"   📉 SHORT pozice - standardní logika (přeskočeno pro tento test)")
    
    # Finální výsledky
    print(f"\n🎯 FINÁLNÍ VÝSLEDKY:")
    print("=" * 80)
    print(f"Počet sekvencí: {len(sequences)}")
    print(f"Počet obchodů: {len(trades)}")
    print(f"Počáteční kapitál: ${start_equity:,.0f}")
    print(f"Finální kapitál: ${equity:,.0f}")
    
    if trades:
        total_pnl = sum(t["Net_PnL"] for t in trades)
        print(f"Celkový P&L: ${total_pnl:,.0f}")
        
        print(f"\n📋 DETAILNÍ SEZNAM OBCHODŮ:")
        for i, trade in enumerate(trades, 1):
            print(f"{i}. {trade['Direction']} {trade['Start_Date']} - {trade['End_Date']}")
            print(f"   Entry: {trade['Entry_Price']:.2f}, Exit: {trade['Exit_Price']:.2f}")
            print(f"   SL: {trade['Stop_Loss']:.2f}, TP: {trade['Take_Profit']:.2f}")
            print(f"   Exit Reason: {trade['Exit_Reason']}")
            print(f"   P&L: ${trade['Net_PnL']:.2f}, Segments: {trade['Segments']}")
            print(f"   Equity After: ${trade['Equity_After']:.2f}")
            print()
    
    # Analýza problémů
    print(f"🔍 ANALÝZA PROBLÉMŮ:")
    print("=" * 80)
    
    long_trades = [t for t in trades if t['Direction'] == 'Long']
    
    if len(long_trades) == 0:
        print("❌ PROBLÉM: Žádné LONG obchody nebyly vytvořeny!")
        print("   Možné příčiny:")
        print("   • Chyba v position sizing")
        print("   • Chyba v sekvence detekci")
        print("   • Chyba v LONG-FLAT-LIMIT logice")
    elif len(long_trades) == 1:
        print("⚠️  MOŽNÝ PROBLÉM: Jen jeden LONG obchod vytvořen")
        print("   Očekávali jsme více obchodů pro více sekvencí")
        print("   Zkontrolujte konsolidační logiku")
    else:
        print("✅ SPRÁVNĚ: Více LONG obchodů vytvořeno")
    
    print(f"\nPočet UP sekvencí: {len([s for s in sequences if s['Direction'] == 'Up'])}")
    print(f"Počet LONG obchodů: {len(long_trades)}")
    
    if len(long_trades) > 0:
        print(f"\nSL ANALÝZA:")
        for i, trade in enumerate(long_trades, 1):
            print(f"  Obchod {i}: SL = {trade['Stop_Loss']:.2f}")
            if 'BUY_LIMIT_FILLED' in trade['Exit_Reason']:
                print(f"    ✅ SL byla aktualizována (Buy Limit naplněn)")
            else:
                print(f"    ⚠️  SL nebyla aktualizována (Buy Limit nenaplněn)")
    
    print(f"\n🎯 ZÁVĚR TESTU:")
    print("=" * 80)
    print("Test dokončen. Zkontrolujte výsledky výše pro identifikaci problémů.")

if __name__ == "__main__":
    debug_test_august_september()
