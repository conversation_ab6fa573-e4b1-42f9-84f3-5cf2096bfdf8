#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test pro ov<PERSON><PERSON><PERSON><PERSON> Tolerant logiky - konk<PERSON>tně scénář:
Zelená sekvence začala 20.8.2025, skončila kvůli červené 4.9.2025
O<PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON> svíčka 4.9. close př<PERSON><PERSON><PERSON><PERSON> s<PERSON>?
"""

import pandas as pd
import numpy as np

# Testovací data z XAUUSD kolem 20.8.2025 - 4.9.2025
test_data = [
    # Datum, Open, High, Low, Close
    ("2025.08.20", 3315.465, 3350.198, 3311.255, 3347.335),  # Zelená - začátek sekvence?
    ("2025.08.21", 3349.098, 3351.935, 3324.948, 3338.935),  # <PERSON>erve<PERSON>
    ("2025.08.22", 3338.405, 3378.585, 3321.045, 3371.235),  # <PERSON><PERSON><PERSON>
    ("2025.08.25", 3370.835, 3376.135, 3359.495, 3365.605),  # <PERSON><PERSON><PERSON>
    ("2025.08.26", 3365.135, 3393.495, 3351.175, 3393.465),  # <PERSON><PERSON><PERSON>
    ("2025.08.27", 3392.648, 3398.569, 3373.665, 3396.495),  # Zele<PERSON>
    ("2025.08.28", 3397.835, 3423.069, 3384.445, 3415.795),  # Zelená
    ("2025.08.29", 3416.965, 3453.565, 3404.185, 3446.805),  # Zelená
    ("2025.09.01", 3445.648, 3489.595, 3436.548, 3476.225),  # Zelená
    ("2025.09.02", 3476.505, 3539.849, 3469.805, 3532.405),  # Zelená
    ("2025.09.03", 3532.698, 3578.175, 3525.848, 3558.475),  # Zelená
    ("2025.09.04", 3560.448, 3563.998, 3510.425, 3544.645),  # Červená - ukončila sekvenci?
    ("2025.09.05", 3545.935, 3599.905, 3539.715, 3585.195),  # Zelená
]

def analyze_candles():
    """Analyzuje svíčky a určí barvy"""
    print("🔍 ANALÝZA SVÍČEK:")
    print("=" * 80)
    
    for i, (date, o, h, l, c) in enumerate(test_data):
        is_green = c >= o
        color = "🟢 ZELENÁ" if is_green else "🔴 ČERVENÁ"
        print(f"{i+1:2d}. {date}: O={o:7.3f} H={h:7.3f} L={l:7.3f} C={c:7.3f} → {color}")
    
    print()

def test_tolerant_logic():
    """Testuje Tolerant logiku podle implementace z WebGUI"""
    print("🧪 TEST TOLERANT LOGIKY:")
    print("=" * 80)
    
    # Simulace Tolerant logiky
    on = False
    dir_bull = None
    order = 0
    last_dir_idx = None
    
    results = []
    
    for i, (date, o, h, l, c) in enumerate(test_data):
        day_bull = c >= o
        series_order = 0
        tolerated = False
        
        if on:
            if dir_bull:  # UP sekvence
                if day_bull:
                    # Zelená svíčka v UP sekvenci - pokračuje
                    order += 1
                    last_dir_idx = i
                    series_order = order
                    print(f"  {i+1:2d}. {date}: 🟢 UP sekvence pokračuje (order={order})")
                else:
                    # Červená svíčka v UP sekvenci - kontrola tolerance
                    if last_dir_idx is not None:
                        last_low = test_data[last_dir_idx][3]  # Low předchozí zelené (index 3 = Low)
                        current_close = c

                        print(f"  {i+1:2d}. {date}: 🔴 Červená v UP sekvenci")
                        print(f"      Předchozí zelená Low: {last_low:.3f}")
                        print(f"      Aktuální červená Close: {current_close:.3f}")
                        print(f"      Kontrola: {current_close:.3f} >= {last_low:.3f} ?")
                        
                        if current_close >= last_low:
                            # Tolerováno
                            order += 1
                            series_order = order
                            tolerated = True
                            print(f"      ✅ TOLEROVÁNO - sekvence pokračuje (order={order})")
                        else:
                            # Ukončeno
                            on = False
                            dir_bull = None
                            order = 0
                            last_dir_idx = None
                            print(f"      ❌ UKONČENO - prolomila předchozí close")
                    else:
                        on = False
                        dir_bull = None
                        order = 0
                        last_dir_idx = None
                        print(f"  {i+1:2d}. {date}: 🔴 Ukončeno (no last_dir_idx)")
        else:
            # Žádná sekvence - hledáme začátek
            if day_bull:
                print(f"  {i+1:2d}. {date}: 🟢 Možný začátek UP sekvence")
                # V reálné implementaci by se kontrolovalo is_marubozu
                # Pro test předpokládáme, že 20.8. je Marubozu
                if date == "2025.08.20":
                    on = True
                    dir_bull = True
                    order = 1
                    last_dir_idx = i
                    series_order = 1
                    print(f"      ✅ Začátek UP sekvence (předpokládáme Marubozu)")
            else:
                print(f"  {i+1:2d}. {date}: 🔴 Červená - žádná sekvence")
        
        results.append({
            'date': date,
            'color': '🟢' if day_bull else '🔴',
            'on': on,
            'dir_bull': dir_bull,
            'series_order': series_order,
            'tolerated': tolerated
        })
    
    return results

def main():
    print("🎯 TEST TOLERANT LOGIKY - Scénář 20.8.2025 - 4.9.2025")
    print("=" * 80)
    print()
    
    analyze_candles()
    results = test_tolerant_logic()
    
    print()
    print("📊 SHRNUTÍ VÝSLEDKŮ:")
    print("=" * 80)
    
    for r in results:
        status = f"Order={r['series_order']}" if r['series_order'] > 0 else "Mimo sekvenci"
        tolerance = " (TOLEROVÁNO)" if r['tolerated'] else ""
        print(f"{r['date']}: {r['color']} - {status}{tolerance}")
    
    print()
    print("🔍 KLÍČOVÁ OTÁZKA:")
    print("Červená svíčka 4.9.2025:")
    print(f"  Close: 3544.645")
    print(f"  Předchozí zelená (3.9.) Low: 3525.848")
    print(f"  Kontrola: 3544.645 >= 3525.848 ? → {'✅ ANO' if 3544.645 >= 3525.848 else '❌ NE'}")
    print()

    if 3544.645 >= 3525.848:
        print("✅ ZÁVĚR: Červená 4.9. NEPROLOMILA předchozí Low → měla být TOLEROVÁNA")
        print("   Pokud byla sekvence ukončena, je to BUG v logice!")
    else:
        print("❌ ZÁVĚR: Červená 4.9. PROLOMILA předchozí Low → správně ukončila sekvenci")

if __name__ == "__main__":
    main()
