import { NextRequest, NextResponse } from 'next/server';
import { getAvailableD1Files, getDataFileInfo } from '@/lib/dataUtils';

export async function GET(): Promise<NextResponse> {
  try {
    const availableFiles = getAvailableD1Files();
    
    if (availableFiles.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Nenalezeny žádné D1 datové soubory v adresáři C:\\Temp\\Trading\\DATA ICM',
        files: []
      });
    }
    
    // Získá základní informace o každém souboru
    const filesWithInfo = await Promise.all(
      availableFiles.map(async (file) => {
        const info = await getDataFileInfo(file.fullPath);
        return {
          filename: file.filename,
          symbol: file.symbol,
          timeframe: file.timeframe,
          isValid: file.isValid && info.success,
          startDate: info.startDate,
          endDate: info.endDate,
          totalRecords: info.totalRecords,
          error: info.error
        };
      })
    );
    
    // Filtruje pouze platné soubory
    const validFiles = filesWithInfo.filter(file => file.isValid);
    
    return NextResponse.json({
      success: true,
      files: validFiles,
      totalFiles: availableFiles.length,
      validFiles: validFiles.length
    });
    
  } catch (error) {
    console.error('Error loading data files:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Neznámá chyba při načítání datových souborů',
      files: []
    });
  }
}
