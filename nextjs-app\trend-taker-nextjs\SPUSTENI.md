# 🚀 Spuštění Trend Taker Next.js

## 📋 <PERSON><PERSON><PERSON> spuštění

### Windows
```bash
# Dvojklik na soubor nebo spuštění v cmd
start.bat
```

### Linux/Mac
```bash
# Udělení práv a spuštění
chmod +x start.sh
./start.sh
```

### Manuální spuštění
```bash
# Instalace závislostí
npm install

# Spuštění vývojového serveru
npm run dev
```

## 🌐 Přístup k aplikaci

Po spuštění bude aplikace dostupná na:
**http://localhost:3000**

## 📁 Požadované soubory

Před spuštěním zkopírujte datový soubor do root složky:
- `XAUUSD_GMT+2_US-DST_D1.csv` (hlavní datový soubor)
- Nebo použijte ukázkový: `XAUUSD_GMT+2_US-DST_D1_sample.csv`

## ✅ Ko<PERSON><PERSON><PERSON><PERSON> se<PERSON>

- [ ] Node.js 18+ nainstalován
- [ ] Datový soubor zkopírov<PERSON>
- [ ] Závislosti nainstalovány (`npm install`)
- [ ] Server spuštěn (`npm run dev`)
- [ ] Aplikace otevřena v prohlížeči

## 🔧 Řešení problémů

### Port 3000 je obsazen
```bash
# Změňte port v package.json nebo ukončete proces
netstat -ano | findstr :3000  # Windows
lsof -ti:3000 | xargs kill -9  # Linux/Mac
```

### Chyba při instalaci
```bash
# Vymazání cache a reinstalace
rm -rf node_modules package-lock.json
npm install
```

### Datový soubor nenalezen
- Zkontrolujte, že je soubor v root složce projektu
- Název musí být přesně: `XAUUSD_GMT+2_US-DST_D1.csv`
- Pro testování můžete přejmenovat ukázkový soubor

## 📊 Funkce aplikace

- **Single Test** - Test jednotlivých parametrů
- **Grid Test** - Kompletní grid test všech kombinací
- **Excel Export** - Stahování výsledků
- **HeatMap** - Vizualizace grid testů

## 🛑 Ukončení

Pro ukončení serveru stiskněte **Ctrl+C** v terminálu.
