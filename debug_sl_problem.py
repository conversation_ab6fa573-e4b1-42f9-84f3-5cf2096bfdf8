#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug SL problému - proč končí 26.08.2025 na SL
Analýza Buy Limit SL nastavení
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def debug_sl_problem():
    """Debug SL problému pro období 19.08 - 18.09.2025"""
    
    print("🔍 DEBUG SL PROBLÉMU - 19.08-18.09.2025")
    print("=" * 80)
    
    # Simulace reálných dat pro období kolem 26.08.2025
    print("📊 SIMULACE REÁLNÝCH DAT PRO ANALÝZU:")
    print("=" * 80)
    
    # Vytvoříme testovací data s reálnými cenami Gold kolem 2500-2600
    dates = pd.date_range('2025-08-19', '2025-09-18', freq='D')
    
    # Simulace dat s trendem nahoru (UP sekvence)
    test_data = []
    base_price = 2550.0
    
    # Simulace UP sekvence od 23.08 do 27.08 (5 dní)
    for i, date in enumerate(dates):
        if i < 4:  # Před sekvencí
            open_price = base_price + np.random.normal(0, 10)
            high_price = open_price + abs(np.random.normal(15, 5))
            low_price = open_price - abs(np.random.normal(15, 5))
            close_price = open_price + np.random.normal(0, 8)
        elif 4 <= i <= 8:  # UP sekvence (23.08 - 27.08)
            # Rostoucí trend
            trend_boost = (i - 4) * 15  # +15 bodů každý den
            open_price = base_price + trend_boost + np.random.normal(0, 5)
            high_price = open_price + abs(np.random.normal(20, 5))
            low_price = open_price - abs(np.random.normal(10, 3))  # Menší poklesy
            close_price = open_price + np.random.normal(10, 5)  # Tendence růstu
        else:  # Po sekvenci
            open_price = base_price + 60 + np.random.normal(0, 10)
            high_price = open_price + abs(np.random.normal(15, 5))
            low_price = open_price - abs(np.random.normal(15, 5))
            close_price = open_price + np.random.normal(0, 8)
        
        # Zajistíme správné OHLC vztahy
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        test_data.append({
            'Date': date,
            'Open': round(open_price, 2),
            'High': round(high_price, 2),
            'Low': round(low_price, 2),
            'Close': round(close_price, 2),
            'Range': round(high_price - low_price, 2)
        })
    
    df = pd.DataFrame(test_data)
    
    # Výpočet ATR10
    ranges = df['Range'].values
    atr10 = np.zeros_like(ranges)
    
    for i in range(len(ranges)):
        if i < 10:
            atr10[i] = np.mean(ranges[:i+1])
        else:
            atr10[i] = np.mean(ranges[i-9:i+1])
    
    df['ATR10'] = atr10
    
    print("📋 TESTOVACÍ DATA (klíčové dny):")
    key_days = df.iloc[3:10]  # Dny kolem sekvence
    for _, row in key_days.iterrows():
        print(f"{row['Date'].strftime('%Y-%m-%d')}: O={row['Open']:.2f} H={row['High']:.2f} L={row['Low']:.2f} C={row['Close']:.2f} ATR10={row['ATR10']:.2f}")
    print()
    
    # Simulace LONG-FLAT-LIMIT pro UP sekvenci (dny 4-8)
    print("💹 SIMULACE LONG-FLAT-LIMIT PROBLÉMU:")
    print("=" * 80)
    
    s, e = 4, 8  # Sekvence indexy
    atr10_start = atr10[s]
    buy_limit_offset = 5
    
    # Parametry
    entry = df.iloc[s]['Open']
    initial_stop = entry - atr10_start
    tp = entry + (6.0 * atr10_start)  # RRR = 6.0
    
    print(f"🔹 SEKVENCE PARAMETRY:")
    print(f"   Start den: {s} ({df.iloc[s]['Date'].strftime('%Y-%m-%d')})")
    print(f"   End den: {e} ({df.iloc[e]['Date'].strftime('%Y-%m-%d')})")
    print(f"   Entry: {entry:.2f}")
    print(f"   Initial SL: {initial_stop:.2f}")
    print(f"   TP: {tp:.2f}")
    print(f"   ATR10: {atr10_start:.2f}")
    print()
    
    # Simulace den po dni
    current_day = s
    current_entry = entry
    current_stop = initial_stop
    position_active = True
    segments = []
    
    O = df['Open'].values
    H = df['High'].values
    L = df['Low'].values
    C = df['Close'].values
    
    while current_day <= e and position_active:
        hi, lo, cl = H[current_day], L[current_day], C[current_day]
        date_str = df.iloc[current_day]['Date'].strftime('%Y-%m-%d')
        
        print(f"📅 DEN {current_day} ({date_str}):")
        print(f"   Current Entry: {current_entry:.2f}")
        print(f"   Current SL: {current_stop:.2f}")
        print(f"   OHLC: {O[current_day]:.2f}/{hi:.2f}/{lo:.2f}/{cl:.2f}")
        
        # Kontrola TP/SL během dne
        hit_tp = hi >= tp
        hit_sl = lo <= current_stop
        
        print(f"   TP check: {hi:.2f} >= {tp:.2f} = {hit_tp}")
        print(f"   SL check: {lo:.2f} <= {current_stop:.2f} = {hit_sl}")
        
        if hit_tp and hit_sl:
            exit_px = tp  # Optimistic
            reason = "TP (same-day)"
            position_active = False
            print(f"   ✅ TP a SL hit - exit na TP: {exit_px:.2f}")
        elif hit_tp:
            exit_px = tp
            reason = "TP"
            position_active = False
            print(f"   ✅ TP hit: {exit_px:.2f}")
        elif hit_sl:
            exit_px = current_stop
            reason = "SL"
            position_active = False
            print(f"   ❌ SL hit: {exit_px:.2f}")
        else:
            # Pozice přežila den - uzavřeme na Close
            exit_px = cl
            if current_day == e:
                reason = "SEQ_END"
                position_active = False
                print(f"   📅 Konec sekvence - exit na Close: {exit_px:.2f}")
            else:
                reason = "EOD_CLOSE"
                print(f"   🌅 EOD Close: {exit_px:.2f}")
        
        # Výpočet P&L pro tento segment
        segment_pnl_bod = exit_px - current_entry
        
        print(f"   💰 Segment P&L: {segment_pnl_bod:.2f} bodů")
        
        segments.append({
            "day": current_day,
            "date": date_str,
            "entry": current_entry,
            "exit": exit_px,
            "pnl_bod": segment_pnl_bod,
            "reason": reason,
            "sl_used": current_stop
        })
        
        if position_active and current_day < e:
            # Pokračujeme další den s Buy Limit
            next_day = current_day + 1
            if next_day <= e:
                next_open = O[next_day]
                buy_limit_price = next_open - buy_limit_offset
                next_low = L[next_day]
                next_date = df.iloc[next_day]['Date'].strftime('%Y-%m-%d')
                
                print(f"   📋 Další den ({next_date}) Buy Limit setup:")
                print(f"      Next Open: {next_open:.2f}")
                print(f"      Buy Limit: {buy_limit_price:.2f} (Open - {buy_limit_offset})")
                print(f"      Next Low: {next_low:.2f}")
                
                if next_low <= buy_limit_price:
                    # Buy Limit se naplnil
                    current_entry = buy_limit_price
                    current_day = next_day
                    
                    # KRITICKÉ: Aktualizace SL na 50% předposlední svíčky
                    if current_day > 0:
                        prev_range = H[current_day-1] - L[current_day-1]
                        old_stop = current_stop
                        current_stop = current_entry - (prev_range * 0.5)
                        
                        print(f"      🔄 KRITICKÁ SL AKTUALIZACE:")
                        print(f"         Předposlední range: {prev_range:.2f}")
                        print(f"         50% z range: {prev_range * 0.5:.2f}")
                        print(f"         Old SL: {old_stop:.2f}")
                        print(f"         New SL: {current_stop:.2f}")
                        print(f"         SL změna: {current_stop - old_stop:+.2f} bodů")
                        
                        # ANALÝZA PROBLÉMU
                        if current_stop > old_stop:
                            print(f"         ⚠️  SL se ZVÝŠIL - může způsobit předčasný exit!")
                        else:
                            print(f"         ✅ SL se snížil - normální")
                    
                    print(f"      ✅ Buy Limit naplněn na {buy_limit_price:.2f}")
                else:
                    # Buy Limit se nenaplnil
                    position_active = False
                    print(f"      ❌ Buy Limit nenaplněn (Low {next_low:.2f} > Limit {buy_limit_price:.2f})")
            else:
                position_active = False
        else:
            position_active = False
        
        print()
        
        if not position_active:
            break
    
    # Analýza výsledků
    print("🔍 ANALÝZA PROBLÉMU:")
    print("=" * 80)
    
    print("📋 DETAILNÍ SEGMENTY:")
    total_pnl = 0
    for i, seg in enumerate(segments, 1):
        total_pnl += seg['pnl_bod']
        print(f"Segment {i}: {seg['date']}")
        print(f"  Entry: {seg['entry']:.2f}, Exit: {seg['exit']:.2f}")
        print(f"  SL used: {seg['sl_used']:.2f}")
        print(f"  P&L: {seg['pnl_bod']:.2f} bodů")
        print(f"  Reason: {seg['reason']}")
        print()
    
    print(f"📊 CELKOVÉ VÝSLEDKY:")
    print(f"Celkem segmentů: {len(segments)}")
    print(f"Celkový P&L: {total_pnl:.2f} bodů")
    print(f"Poslední exit: {segments[-1]['date']} na {segments[-1]['reason']}")
    
    # Identifikace problému
    print(f"\n🚨 IDENTIFIKACE PROBLÉMU:")
    print("=" * 80)
    
    sl_segments = [s for s in segments if s['reason'] == 'SL']
    if sl_segments:
        sl_seg = sl_segments[0]
        print(f"❌ PROBLÉM NALEZEN:")
        print(f"   SL hit na: {sl_seg['date']}")
        print(f"   SL hodnota: {sl_seg['sl_used']:.2f}")
        print(f"   Entry byla: {sl_seg['entry']:.2f}")
        print(f"   Rozdíl: {sl_seg['entry'] - sl_seg['sl_used']:.2f} bodů")
        
        # Analýza SL aktualizace
        if len(segments) > 1:
            prev_seg = segments[-2]
            print(f"\n   ANALÝZA SL AKTUALIZACE:")
            print(f"   Předchozí SL: {prev_seg['sl_used']:.2f}")
            print(f"   Aktuální SL: {sl_seg['sl_used']:.2f}")
            print(f"   SL změna: {sl_seg['sl_used'] - prev_seg['sl_used']:+.2f}")
            
            if sl_seg['sl_used'] > prev_seg['sl_used']:
                print(f"   🚨 PROBLÉM: SL se ZVÝŠIL při Buy Limit!")
                print(f"   🔧 MOŽNÉ ŘEŠENÍ: Kontrolovat výpočet 50% předposlední svíčky")
            else:
                print(f"   ✅ SL se snížil normálně")
    else:
        print(f"✅ Žádný SL hit nenalezen")
    
    print(f"\n🎯 DOPORUČENÍ PRO OPRAVU:")
    print("=" * 80)
    print("1. 🔍 Zkontrolovat výpočet SL při Buy Limit aktualizaci")
    print("2. 📊 Ověřit, že se používá správná předposlední svíčka")
    print("3. 🧮 Možná použít jiný algoritmus pro SL aktualizaci")
    print("4. 📋 Přidat detailní Excel export jednotlivých segmentů")

if __name__ == "__main__":
    debug_sl_problem()
