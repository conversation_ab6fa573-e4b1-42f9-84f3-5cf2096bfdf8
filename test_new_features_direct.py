#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Přímý test nových funkcí bez WebGUI
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Přidání cesty k modulu
sys.path.append('python-webgui')

def test_new_features():
    """Test nových funkcí přímo"""
    
    print("🧪 PŘÍMÝ TEST NOVÝCH FUNKCÍ")
    print("=" * 80)
    
    try:
        # Import TrendTakerWebHandler
        print("📥 Načítání modulu...")
        
        # Načtení kódu
        with open('python-webgui/Trend Taker Web GUI.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Vytvoření namespace
        namespace = {}
        exec(code, namespace)
        
        # <PERSON>ískán<PERSON> třídy
        TrendTakerWebHandler = namespace['TrendTakerWebHandler']
        
        print("✅ Modul načten")
        
        # Vytvoření mock objektu s potřebnými konstantami
        class TestHandler:
            def __init__(self):
                self.USD_PER_BOD_PER_LOT = 100.0
                self.SPREAD_BOD = 0.15
                self.SWAP_LONG_BOD = -0.55237
                self.SWAP_SHORT_BOD = 0.29425
                self.WICK_RATIO_MAX = 0.18
                self.MIN_BODY_RATIO = 0.60
                self.ATR_PERIOD = 10
                
                # Přiřazení všech potřebných metod
                for attr_name in dir(TrendTakerWebHandler):
                    if not attr_name.startswith('_') and hasattr(TrendTakerWebHandler, attr_name):
                        attr = getattr(TrendTakerWebHandler, attr_name)
                        if callable(attr) and not isinstance(attr, type):
                            try:
                                setattr(self, attr_name, attr.__get__(self, TestHandler))
                            except AttributeError:
                                pass  # Ignorujeme atributy, které nelze přiřadit
        
        handler = TestHandler()
        
        print("✅ Handler vytvořen")
        
        # Test parametry
        test_cases = [
            {
                "name": "Initial SL Method",
                "params": {
                    'dataFile': 'XAUUSD_GMT+2_US-DST_D1.csv',
                    'dataRange': 'CUSTOM',
                    'startDate': '2025-08-19',
                    'endDate': '2025-09-18',
                    'variant': 'Tolerant',
                    'rrr': 6.0,
                    'exitPolicy': 'cons',
                    'startEquity': 10000,
                    'riskPct': 2.0,
                    'overnightMode': 'LONG-FLAT-LIMIT',
                    'buyLimitOffset': 5,
                    'minBodyPct': 5.0,
                    'longLmtSlMethod': 'Initial',
                    'barsBack': 3
                }
            },
            {
                "name": "BarsBack Low SL Method",
                "params": {
                    'dataFile': 'XAUUSD_GMT+2_US-DST_D1.csv',
                    'dataRange': 'CUSTOM',
                    'startDate': '2025-08-19',
                    'endDate': '2025-09-18',
                    'variant': 'Tolerant',
                    'rrr': 6.0,
                    'exitPolicy': 'cons',
                    'startEquity': 10000,
                    'riskPct': 2.0,
                    'overnightMode': 'LONG-FLAT-LIMIT',
                    'buyLimitOffset': 5,
                    'minBodyPct': 5.0,
                    'longLmtSlMethod': 'BarsBack_Low',
                    'barsBack': 3
                }
            },
            {
                "name": "BarsBack 50% SL Method",
                "params": {
                    'dataFile': 'XAUUSD_GMT+2_US-DST_D1.csv',
                    'dataRange': 'CUSTOM',
                    'startDate': '2025-08-19',
                    'endDate': '2025-09-18',
                    'variant': 'Tolerant',
                    'rrr': 6.0,
                    'exitPolicy': 'cons',
                    'startEquity': 10000,
                    'riskPct': 2.0,
                    'overnightMode': 'LONG-FLAT-LIMIT',
                    'buyLimitOffset': 5,
                    'minBodyPct': 5.0,
                    'longLmtSlMethod': 'BarsBack_50pct',
                    'barsBack': 2
                }
            }
        ]
        
        results_summary = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 TEST {i}: {test_case['name']}")
            print("=" * 60)
            
            try:
                # Spuštění testu
                results, excel_file = handler.run_trend_taker_test(test_case['params'])
                
                print(f"✅ Test dokončen")
                print(f"📊 Excel soubor: {excel_file}")
                
                # Analýza výsledků
                if excel_file and os.path.exists(excel_file):
                    analysis = analyze_test_results(excel_file, test_case['name'])
                    results_summary.append(analysis)
                else:
                    print(f"❌ Excel soubor nenalezen")
                    results_summary.append({
                        'test_name': test_case['name'],
                        'success': False,
                        'error': 'Excel soubor nenalezen'
                    })
                
            except Exception as e:
                print(f"❌ Chyba v testu: {e}")
                results_summary.append({
                    'test_name': test_case['name'],
                    'success': False,
                    'error': str(e)
                })
        
        # Finální shrnutí
        print(f"\n🎯 FINÁLNÍ SHRNUTÍ")
        print("=" * 80)
        
        success_count = 0
        for result in results_summary:
            if result['success']:
                print(f"✅ {result['test_name']}: ÚSPĚCH")
                success_count += 1
            else:
                print(f"❌ {result['test_name']}: CHYBA - {result.get('error', 'Neznámá chyba')}")
        
        total_tests = len(results_summary)
        print(f"\n📈 CELKOVÉ SKÓRE: {success_count}/{total_tests} ({success_count/total_tests*100:.0f}%)")
        
        if success_count == total_tests:
            print("🎉 VŠECHNY TESTY ÚSPĚŠNÉ!")
            return True
        elif success_count > 0:
            print("⚠️  ČÁSTEČNÝ ÚSPĚCH")
            return False
        else:
            print("❌ VŠECHNY TESTY SELHALY")
            return False
    
    except Exception as e:
        print(f"❌ Kritická chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_test_results(excel_file, test_name):
    """Analýza výsledků testu"""
    
    try:
        import openpyxl
        wb = openpyxl.load_workbook(excel_file)
        
        analysis = {
            'test_name': test_name,
            'success': True,
            'trades_count': 0,
            'buy_limit_info': False,
            'sl_calc_present': False,
            'seq_end_detailed': False,
            'new_sl_method': False
        }
        
        if 'Obchody' in wb.sheetnames:
            ws = wb['Obchody']
            analysis['trades_count'] = ws.max_row - 1
            
            if ws.max_row > 1:
                # Získání headers
                headers = {}
                for col in range(1, ws.max_column + 1):
                    header = ws.cell(row=1, column=col).value
                    if header:
                        headers[header] = col
                
                # Kontrola Exit_Reason
                if 'Exit_Reason' in headers:
                    for row in range(2, ws.max_row + 1):
                        exit_reason = ws.cell(row=row, column=headers['Exit_Reason']).value
                        if exit_reason:
                            if 'BUY_LIMIT' in str(exit_reason):
                                analysis['buy_limit_info'] = True
                            if 'SEQ_END_EOD_CLOSE' in str(exit_reason):
                                analysis['seq_end_detailed'] = True
                
                # Kontrola SL_Calc
                if 'SL_Calc' in headers:
                    for row in range(2, ws.max_row + 1):
                        sl_calc = ws.cell(row=row, column=headers['SL_Calc']).value
                        if sl_calc and str(sl_calc).strip():
                            analysis['sl_calc_present'] = True
                            if 'BarsBack' in str(sl_calc):
                                analysis['new_sl_method'] = True
                            break
        
        wb.close()
        
        print(f"📊 Analýza {test_name}:")
        print(f"   Obchody: {analysis['trades_count']}")
        print(f"   BUY_LIMIT info: {'✅' if analysis['buy_limit_info'] else '❌'}")
        print(f"   SL_Calc: {'✅' if analysis['sl_calc_present'] else '❌'}")
        print(f"   SEQ_END detailní: {'✅' if analysis['seq_end_detailed'] else '❌'}")
        print(f"   Nová SL metoda: {'✅' if analysis['new_sl_method'] else '❌'}")
        
        # Hodnocení úspěchu
        checks = [
            analysis['trades_count'] > 0,
            analysis['buy_limit_info'],
            analysis['sl_calc_present'],
            analysis['seq_end_detailed']
        ]
        
        if test_name != "Initial SL Method":
            checks.append(analysis['new_sl_method'])
        
        analysis['success'] = all(checks)
        
        return analysis
        
    except Exception as e:
        print(f"❌ Chyba při analýze {test_name}: {e}")
        return {
            'test_name': test_name,
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    success = test_new_features()
    
    if success:
        print("\n🎉 VŠECHNY NOVÉ FUNKCE FUNGUJÍ!")
        print("✅ Připraveno pro spuštění WebGUI serveru")
    else:
        print("\n❌ NĚKTERÉ FUNKCE NEFUNGUJÍ")
        print("🔧 Nutné další opravy")
