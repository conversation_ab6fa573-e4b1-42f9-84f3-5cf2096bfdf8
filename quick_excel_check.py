#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Rychlá kontrola Excel souboru
"""

import openpyxl
import os

def check_excel():
    excel_file = "TrendTaker_WebGUI_Tolerant_2025-08-19_2025-09-18.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel soubor nenalezen: {excel_file}")
        return
    
    print(f"✅ Excel soubor nalezen: {excel_file}")
    
    wb = openpyxl.load_workbook(excel_file)
    
    if 'Obchody' in wb.sheetnames:
        ws = wb['Obchody']
        print(f"💹 Obchody: {ws.max_row - 1} řádků")
        
        if ws.max_row > 1:
            # Headers
            headers = {}
            for col in range(1, ws.max_column + 1):
                header = ws.cell(row=1, column=col).value
                if header:
                    headers[header] = col
            
            print(f"📋 Sloupce: {list(headers.keys())}")
            
            # První obchod
            print(f"\n🔹 PRVNÍ OBCHOD:")
            for header, col in headers.items():
                value = ws.cell(row=2, column=col).value
                print(f"   {header}: {value}")
    
    wb.close()

if __name__ == "__main__":
    check_excel()
