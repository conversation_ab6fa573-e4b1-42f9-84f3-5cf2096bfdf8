#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jednoduchý test LONG-FLAT-LIMIT logiky
Importuje existující třídu z WebGUI a testuje konkrétní sek<PERSON>ci
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'python-webgui'))

# Import existuj<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON>
try:
    from importlib.util import spec_from_file_location, module_from_spec
    spec = spec_from_file_location("webgui", "python-webgui/Trend Taker Web GUI.py")
    webgui_module = module_from_spec(spec)
    spec.loader.exec_module(webgui_module)
    TrendTakerWebHandler = webgui_module.TrendTakerWebHandler
    print("✅ WebGUI modul úspěšně importován")
except Exception as e:
    print(f"❌ Chyba při importu WebGUI: {e}")
    sys.exit(1)

def test_august_september_simple():
    """Jednoduchý test sekvence 19.08 - 18.09.2025"""
    
    print("🧪 JEDNODUCHÝ TEST LONG-FLAT-LIMIT - 19.08-18.09.2025")
    print("=" * 80)
    
    # Vytvoření instance
    tester = TrendTakerWebHandler(None, None, None)
    
    # Parametry testu
    params = {
        'filename': 'XAUUSD_GMT+2_US-DST_D1.csv',
        'startDate': '2025-08-19',
        'endDate': '2025-09-18',
        'variant': 'Tolerant',
        'exitPolicy': 'Optimistic',
        'startEquity': 10000.0,
        'riskPct': 2.0,
        'rrr': 6.0,
        'overnightMode': 'LONG-FLAT-LIMIT',
        'buyLimitOffset': 5,
        'minBodyRatio': 60
    }
    
    print("🔍 TESTOVACÍ PARAMETRY:")
    for key, value in params.items():
        print(f"   {key}: {value}")
    print()
    
    try:
        # Spuštění testu
        print("🚀 Spouštím test...")
        results = tester.run_test(params)
        
        # Analýza výsledků
        print("\n📊 VÝSLEDKY TESTU:")
        print("=" * 80)
        
        # Výpis výsledků
        for line in results:
            if line.strip():
                print(line.strip())
        
        print("\n🎯 ANALÝZA DOKONČENA")
        
    except Exception as e:
        print(f"❌ Chyba při testu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_august_september_simple()
