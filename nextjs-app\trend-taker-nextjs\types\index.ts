export interface TestParams {
  testMode: 'SINGLE' | 'GRID';
  dataFile: string;
  dataRange?: 'CUSTOM' | 'FULL';
  startDate: string;
  endDate: string;
  variant?: 'Strict' | 'Tolerant';
  rrr?: number;
  exitPolicy?: 'Optimistic' | 'Conservative';
  overnightMode?: 'STANDARD' | 'LONG-FLAT-LIMIT';
  startEquity: number;
  riskPct: number;
  // v2.0 New Parameters
  buyLimitOffset?: number;
  minBodyRatio?: number; // Min. velikost těla sv<PERSON>ky (%) - 60% default
  longLmtSlMethod?: 'Initial' | 'BarsBack_Low' | 'BarsBack_50pct';
  barsBack?: number;
}

export interface TestResult {
  success: boolean;
  results?: string;
  error?: string;
  excel_file?: string;
}

export interface DatasetRange {
  success: boolean;
  start_date?: string;
  end_date?: string;
  total_records?: number;
  symbol?: string;
  filename?: string;
  file_path?: string;
  error?: string;
}

export interface Trade {
  Start_Date: string;
  End_Date: string;
  Direction: 'LONG' | 'SHORT';
  Lots: number;
  ATR10: number;
  Entry_Price: number;
  Exit_Price: number;
  Stop_Loss: number;
  Take_Profit: number;
  Exit_Reason: string;
  SL_Calc?: string; // v2.0 New: SL calculation history
  Spread_Cost: number;
  Swaps_USD: number;
  PnL_Bod: number;
  PnL_USD: number;
  Net_PnL: number;
  Equity_After: number;
  Segments_Count?: number; // v2.0 New: Number of segments in LONG-FLAT-LIMIT
  Segments_Detail?: any[]; // v2.0 New: Detailed segments for Excel export
  Sequence_ID?: string; // v2.0 New: Sequence identifier
}

export interface GridTestResult {
  Variant: string;
  ExitPolicy: string;
  OvernightMode: string;
  RRR: number;
  Trades: number;
  WinRate: number;
  ProfitFactor: number | 'inf';
  NetProfit_USD: number;
  MaxDrawdown_USD: number;
  GrossProfit_USD: number;
  GrossLoss_USD: number;
  AvgLots: number;
  AvgATR10: number;
  Swaps_USD: number;
  SpreadCost_USD: number;
  Equity_End_USD: number;
}

// v2.0 New Interfaces
export interface DailyAnalysis {
  Date: string;
  Open: number;
  High: number;
  Low: number;
  Close: number;
  Color: string;
  Is_Marubozu: boolean;
  Series_Order: number;
  Direction: string;
  ATR10_s1: number;
  Tolerated_Opposite?: boolean;
  LONG_LMT_SL_Method?: string;
  BarsBack?: number | string;
  Calculated_SL?: string;
}

export interface TradeSegment {
  Sequence_ID: string;
  Segment_Number: number;
  Start_Date: string;
  End_Date: string;
  Direction: 'LONG' | 'SHORT';
  Entry_Price: number;
  Exit_Price: number;
  Exit_Reason: string;
  PnL_Bod: number;
  PnL_USD: number;
  SL_Calc?: string;
}

export interface SequenceInfo {
  Sequence_ID: string;
  Start_Date: string;
  End_Date: string;
  Direction: string;
  Length: number;
  Marubozu_Count: number;
  Tolerated_Count: number;
}
